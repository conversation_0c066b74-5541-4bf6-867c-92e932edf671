{"version": 3, "sources": ["../../embla-carousel-autoplay/src/components/Options.ts", "../../embla-carousel-autoplay/src/components/utils.ts", "../../embla-carousel-autoplay/src/components/Autoplay.ts"], "sourcesContent": ["import { CreateOptionsType, EmblaCarouselType } from 'embla-carousel'\n\nexport type DelayOptionType =\n  | number\n  | ((scrollSnaps: number[], emblaApi: EmblaCarouselType) => number[])\n\nexport type RootNodeType =\n  | null\n  | ((emblaRoot: HTMLElement) => HTMLElement | null)\n\nexport type OptionsType = CreateOptionsType<{\n  delay: DelayOptionType\n  jump: boolean\n  playOnInit: boolean\n  stopOnFocusIn: boolean\n  stopOnInteraction: boolean\n  stopOnMouseEnter: boolean\n  stopOnLastSnap: boolean\n  rootNode: RootNodeType\n}>\n\nexport const defaultOptions: OptionsType = {\n  active: true,\n  breakpoints: {},\n  delay: 4000,\n  jump: false,\n  playOnInit: true,\n  stopOnFocusIn: true,\n  stopOnInteraction: true,\n  stopOnMouseEnter: false,\n  stopOnLastSnap: false,\n  rootNode: null\n}\n", "import { EmblaCarouselType } from 'embla-carousel/components/EmblaCarousel'\nimport { DelayOptionType, RootNodeType } from './Options'\n\nexport function normalizeDelay(\n  emblaApi: EmblaCarouselType,\n  delay: DelayOptionType\n): number[] {\n  const scrollSnaps = emblaApi.scrollSnapList()\n\n  if (typeof delay === 'number') {\n    return scrollSnaps.map(() => delay)\n  }\n  return delay(scrollSnaps, emblaApi)\n}\n\nexport function getAutoplayRootNode(\n  emblaApi: EmblaCarouselType,\n  rootNode: RootNodeType\n): HTMLElement {\n  const emblaRootNode = emblaApi.rootNode()\n  return (rootNode && rootNode(emblaRootNode)) || emblaRootNode\n}\n", "import { OptionsType, defaultOptions } from './Options'\nimport { getAutoplayRootNode, normalizeDelay } from './utils'\nimport {\n  CreatePluginType,\n  OptionsHandlerType,\n  EmblaCarouselType\n} from 'embla-carousel'\n\ndeclare module 'embla-carousel' {\n  interface EmblaPluginsType {\n    autoplay: AutoplayType\n  }\n\n  interface EmblaEventListType {\n    autoplayPlay: 'autoplay:play'\n    autoplayStop: 'autoplay:stop'\n    autoplaySelect: 'autoplay:select'\n    autoplayTimerSet: 'autoplay:timerset'\n    autoplayTimerStopped: 'autoplay:timerstopped'\n  }\n}\n\nexport type AutoplayType = CreatePluginType<\n  {\n    play: (jump?: boolean) => void\n    stop: () => void\n    reset: () => void\n    isPlaying: () => boolean\n    timeUntilNext: () => number | null\n  },\n  OptionsType\n>\n\nexport type AutoplayOptionsType = AutoplayType['options']\n\nfunction Autoplay(userOptions: AutoplayOptionsType = {}): AutoplayType {\n  let options: OptionsType\n  let emblaApi: EmblaCarouselType\n  let destroyed: boolean\n  let delay: ReturnType<EmblaCarouselType['scrollSnapList']>\n  let timerStartTime: null | number = null\n  let timerId = 0\n  let autoplayActive = false\n  let mouseIsOver = false\n  let playOnDocumentVisible = false\n  let jump = false\n\n  function init(\n    emblaApiInstance: EmblaCarouselType,\n    optionsHandler: OptionsHandlerType\n  ): void {\n    emblaApi = emblaApiInstance\n\n    const { mergeOptions, optionsAtMedia } = optionsHandler\n    const optionsBase = mergeOptions(defaultOptions, Autoplay.globalOptions)\n    const allOptions = mergeOptions(optionsBase, userOptions)\n    options = optionsAtMedia(allOptions)\n\n    if (emblaApi.scrollSnapList().length <= 1) return\n\n    jump = options.jump\n    destroyed = false\n    delay = normalizeDelay(emblaApi, options.delay)\n\n    const { eventStore, ownerDocument } = emblaApi.internalEngine()\n    const isDraggable = !!emblaApi.internalEngine().options.watchDrag\n    const root = getAutoplayRootNode(emblaApi, options.rootNode)\n\n    eventStore.add(ownerDocument, 'visibilitychange', visibilityChange)\n\n    if (isDraggable) {\n      emblaApi.on('pointerDown', pointerDown)\n    }\n\n    if (isDraggable && !options.stopOnInteraction) {\n      emblaApi.on('pointerUp', pointerUp)\n    }\n\n    if (options.stopOnMouseEnter) {\n      eventStore.add(root, 'mouseenter', mouseEnter)\n    }\n\n    if (options.stopOnMouseEnter && !options.stopOnInteraction) {\n      eventStore.add(root, 'mouseleave', mouseLeave)\n    }\n\n    if (options.stopOnFocusIn) {\n      emblaApi.on('slideFocusStart', stopAutoplay)\n    }\n\n    if (options.stopOnFocusIn && !options.stopOnInteraction) {\n      eventStore.add(emblaApi.containerNode(), 'focusout', startAutoplay)\n    }\n\n    if (options.playOnInit) startAutoplay()\n  }\n\n  function destroy(): void {\n    emblaApi\n      .off('pointerDown', pointerDown)\n      .off('pointerUp', pointerUp)\n      .off('slideFocusStart', stopAutoplay)\n\n    stopAutoplay()\n    destroyed = true\n    autoplayActive = false\n  }\n\n  function setTimer(): void {\n    const { ownerWindow } = emblaApi.internalEngine()\n    ownerWindow.clearTimeout(timerId)\n    timerId = ownerWindow.setTimeout(next, delay[emblaApi.selectedScrollSnap()])\n    timerStartTime = new Date().getTime()\n    emblaApi.emit('autoplay:timerset')\n  }\n\n  function clearTimer(): void {\n    const { ownerWindow } = emblaApi.internalEngine()\n    ownerWindow.clearTimeout(timerId)\n    timerId = 0\n    timerStartTime = null\n    emblaApi.emit('autoplay:timerstopped')\n  }\n\n  function startAutoplay(): void {\n    if (destroyed) return\n    if (documentIsHidden()) {\n      playOnDocumentVisible = true\n      return\n    }\n    if (!autoplayActive) emblaApi.emit('autoplay:play')\n\n    setTimer()\n    autoplayActive = true\n  }\n\n  function stopAutoplay(): void {\n    if (destroyed) return\n    if (autoplayActive) emblaApi.emit('autoplay:stop')\n\n    clearTimer()\n    autoplayActive = false\n  }\n\n  function visibilityChange(): void {\n    if (documentIsHidden()) {\n      playOnDocumentVisible = autoplayActive\n      return stopAutoplay()\n    }\n\n    if (playOnDocumentVisible) startAutoplay()\n  }\n\n  function documentIsHidden(): boolean {\n    const { ownerDocument } = emblaApi.internalEngine()\n    return ownerDocument.visibilityState === 'hidden'\n  }\n\n  function pointerDown(): void {\n    if (!mouseIsOver) stopAutoplay()\n  }\n\n  function pointerUp(): void {\n    if (!mouseIsOver) startAutoplay()\n  }\n\n  function mouseEnter(): void {\n    mouseIsOver = true\n    stopAutoplay()\n  }\n\n  function mouseLeave(): void {\n    mouseIsOver = false\n    startAutoplay()\n  }\n\n  function play(jumpOverride?: boolean): void {\n    if (typeof jumpOverride !== 'undefined') jump = jumpOverride\n    startAutoplay()\n  }\n\n  function stop(): void {\n    if (autoplayActive) stopAutoplay()\n  }\n\n  function reset(): void {\n    if (autoplayActive) startAutoplay()\n  }\n\n  function isPlaying(): boolean {\n    return autoplayActive\n  }\n\n  function next(): void {\n    const { index } = emblaApi.internalEngine()\n    const nextIndex = index.clone().add(1).get()\n    const lastIndex = emblaApi.scrollSnapList().length - 1\n    const kill = options.stopOnLastSnap && nextIndex === lastIndex\n\n    if (emblaApi.canScrollNext()) {\n      emblaApi.scrollNext(jump)\n    } else {\n      emblaApi.scrollTo(0, jump)\n    }\n\n    emblaApi.emit('autoplay:select')\n\n    if (kill) return stopAutoplay()\n    startAutoplay()\n  }\n\n  function timeUntilNext(): number | null {\n    if (!timerStartTime) return null\n    const currentDelay = delay[emblaApi.selectedScrollSnap()]\n    const timePastSinceStart = new Date().getTime() - timerStartTime\n    return currentDelay - timePastSinceStart\n  }\n\n  const self: AutoplayType = {\n    name: 'autoplay',\n    options: userOptions,\n    init,\n    destroy,\n    play,\n    stop,\n    reset,\n    isPlaying,\n    timeUntilNext\n  }\n  return self\n}\n\ndeclare namespace Autoplay {\n  let globalOptions: AutoplayOptionsType | undefined\n}\n\nAutoplay.globalOptions = undefined\n\nexport default Autoplay\n"], "mappings": ";;;AAqBO,IAAMA,iBAA8B;EACzCC,QAAQ;EACRC,aAAa,CAAA;EACbC,OAAO;EACPC,MAAM;EACNC,YAAY;EACZC,eAAe;EACfC,mBAAmB;EACnBC,kBAAkB;EAClBC,gBAAgB;EAChBC,UAAU;;AC5BI,SAAAC,eACdC,UACAT,OAAsB;AAEtB,QAAMU,cAAcD,SAASE,eAAc;AAE3C,MAAI,OAAOX,UAAU,UAAU;AAC7B,WAAOU,YAAYE,IAAI,MAAMZ,KAAK;EACpC;AACA,SAAOA,MAAMU,aAAaD,QAAQ;AACpC;AAEgB,SAAAI,oBACdJ,UACAF,UAAsB;AAEtB,QAAMO,gBAAgBL,SAASF,SAAQ;AACvC,SAAQA,YAAYA,SAASO,aAAa,KAAMA;AAClD;ACcA,SAASC,SAASC,cAAmC,CAAA,GAAE;AACrD,MAAIC;AACJ,MAAIR;AACJ,MAAIS;AACJ,MAAIlB;AACJ,MAAImB,iBAAgC;AACpC,MAAIC,UAAU;AACd,MAAIC,iBAAiB;AACrB,MAAIC,cAAc;AAClB,MAAIC,wBAAwB;AAC5B,MAAItB,OAAO;AAEX,WAASuB,KACPC,kBACAC,gBAAkC;AAElCjB,eAAWgB;AAEX,UAAM;MAAEE;MAAcC;IAAgB,IAAGF;AACzC,UAAMG,cAAcF,aAAa9B,gBAAgBkB,SAASe,aAAa;AACvE,UAAMC,aAAaJ,aAAaE,aAAab,WAAW;AACxDC,cAAUW,eAAeG,UAAU;AAEnC,QAAItB,SAASE,eAAc,EAAGqB,UAAU,EAAG;AAE3C/B,WAAOgB,QAAQhB;AACfiB,gBAAY;AACZlB,YAAQQ,eAAeC,UAAUQ,QAAQjB,KAAK;AAE9C,UAAM;MAAEiC;MAAYC;IAAa,IAAKzB,SAAS0B,eAAc;AAC7D,UAAMC,cAAc,CAAC,CAAC3B,SAAS0B,eAAc,EAAGlB,QAAQoB;AACxD,UAAMC,OAAOzB,oBAAoBJ,UAAUQ,QAAQV,QAAQ;AAE3D0B,eAAWM,IAAIL,eAAe,oBAAoBM,gBAAgB;AAElE,QAAIJ,aAAa;AACf3B,eAASgC,GAAG,eAAeC,WAAW;IACxC;AAEA,QAAIN,eAAe,CAACnB,QAAQb,mBAAmB;AAC7CK,eAASgC,GAAG,aAAaE,SAAS;IACpC;AAEA,QAAI1B,QAAQZ,kBAAkB;AAC5B4B,iBAAWM,IAAID,MAAM,cAAcM,UAAU;IAC/C;AAEA,QAAI3B,QAAQZ,oBAAoB,CAACY,QAAQb,mBAAmB;AAC1D6B,iBAAWM,IAAID,MAAM,cAAcO,UAAU;IAC/C;AAEA,QAAI5B,QAAQd,eAAe;AACzBM,eAASgC,GAAG,mBAAmBK,YAAY;IAC7C;AAEA,QAAI7B,QAAQd,iBAAiB,CAACc,QAAQb,mBAAmB;AACvD6B,iBAAWM,IAAI9B,SAASsC,cAAa,GAAI,YAAYC,aAAa;IACpE;AAEA,QAAI/B,QAAQf,WAAY8C,eAAa;EACvC;AAEA,WAASC,UAAO;AACdxC,aACGyC,IAAI,eAAeR,WAAW,EAC9BQ,IAAI,aAAaP,SAAS,EAC1BO,IAAI,mBAAmBJ,YAAY;AAEtCA,iBAAY;AACZ5B,gBAAY;AACZG,qBAAiB;EACnB;AAEA,WAAS8B,WAAQ;AACf,UAAM;MAAEC;IAAa,IAAG3C,SAAS0B,eAAc;AAC/CiB,gBAAYC,aAAajC,OAAO;AAChCA,cAAUgC,YAAYE,WAAWC,MAAMvD,MAAMS,SAAS+C,mBAAkB,CAAE,CAAC;AAC3ErC,sBAAiB,oBAAIsC,KAAI,GAAGC,QAAO;AACnCjD,aAASkD,KAAK,mBAAmB;EACnC;AAEA,WAASC,aAAU;AACjB,UAAM;MAAER;IAAa,IAAG3C,SAAS0B,eAAc;AAC/CiB,gBAAYC,aAAajC,OAAO;AAChCA,cAAU;AACVD,qBAAiB;AACjBV,aAASkD,KAAK,uBAAuB;EACvC;AAEA,WAASX,gBAAa;AACpB,QAAI9B,UAAW;AACf,QAAI2C,iBAAgB,GAAI;AACtBtC,8BAAwB;AACxB;IACF;AACA,QAAI,CAACF,eAAgBZ,UAASkD,KAAK,eAAe;AAElDR,aAAQ;AACR9B,qBAAiB;EACnB;AAEA,WAASyB,eAAY;AACnB,QAAI5B,UAAW;AACf,QAAIG,eAAgBZ,UAASkD,KAAK,eAAe;AAEjDC,eAAU;AACVvC,qBAAiB;EACnB;AAEA,WAASmB,mBAAgB;AACvB,QAAIqB,iBAAgB,GAAI;AACtBtC,8BAAwBF;AACxB,aAAOyB,aAAY;IACrB;AAEA,QAAIvB,sBAAuByB,eAAa;EAC1C;AAEA,WAASa,mBAAgB;AACvB,UAAM;MAAE3B;IAAe,IAAGzB,SAAS0B,eAAc;AACjD,WAAOD,cAAc4B,oBAAoB;EAC3C;AAEA,WAASpB,cAAW;AAClB,QAAI,CAACpB,YAAawB,cAAY;EAChC;AAEA,WAASH,YAAS;AAChB,QAAI,CAACrB,YAAa0B,eAAa;EACjC;AAEA,WAASJ,aAAU;AACjBtB,kBAAc;AACdwB,iBAAY;EACd;AAEA,WAASD,aAAU;AACjBvB,kBAAc;AACd0B,kBAAa;EACf;AAEA,WAASe,KAAKC,cAAsB;AAClC,QAAI,OAAOA,iBAAiB,YAAa/D,QAAO+D;AAChDhB,kBAAa;EACf;AAEA,WAASiB,OAAI;AACX,QAAI5C,eAAgByB,cAAY;EAClC;AAEA,WAASoB,QAAK;AACZ,QAAI7C,eAAgB2B,eAAa;EACnC;AAEA,WAASmB,YAAS;AAChB,WAAO9C;EACT;AAEA,WAASkC,OAAI;AACX,UAAM;MAAEa;IAAO,IAAG3D,SAAS0B,eAAc;AACzC,UAAMkC,YAAYD,MAAME,MAAK,EAAG/B,IAAI,CAAC,EAAEgC,IAAG;AAC1C,UAAMC,YAAY/D,SAASE,eAAc,EAAGqB,SAAS;AACrD,UAAMyC,OAAOxD,QAAQX,kBAAkB+D,cAAcG;AAErD,QAAI/D,SAASiE,cAAa,GAAI;AAC5BjE,eAASkE,WAAW1E,IAAI;IAC1B,OAAO;AACLQ,eAASmE,SAAS,GAAG3E,IAAI;IAC3B;AAEAQ,aAASkD,KAAK,iBAAiB;AAE/B,QAAIc,KAAM,QAAO3B,aAAY;AAC7BE,kBAAa;EACf;AAEA,WAAS6B,gBAAa;AACpB,QAAI,CAAC1D,eAAgB,QAAO;AAC5B,UAAM2D,eAAe9E,MAAMS,SAAS+C,mBAAkB,CAAE;AACxD,UAAMuB,sBAAqB,oBAAItB,KAAI,GAAGC,QAAO,IAAKvC;AAClD,WAAO2D,eAAeC;EACxB;AAEA,QAAMC,OAAqB;IACzBC,MAAM;IACNhE,SAASD;IACTQ;IACAyB;IACAc;IACAE;IACAC;IACAC;IACAU;;AAEF,SAAOG;AACT;AAMAjE,SAASe,gBAAgBoD;", "names": ["defaultOptions", "active", "breakpoints", "delay", "jump", "playOnInit", "stopOnFocusIn", "stopOnInteraction", "stopOnMouseEnter", "stopOnLastSnap", "rootNode", "normalizeDelay", "emblaApi", "scrollSnaps", "scrollSnapList", "map", "getAutoplayRootNode", "emblaRootNode", "Autoplay", "userOptions", "options", "destroyed", "timerStartTime", "timerId", "autoplayActive", "mouseIsOver", "playOnDocumentVisible", "init", "emblaApiInstance", "optionsHandler", "mergeOptions", "optionsAtMedia", "optionsBase", "globalOptions", "allOptions", "length", "eventStore", "ownerDocument", "internalEngine", "isDraggable", "watchDrag", "root", "add", "visibilityChange", "on", "pointerDown", "pointerUp", "mouseEnter", "mouseLeave", "stopAutoplay", "containerNode", "startAutoplay", "destroy", "off", "setTimer", "ownerWindow", "clearTimeout", "setTimeout", "next", "selectedScrollSnap", "Date", "getTime", "emit", "clearTimer", "documentIsHidden", "visibilityState", "play", "jumpOverride", "stop", "reset", "isPlaying", "index", "nextIndex", "clone", "get", "lastIndex", "kill", "canScrollNext", "scrollNext", "scrollTo", "timeUntilNext", "current<PERSON><PERSON><PERSON>", "timePastSinceStart", "self", "name", "undefined"]}