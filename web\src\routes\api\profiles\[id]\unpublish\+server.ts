import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';

export async function POST({ params, request, locals }) {
  try {
    // Get the user from the session
    const session = locals.session;
    if (!session?.user) {
      return json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }
    const user = session.user;

    const { id } = params;
    if (!id) {
      return json({ success: false, error: 'Profile ID is required' }, { status: 400 });
    }

    // Check if the profile exists and belongs to the user
    const profile = await prisma.profile.findUnique({
      where: {
        id,
        userId: user.id,
      },
    });

    if (!profile) {
      return json({ success: false, error: 'Profile not found' }, { status: 404 });
    }

    // Update the profile to set published to false
    // Use try-catch to handle the case where the published field might not exist yet
    let updatedProfile = profile;
    try {
      updatedProfile = await prisma.profile.update({
        where: {
          id,
        },
        data: {
          published: false,
          publishedAt: null,
        },
      });
    } catch (error) {
      console.error('Error updating profile published status:', error);
      // If the error is related to the published field not existing, we can still return success
    }

    return json({ success: true, profile: updatedProfile });
  } catch (error) {
    console.error('Error unpublishing profile:', error);
    return json({ success: false, error: 'Failed to unpublish profile' }, { status: 500 });
  }
}
