{"version": 3, "sources": ["../../svelte/src/internal/client/dom/blocks/svelte-head.js", "../../svelte/src/internal/client/dom/reconciler.js", "../../svelte/src/internal/client/dom/template.js", "../../svelte/src/utils.js", "../../svelte/src/internal/client/render.js", "../../svelte/src/internal/client/dom/legacy/event-modifiers.js", "../../svelte/src/legacy/legacy-client.js"], "sourcesContent": ["/** @import { TemplateNode } from '#client' */\nimport { hydrate_node, hydrating, set_hydrate_node, set_hydrating } from '../hydration.js';\nimport { create_text, get_first_child, get_next_sibling } from '../operations.js';\nimport { block } from '../../reactivity/effects.js';\nimport { HEAD_EFFECT } from '../../constants.js';\nimport { HYDRATION_START } from '../../../../constants.js';\n\n/**\n * @type {Node | undefined}\n */\nlet head_anchor;\n\nexport function reset_head_anchor() {\n\thead_anchor = undefined;\n}\n\n/**\n * @param {(anchor: Node) => void} render_fn\n * @returns {void}\n */\nexport function head(render_fn) {\n\t// The head function may be called after the first hydration pass and ssr comment nodes may still be present,\n\t// therefore we need to skip that when we detect that we're not in hydration mode.\n\tlet previous_hydrate_node = null;\n\tlet was_hydrating = hydrating;\n\n\t/** @type {Comment | Text} */\n\tvar anchor;\n\n\tif (hydrating) {\n\t\tprevious_hydrate_node = hydrate_node;\n\n\t\t// There might be multiple head blocks in our app, so we need to account for each one needing independent hydration.\n\t\tif (head_anchor === undefined) {\n\t\t\thead_anchor = /** @type {TemplateNode} */ (get_first_child(document.head));\n\t\t}\n\n\t\twhile (\n\t\t\thead_anchor !== null &&\n\t\t\t(head_anchor.nodeType !== 8 || /** @type {Comment} */ (head_anchor).data !== HYDRATION_START)\n\t\t) {\n\t\t\thead_anchor = /** @type {TemplateNode} */ (get_next_sibling(head_anchor));\n\t\t}\n\n\t\t// If we can't find an opening hydration marker, skip hydration (this can happen\n\t\t// if a framework rendered body but not head content)\n\t\tif (head_anchor === null) {\n\t\t\tset_hydrating(false);\n\t\t} else {\n\t\t\thead_anchor = set_hydrate_node(/** @type {TemplateNode} */ (get_next_sibling(head_anchor)));\n\t\t}\n\t}\n\n\tif (!hydrating) {\n\t\tanchor = document.head.appendChild(create_text());\n\t}\n\n\ttry {\n\t\tblock(() => render_fn(anchor), HEAD_EFFECT);\n\t} finally {\n\t\tif (was_hydrating) {\n\t\t\tset_hydrating(true);\n\t\t\thead_anchor = hydrate_node; // so that next head block starts from the correct node\n\t\t\tset_hydrate_node(/** @type {TemplateNode} */ (previous_hydrate_node));\n\t\t}\n\t}\n}\n", "/** @param {string} html */\nexport function create_fragment_from_html(html) {\n\tvar elem = document.createElement('template');\n\telem.innerHTML = html;\n\treturn elem.content;\n}\n", "/** @import { Effect, TemplateNode } from '#client' */\nimport { hydrate_next, hydrate_node, hydrating, set_hydrate_node } from './hydration.js';\nimport { create_text, get_first_child, is_firefox } from './operations.js';\nimport { create_fragment_from_html } from './reconciler.js';\nimport { active_effect } from '../runtime.js';\nimport { TEMPLATE_FRAGMENT, TEMPLATE_USE_IMPORT_NODE } from '../../../constants.js';\n\n/**\n * @param {TemplateNode} start\n * @param {TemplateNode | null} end\n */\nexport function assign_nodes(start, end) {\n\tvar effect = /** @type {Effect} */ (active_effect);\n\tif (effect.nodes_start === null) {\n\t\teffect.nodes_start = start;\n\t\teffect.nodes_end = end;\n\t}\n}\n\n/**\n * @param {string} content\n * @param {number} flags\n * @returns {() => Node | Node[]}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function template(content, flags) {\n\tvar is_fragment = (flags & TEMPLATE_FRAGMENT) !== 0;\n\tvar use_import_node = (flags & TEMPLATE_USE_IMPORT_NODE) !== 0;\n\n\t/** @type {Node} */\n\tvar node;\n\n\t/**\n\t * Whether or not the first item is a text/element node. If not, we need to\n\t * create an additional comment node to act as `effect.nodes.start`\n\t */\n\tvar has_start = !content.startsWith('<!>');\n\n\treturn () => {\n\t\tif (hydrating) {\n\t\t\tassign_nodes(hydrate_node, null);\n\t\t\treturn hydrate_node;\n\t\t}\n\n\t\tif (node === undefined) {\n\t\t\tnode = create_fragment_from_html(has_start ? content : '<!>' + content);\n\t\t\tif (!is_fragment) node = /** @type {Node} */ (get_first_child(node));\n\t\t}\n\n\t\tvar clone = /** @type {TemplateNode} */ (\n\t\t\tuse_import_node || is_firefox ? document.importNode(node, true) : node.cloneNode(true)\n\t\t);\n\n\t\tif (is_fragment) {\n\t\t\tvar start = /** @type {TemplateNode} */ (get_first_child(clone));\n\t\t\tvar end = /** @type {TemplateNode} */ (clone.lastChild);\n\n\t\t\tassign_nodes(start, end);\n\t\t} else {\n\t\t\tassign_nodes(clone, clone);\n\t\t}\n\n\t\treturn clone;\n\t};\n}\n\n/**\n * @param {string} content\n * @param {number} flags\n * @returns {() => Node | Node[]}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function template_with_script(content, flags) {\n\tvar fn = template(content, flags);\n\treturn () => run_scripts(/** @type {Element | DocumentFragment} */ (fn()));\n}\n\n/**\n * @param {string} content\n * @param {number} flags\n * @param {'svg' | 'math'} ns\n * @returns {() => Node | Node[]}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function ns_template(content, flags, ns = 'svg') {\n\t/**\n\t * Whether or not the first item is a text/element node. If not, we need to\n\t * create an additional comment node to act as `effect.nodes.start`\n\t */\n\tvar has_start = !content.startsWith('<!>');\n\n\tvar is_fragment = (flags & TEMPLATE_FRAGMENT) !== 0;\n\tvar wrapped = `<${ns}>${has_start ? content : '<!>' + content}</${ns}>`;\n\n\t/** @type {Element | DocumentFragment} */\n\tvar node;\n\n\treturn () => {\n\t\tif (hydrating) {\n\t\t\tassign_nodes(hydrate_node, null);\n\t\t\treturn hydrate_node;\n\t\t}\n\n\t\tif (!node) {\n\t\t\tvar fragment = /** @type {DocumentFragment} */ (create_fragment_from_html(wrapped));\n\t\t\tvar root = /** @type {Element} */ (get_first_child(fragment));\n\n\t\t\tif (is_fragment) {\n\t\t\t\tnode = document.createDocumentFragment();\n\t\t\t\twhile (get_first_child(root)) {\n\t\t\t\t\tnode.appendChild(/** @type {Node} */ (get_first_child(root)));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tnode = /** @type {Element} */ (get_first_child(root));\n\t\t\t}\n\t\t}\n\n\t\tvar clone = /** @type {TemplateNode} */ (node.cloneNode(true));\n\n\t\tif (is_fragment) {\n\t\t\tvar start = /** @type {TemplateNode} */ (get_first_child(clone));\n\t\t\tvar end = /** @type {TemplateNode} */ (clone.lastChild);\n\n\t\t\tassign_nodes(start, end);\n\t\t} else {\n\t\t\tassign_nodes(clone, clone);\n\t\t}\n\n\t\treturn clone;\n\t};\n}\n\n/**\n * @param {string} content\n * @param {number} flags\n * @returns {() => Node | Node[]}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function svg_template_with_script(content, flags) {\n\tvar fn = ns_template(content, flags);\n\treturn () => run_scripts(/** @type {Element | DocumentFragment} */ (fn()));\n}\n\n/**\n * @param {string} content\n * @param {number} flags\n * @returns {() => Node | Node[]}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function mathml_template(content, flags) {\n\treturn ns_template(content, flags, 'math');\n}\n\n/**\n * Creating a document fragment from HTML that contains script tags will not execute\n * the scripts. We need to replace the script tags with new ones so that they are executed.\n * @param {Element | DocumentFragment} node\n * @returns {Node | Node[]}\n */\nfunction run_scripts(node) {\n\t// scripts were SSR'd, in which case they will run\n\tif (hydrating) return node;\n\n\tconst is_fragment = node.nodeType === 11;\n\tconst scripts =\n\t\t/** @type {HTMLElement} */ (node).tagName === 'SCRIPT'\n\t\t\t? [/** @type {HTMLScriptElement} */ (node)]\n\t\t\t: node.querySelectorAll('script');\n\tconst effect = /** @type {Effect} */ (active_effect);\n\n\tfor (const script of scripts) {\n\t\tconst clone = document.createElement('script');\n\t\tfor (var attribute of script.attributes) {\n\t\t\tclone.setAttribute(attribute.name, attribute.value);\n\t\t}\n\n\t\tclone.textContent = script.textContent;\n\n\t\t// The script has changed - if it's at the edges, the effect now points at dead nodes\n\t\tif (is_fragment ? node.firstChild === script : node === script) {\n\t\t\teffect.nodes_start = clone;\n\t\t}\n\t\tif (is_fragment ? node.lastChild === script : node === script) {\n\t\t\teffect.nodes_end = clone;\n\t\t}\n\n\t\tscript.replaceWith(clone);\n\t}\n\treturn node;\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @param {any} value\n */\nexport function text(value = '') {\n\tif (!hydrating) {\n\t\tvar t = create_text(value + '');\n\t\tassign_nodes(t, t);\n\t\treturn t;\n\t}\n\n\tvar node = hydrate_node;\n\n\tif (node.nodeType !== 3) {\n\t\t// if an {expression} is empty during SSR, we need to insert an empty text node\n\t\tnode.before((node = create_text()));\n\t\tset_hydrate_node(node);\n\t}\n\n\tassign_nodes(node, node);\n\treturn node;\n}\n\nexport function comment() {\n\t// we're not delegating to `template` here for performance reasons\n\tif (hydrating) {\n\t\tassign_nodes(hydrate_node, null);\n\t\treturn hydrate_node;\n\t}\n\n\tvar frag = document.createDocumentFragment();\n\tvar start = document.createComment('');\n\tvar anchor = create_text();\n\tfrag.append(start, anchor);\n\n\tassign_nodes(start, anchor);\n\n\treturn frag;\n}\n\n/**\n * Assign the created (or in hydration mode, traversed) dom elements to the current block\n * and insert the elements into the dom (in client mode).\n * @param {Text | Comment | Element} anchor\n * @param {DocumentFragment | Element} dom\n */\nexport function append(anchor, dom) {\n\tif (hydrating) {\n\t\t/** @type {Effect} */ (active_effect).nodes_end = hydrate_node;\n\t\thydrate_next();\n\t\treturn;\n\t}\n\n\tif (anchor === null) {\n\t\t// edge case — void `<svelte:element>` with content\n\t\treturn;\n\t}\n\n\tanchor.before(/** @type {Node} */ (dom));\n}\n\n/**\n * Create (or hydrate) an unique UID for the component instance.\n */\nexport function props_id() {\n\tif (\n\t\thydrating &&\n\t\thydrate_node &&\n\t\thydrate_node.nodeType === 8 &&\n\t\thydrate_node.textContent?.startsWith(`#`)\n\t) {\n\t\tconst id = hydrate_node.textContent.substring(1);\n\t\thydrate_next();\n\t\treturn id;\n\t}\n\n\t// @ts-expect-error This way we ensure the id is unique even across Svelte runtimes\n\t(window.__svelte ??= {}).uid ??= 1;\n\n\t// @ts-expect-error\n\treturn `c${window.__svelte.uid++}`;\n}\n", "const regex_return_characters = /\\r/g;\n\n/**\n * @param {string} str\n * @returns {string}\n */\nexport function hash(str) {\n\tstr = str.replace(regex_return_characters, '');\n\tlet hash = 5381;\n\tlet i = str.length;\n\n\twhile (i--) hash = ((hash << 5) - hash) ^ str.charCodeAt(i);\n\treturn (hash >>> 0).toString(36);\n}\n\nconst VOID_ELEMENT_NAMES = [\n\t'area',\n\t'base',\n\t'br',\n\t'col',\n\t'command',\n\t'embed',\n\t'hr',\n\t'img',\n\t'input',\n\t'keygen',\n\t'link',\n\t'meta',\n\t'param',\n\t'source',\n\t'track',\n\t'wbr'\n];\n\n/**\n * Returns `true` if `name` is of a void element\n * @param {string} name\n */\nexport function is_void(name) {\n\treturn VOID_ELEMENT_NAMES.includes(name) || name.toLowerCase() === '!doctype';\n}\n\nconst RESERVED_WORDS = [\n\t'arguments',\n\t'await',\n\t'break',\n\t'case',\n\t'catch',\n\t'class',\n\t'const',\n\t'continue',\n\t'debugger',\n\t'default',\n\t'delete',\n\t'do',\n\t'else',\n\t'enum',\n\t'eval',\n\t'export',\n\t'extends',\n\t'false',\n\t'finally',\n\t'for',\n\t'function',\n\t'if',\n\t'implements',\n\t'import',\n\t'in',\n\t'instanceof',\n\t'interface',\n\t'let',\n\t'new',\n\t'null',\n\t'package',\n\t'private',\n\t'protected',\n\t'public',\n\t'return',\n\t'static',\n\t'super',\n\t'switch',\n\t'this',\n\t'throw',\n\t'true',\n\t'try',\n\t'typeof',\n\t'var',\n\t'void',\n\t'while',\n\t'with',\n\t'yield'\n];\n\n/**\n * Returns `true` if `word` is a reserved JavaScript keyword\n * @param {string} word\n */\nexport function is_reserved(word) {\n\treturn RESERVED_WORDS.includes(word);\n}\n\n/**\n * @param {string} name\n */\nexport function is_capture_event(name) {\n\treturn name.endsWith('capture') && name !== 'gotpointercapture' && name !== 'lostpointercapture';\n}\n\n/** List of Element events that will be delegated */\nconst DELEGATED_EVENTS = [\n\t'beforeinput',\n\t'click',\n\t'change',\n\t'dblclick',\n\t'contextmenu',\n\t'focusin',\n\t'focusout',\n\t'input',\n\t'keydown',\n\t'keyup',\n\t'mousedown',\n\t'mousemove',\n\t'mouseout',\n\t'mouseover',\n\t'mouseup',\n\t'pointerdown',\n\t'pointermove',\n\t'pointerout',\n\t'pointerover',\n\t'pointerup',\n\t'touchend',\n\t'touchmove',\n\t'touchstart'\n];\n\n/**\n * Returns `true` if `event_name` is a delegated event\n * @param {string} event_name\n */\nexport function is_delegated(event_name) {\n\treturn DELEGATED_EVENTS.includes(event_name);\n}\n\n/**\n * Attributes that are boolean, i.e. they are present or not present.\n */\nconst DOM_BOOLEAN_ATTRIBUTES = [\n\t'allowfullscreen',\n\t'async',\n\t'autofocus',\n\t'autoplay',\n\t'checked',\n\t'controls',\n\t'default',\n\t'disabled',\n\t'formnovalidate',\n\t'hidden',\n\t'indeterminate',\n\t'inert',\n\t'ismap',\n\t'loop',\n\t'multiple',\n\t'muted',\n\t'nomodule',\n\t'novalidate',\n\t'open',\n\t'playsinline',\n\t'readonly',\n\t'required',\n\t'reversed',\n\t'seamless',\n\t'selected',\n\t'webkitdirectory',\n\t'defer',\n\t'disablepictureinpicture',\n\t'disableremoteplayback'\n];\n\n/**\n * Returns `true` if `name` is a boolean attribute\n * @param {string} name\n */\nexport function is_boolean_attribute(name) {\n\treturn DOM_BOOLEAN_ATTRIBUTES.includes(name);\n}\n\n/**\n * @type {Record<string, string>}\n * List of attribute names that should be aliased to their property names\n * because they behave differently between setting them as an attribute and\n * setting them as a property.\n */\nconst ATTRIBUTE_ALIASES = {\n\t// no `class: 'className'` because we handle that separately\n\tformnovalidate: 'formNoValidate',\n\tismap: 'isMap',\n\tnomodule: 'noModule',\n\tplaysinline: 'playsInline',\n\treadonly: 'readOnly',\n\tdefaultvalue: 'defaultValue',\n\tdefaultchecked: 'defaultChecked',\n\tsrcobject: 'srcObject',\n\tnovalidate: 'noValidate',\n\tallowfullscreen: 'allowFullscreen',\n\tdisablepictureinpicture: 'disablePictureInPicture',\n\tdisableremoteplayback: 'disableRemotePlayback'\n};\n\n/**\n * @param {string} name\n */\nexport function normalize_attribute(name) {\n\tname = name.toLowerCase();\n\treturn ATTRIBUTE_ALIASES[name] ?? name;\n}\n\nconst DOM_PROPERTIES = [\n\t...DOM_BOOLEAN_ATTRIBUTES,\n\t'formNoValidate',\n\t'isMap',\n\t'noModule',\n\t'playsInline',\n\t'readOnly',\n\t'value',\n\t'volume',\n\t'defaultValue',\n\t'defaultChecked',\n\t'srcObject',\n\t'noValidate',\n\t'allowFullscreen',\n\t'disablePictureInPicture',\n\t'disableRemotePlayback'\n];\n\n/**\n * @param {string} name\n */\nexport function is_dom_property(name) {\n\treturn DOM_PROPERTIES.includes(name);\n}\n\nconst NON_STATIC_PROPERTIES = ['autofocus', 'muted', 'defaultValue', 'defaultChecked'];\n\n/**\n * Returns `true` if the given attribute cannot be set through the template\n * string, i.e. needs some kind of JavaScript handling to work.\n * @param {string} name\n */\nexport function cannot_be_set_statically(name) {\n\treturn NON_STATIC_PROPERTIES.includes(name);\n}\n\n/**\n * Subset of delegated events which should be passive by default.\n * These two are already passive via browser defaults on window, document and body.\n * But since\n * - we're delegating them\n * - they happen often\n * - they apply to mobile which is generally less performant\n * we're marking them as passive by default for other elements, too.\n */\nconst PASSIVE_EVENTS = ['touchstart', 'touchmove'];\n\n/**\n * Returns `true` if `name` is a passive event\n * @param {string} name\n */\nexport function is_passive_event(name) {\n\treturn PASSIVE_EVENTS.includes(name);\n}\n\nconst CONTENT_EDITABLE_BINDINGS = ['textContent', 'innerHTML', 'innerText'];\n\n/** @param {string} name */\nexport function is_content_editable_binding(name) {\n\treturn CONTENT_EDITABLE_BINDINGS.includes(name);\n}\n\nconst LOAD_ERROR_ELEMENTS = [\n\t'body',\n\t'embed',\n\t'iframe',\n\t'img',\n\t'link',\n\t'object',\n\t'script',\n\t'style',\n\t'track'\n];\n\n/**\n * Returns `true` if the element emits `load` and `error` events\n * @param {string} name\n */\nexport function is_load_error_element(name) {\n\treturn LOAD_ERROR_ELEMENTS.includes(name);\n}\n\nconst SVG_ELEMENTS = [\n\t'altGlyph',\n\t'altGlyphDef',\n\t'altGlyphItem',\n\t'animate',\n\t'animateColor',\n\t'animateMotion',\n\t'animateTransform',\n\t'circle',\n\t'clipPath',\n\t'color-profile',\n\t'cursor',\n\t'defs',\n\t'desc',\n\t'discard',\n\t'ellipse',\n\t'feBlend',\n\t'feColorMatrix',\n\t'feComponentTransfer',\n\t'feComposite',\n\t'feConvolveMatrix',\n\t'feDiffuseLighting',\n\t'feDisplacementMap',\n\t'feDistantLight',\n\t'feDropShadow',\n\t'feFlood',\n\t'feFuncA',\n\t'feFuncB',\n\t'feFuncG',\n\t'feFuncR',\n\t'feGaussianBlur',\n\t'feImage',\n\t'feMerge',\n\t'feMergeNode',\n\t'feMorphology',\n\t'feOffset',\n\t'fePointLight',\n\t'feSpecularLighting',\n\t'feSpotLight',\n\t'feTile',\n\t'feTurbulence',\n\t'filter',\n\t'font',\n\t'font-face',\n\t'font-face-format',\n\t'font-face-name',\n\t'font-face-src',\n\t'font-face-uri',\n\t'foreignObject',\n\t'g',\n\t'glyph',\n\t'glyphRef',\n\t'hatch',\n\t'hatchpath',\n\t'hkern',\n\t'image',\n\t'line',\n\t'linearGradient',\n\t'marker',\n\t'mask',\n\t'mesh',\n\t'meshgradient',\n\t'meshpatch',\n\t'meshrow',\n\t'metadata',\n\t'missing-glyph',\n\t'mpath',\n\t'path',\n\t'pattern',\n\t'polygon',\n\t'polyline',\n\t'radialGradient',\n\t'rect',\n\t'set',\n\t'solidcolor',\n\t'stop',\n\t'svg',\n\t'switch',\n\t'symbol',\n\t'text',\n\t'textPath',\n\t'tref',\n\t'tspan',\n\t'unknown',\n\t'use',\n\t'view',\n\t'vkern'\n];\n\n/** @param {string} name */\nexport function is_svg(name) {\n\treturn SVG_ELEMENTS.includes(name);\n}\n\nconst MATHML_ELEMENTS = [\n\t'annotation',\n\t'annotation-xml',\n\t'maction',\n\t'math',\n\t'merror',\n\t'mfrac',\n\t'mi',\n\t'mmultiscripts',\n\t'mn',\n\t'mo',\n\t'mover',\n\t'mpadded',\n\t'mphantom',\n\t'mprescripts',\n\t'mroot',\n\t'mrow',\n\t'ms',\n\t'mspace',\n\t'msqrt',\n\t'mstyle',\n\t'msub',\n\t'msubsup',\n\t'msup',\n\t'mtable',\n\t'mtd',\n\t'mtext',\n\t'mtr',\n\t'munder',\n\t'munderover',\n\t'semantics'\n];\n\n/** @param {string} name */\nexport function is_mathml(name) {\n\treturn MATHML_ELEMENTS.includes(name);\n}\n\nconst RUNES = /** @type {const} */ ([\n\t'$state',\n\t'$state.raw',\n\t'$state.snapshot',\n\t'$props',\n\t'$props.id',\n\t'$bindable',\n\t'$derived',\n\t'$derived.by',\n\t'$effect',\n\t'$effect.pre',\n\t'$effect.tracking',\n\t'$effect.root',\n\t'$inspect',\n\t'$inspect().with',\n\t'$inspect.trace',\n\t'$host'\n]);\n\n/**\n * @param {string} name\n * @returns {name is RUNES[number]}\n */\nexport function is_rune(name) {\n\treturn RUNES.includes(/** @type {RUNES[number]} */ (name));\n}\n\n/** List of elements that require raw contents and should not have SSR comments put in them */\nconst RAW_TEXT_ELEMENTS = /** @type {const} */ (['textarea', 'script', 'style', 'title']);\n\n/** @param {string} name */\nexport function is_raw_text_element(name) {\n\treturn RAW_TEXT_ELEMENTS.includes(/** @type {RAW_TEXT_ELEMENTS[number]} */ (name));\n}\n\n/**\n * Prevent devtools trying to make `location` a clickable link by inserting a zero-width space\n * @template {string | undefined} T\n * @param {T} location\n * @returns {T};\n */\nexport function sanitize_location(location) {\n\treturn /** @type {T} */ (location?.replace(/\\//g, '/\\u200b'));\n}\n", "/** @import { ComponentContext, Effect, TemplateNode } from '#client' */\n/** @import { Component, ComponentType, SvelteComponent, MountOptions } from '../../index.js' */\nimport { DEV } from 'esm-env';\nimport {\n\tclear_text_content,\n\tcreate_text,\n\tget_first_child,\n\tget_next_sibling,\n\tinit_operations\n} from './dom/operations.js';\nimport { HYDRATION_END, HYDRATION_ERROR, HYDRATION_START } from '../../constants.js';\nimport { active_effect } from './runtime.js';\nimport { push, pop, component_context } from './context.js';\nimport { component_root, branch } from './reactivity/effects.js';\nimport {\n\thydrate_next,\n\thydrate_node,\n\thydrating,\n\tset_hydrate_node,\n\tset_hydrating\n} from './dom/hydration.js';\nimport { array_from } from '../shared/utils.js';\nimport {\n\tall_registered_events,\n\thandle_event_propagation,\n\troot_event_handles\n} from './dom/elements/events.js';\nimport { reset_head_anchor } from './dom/blocks/svelte-head.js';\nimport * as w from './warnings.js';\nimport * as e from './errors.js';\nimport { assign_nodes } from './dom/template.js';\nimport { is_passive_event } from '../../utils.js';\n\n/**\n * This is normally true — block effects should run their intro transitions —\n * but is false during hydration (unless `options.intro` is `true`) and\n * when creating the children of a `<svelte:element>` that just changed tag\n */\nexport let should_intro = true;\n\n/** @param {boolean} value */\nexport function set_should_intro(value) {\n\tshould_intro = value;\n}\n\n/**\n * @param {Element} text\n * @param {string} value\n * @returns {void}\n */\nexport function set_text(text, value) {\n\t// For objects, we apply string coercion (which might make things like $state array references in the template reactive) before diffing\n\tvar str = value == null ? '' : typeof value === 'object' ? value + '' : value;\n\t// @ts-expect-error\n\tif (str !== (text.__t ??= text.nodeValue)) {\n\t\t// @ts-expect-error\n\t\ttext.__t = str;\n\t\ttext.nodeValue = str + '';\n\t}\n}\n\n/**\n * Mounts a component to the given target and returns the exports and potentially the props (if compiled with `accessors: true`) of the component.\n * Transitions will play during the initial render unless the `intro` option is set to `false`.\n *\n * @template {Record<string, any>} Props\n * @template {Record<string, any>} Exports\n * @param {ComponentType<SvelteComponent<Props>> | Component<Props, Exports, any>} component\n * @param {MountOptions<Props>} options\n * @returns {Exports}\n */\nexport function mount(component, options) {\n\treturn _mount(component, options);\n}\n\n/**\n * Hydrates a component on the given target and returns the exports and potentially the props (if compiled with `accessors: true`) of the component\n *\n * @template {Record<string, any>} Props\n * @template {Record<string, any>} Exports\n * @param {ComponentType<SvelteComponent<Props>> | Component<Props, Exports, any>} component\n * @param {{} extends Props ? {\n * \t\ttarget: Document | Element | ShadowRoot;\n * \t\tprops?: Props;\n * \t\tevents?: Record<string, (e: any) => any>;\n *  \tcontext?: Map<any, any>;\n * \t\tintro?: boolean;\n * \t\trecover?: boolean;\n * \t} : {\n * \t\ttarget: Document | Element | ShadowRoot;\n * \t\tprops: Props;\n * \t\tevents?: Record<string, (e: any) => any>;\n *  \tcontext?: Map<any, any>;\n * \t\tintro?: boolean;\n * \t\trecover?: boolean;\n * \t}} options\n * @returns {Exports}\n */\nexport function hydrate(component, options) {\n\tinit_operations();\n\toptions.intro = options.intro ?? false;\n\tconst target = options.target;\n\tconst was_hydrating = hydrating;\n\tconst previous_hydrate_node = hydrate_node;\n\n\ttry {\n\t\tvar anchor = /** @type {TemplateNode} */ (get_first_child(target));\n\t\twhile (\n\t\t\tanchor &&\n\t\t\t(anchor.nodeType !== 8 || /** @type {Comment} */ (anchor).data !== HYDRATION_START)\n\t\t) {\n\t\t\tanchor = /** @type {TemplateNode} */ (get_next_sibling(anchor));\n\t\t}\n\n\t\tif (!anchor) {\n\t\t\tthrow HYDRATION_ERROR;\n\t\t}\n\n\t\tset_hydrating(true);\n\t\tset_hydrate_node(/** @type {Comment} */ (anchor));\n\t\thydrate_next();\n\n\t\tconst instance = _mount(component, { ...options, anchor });\n\n\t\tif (\n\t\t\thydrate_node === null ||\n\t\t\thydrate_node.nodeType !== 8 ||\n\t\t\t/** @type {Comment} */ (hydrate_node).data !== HYDRATION_END\n\t\t) {\n\t\t\tw.hydration_mismatch();\n\t\t\tthrow HYDRATION_ERROR;\n\t\t}\n\n\t\tset_hydrating(false);\n\n\t\treturn /**  @type {Exports} */ (instance);\n\t} catch (error) {\n\t\tif (error === HYDRATION_ERROR) {\n\t\t\tif (options.recover === false) {\n\t\t\t\te.hydration_failed();\n\t\t\t}\n\n\t\t\t// If an error occured above, the operations might not yet have been initialised.\n\t\t\tinit_operations();\n\t\t\tclear_text_content(target);\n\n\t\t\tset_hydrating(false);\n\t\t\treturn mount(component, options);\n\t\t}\n\n\t\tthrow error;\n\t} finally {\n\t\tset_hydrating(was_hydrating);\n\t\tset_hydrate_node(previous_hydrate_node);\n\t\treset_head_anchor();\n\t}\n}\n\n/** @type {Map<string, number>} */\nconst document_listeners = new Map();\n\n/**\n * @template {Record<string, any>} Exports\n * @param {ComponentType<SvelteComponent<any>> | Component<any>} Component\n * @param {MountOptions} options\n * @returns {Exports}\n */\nfunction _mount(Component, { target, anchor, props = {}, events, context, intro = true }) {\n\tinit_operations();\n\n\tvar registered_events = new Set();\n\n\t/** @param {Array<string>} events */\n\tvar event_handle = (events) => {\n\t\tfor (var i = 0; i < events.length; i++) {\n\t\t\tvar event_name = events[i];\n\n\t\t\tif (registered_events.has(event_name)) continue;\n\t\t\tregistered_events.add(event_name);\n\n\t\t\tvar passive = is_passive_event(event_name);\n\n\t\t\t// Add the event listener to both the container and the document.\n\t\t\t// The container listener ensures we catch events from within in case\n\t\t\t// the outer content stops propagation of the event.\n\t\t\ttarget.addEventListener(event_name, handle_event_propagation, { passive });\n\n\t\t\tvar n = document_listeners.get(event_name);\n\n\t\t\tif (n === undefined) {\n\t\t\t\t// The document listener ensures we catch events that originate from elements that were\n\t\t\t\t// manually moved outside of the container (e.g. via manual portals).\n\t\t\t\tdocument.addEventListener(event_name, handle_event_propagation, { passive });\n\t\t\t\tdocument_listeners.set(event_name, 1);\n\t\t\t} else {\n\t\t\t\tdocument_listeners.set(event_name, n + 1);\n\t\t\t}\n\t\t}\n\t};\n\n\tevent_handle(array_from(all_registered_events));\n\troot_event_handles.add(event_handle);\n\n\t/** @type {Exports} */\n\t// @ts-expect-error will be defined because the render effect runs synchronously\n\tvar component = undefined;\n\n\tvar unmount = component_root(() => {\n\t\tvar anchor_node = anchor ?? target.appendChild(create_text());\n\n\t\tbranch(() => {\n\t\t\tif (context) {\n\t\t\t\tpush({});\n\t\t\t\tvar ctx = /** @type {ComponentContext} */ (component_context);\n\t\t\t\tctx.c = context;\n\t\t\t}\n\n\t\t\tif (events) {\n\t\t\t\t// We can't spread the object or else we'd lose the state proxy stuff, if it is one\n\t\t\t\t/** @type {any} */ (props).$$events = events;\n\t\t\t}\n\n\t\t\tif (hydrating) {\n\t\t\t\tassign_nodes(/** @type {TemplateNode} */ (anchor_node), null);\n\t\t\t}\n\n\t\t\tshould_intro = intro;\n\t\t\t// @ts-expect-error the public typings are not what the actual function looks like\n\t\t\tcomponent = Component(anchor_node, props) || {};\n\t\t\tshould_intro = true;\n\n\t\t\tif (hydrating) {\n\t\t\t\t/** @type {Effect} */ (active_effect).nodes_end = hydrate_node;\n\t\t\t}\n\n\t\t\tif (context) {\n\t\t\t\tpop();\n\t\t\t}\n\t\t});\n\n\t\treturn () => {\n\t\t\tfor (var event_name of registered_events) {\n\t\t\t\ttarget.removeEventListener(event_name, handle_event_propagation);\n\n\t\t\t\tvar n = /** @type {number} */ (document_listeners.get(event_name));\n\n\t\t\t\tif (--n === 0) {\n\t\t\t\t\tdocument.removeEventListener(event_name, handle_event_propagation);\n\t\t\t\t\tdocument_listeners.delete(event_name);\n\t\t\t\t} else {\n\t\t\t\t\tdocument_listeners.set(event_name, n);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\troot_event_handles.delete(event_handle);\n\n\t\t\tif (anchor_node !== anchor) {\n\t\t\t\tanchor_node.parentNode?.removeChild(anchor_node);\n\t\t\t}\n\t\t};\n\t});\n\n\tmounted_components.set(component, unmount);\n\treturn component;\n}\n\n/**\n * References of the components that were mounted or hydrated.\n * Uses a `WeakMap` to avoid memory leaks.\n */\nlet mounted_components = new WeakMap();\n\n/**\n * Unmounts a component that was previously mounted using `mount` or `hydrate`.\n *\n * Since 5.13.0, if `options.outro` is `true`, [transitions](https://svelte.dev/docs/svelte/transition) will play before the component is removed from the DOM.\n *\n * Returns a `Promise` that resolves after transitions have completed if `options.outro` is true, or immediately otherwise (prior to 5.13.0, returns `void`).\n *\n * ```js\n * import { mount, unmount } from 'svelte';\n * import App from './App.svelte';\n *\n * const app = mount(App, { target: document.body });\n *\n * // later...\n * unmount(app, { outro: true });\n * ```\n * @param {Record<string, any>} component\n * @param {{ outro?: boolean }} [options]\n * @returns {Promise<void>}\n */\nexport function unmount(component, options) {\n\tconst fn = mounted_components.get(component);\n\n\tif (fn) {\n\t\tmounted_components.delete(component);\n\t\treturn fn(options);\n\t}\n\n\tif (DEV) {\n\t\tw.lifecycle_double_unmount();\n\t}\n\n\treturn Promise.resolve();\n}\n", "/** @import { ActionReturn } from 'svelte/action' */\nimport { noop } from '../../../shared/utils.js';\nimport { user_pre_effect } from '../../reactivity/effects.js';\nimport { on } from '../elements/events.js';\n\n/**\n * Substitute for the `trusted` event modifier\n * @deprecated\n * @param {(event: Event, ...args: Array<unknown>) => void} fn\n * @returns {(event: Event, ...args: unknown[]) => void}\n */\nexport function trusted(fn) {\n\treturn function (...args) {\n\t\tvar event = /** @type {Event} */ (args[0]);\n\t\tif (event.isTrusted) {\n\t\t\t// @ts-ignore\n\t\t\tfn?.apply(this, args);\n\t\t}\n\t};\n}\n\n/**\n * Substitute for the `self` event modifier\n * @deprecated\n * @param {(event: Event, ...args: Array<unknown>) => void} fn\n * @returns {(event: Event, ...args: unknown[]) => void}\n */\nexport function self(fn) {\n\treturn function (...args) {\n\t\tvar event = /** @type {Event} */ (args[0]);\n\t\t// @ts-ignore\n\t\tif (event.target === this) {\n\t\t\t// @ts-ignore\n\t\t\tfn?.apply(this, args);\n\t\t}\n\t};\n}\n\n/**\n * Substitute for the `stopPropagation` event modifier\n * @deprecated\n * @param {(event: Event, ...args: Array<unknown>) => void} fn\n * @returns {(event: Event, ...args: unknown[]) => void}\n */\nexport function stopPropagation(fn) {\n\treturn function (...args) {\n\t\tvar event = /** @type {Event} */ (args[0]);\n\t\tevent.stopPropagation();\n\t\t// @ts-ignore\n\t\treturn fn?.apply(this, args);\n\t};\n}\n\n/**\n * Substitute for the `once` event modifier\n * @deprecated\n * @param {(event: Event, ...args: Array<unknown>) => void} fn\n * @returns {(event: Event, ...args: unknown[]) => void}\n */\nexport function once(fn) {\n\tvar ran = false;\n\n\treturn function (...args) {\n\t\tif (ran) return;\n\t\tran = true;\n\n\t\t// @ts-ignore\n\t\treturn fn?.apply(this, args);\n\t};\n}\n\n/**\n * Substitute for the `stopImmediatePropagation` event modifier\n * @deprecated\n * @param {(event: Event, ...args: Array<unknown>) => void} fn\n * @returns {(event: Event, ...args: unknown[]) => void}\n */\nexport function stopImmediatePropagation(fn) {\n\treturn function (...args) {\n\t\tvar event = /** @type {Event} */ (args[0]);\n\t\tevent.stopImmediatePropagation();\n\t\t// @ts-ignore\n\t\treturn fn?.apply(this, args);\n\t};\n}\n\n/**\n * Substitute for the `preventDefault` event modifier\n * @deprecated\n * @param {(event: Event, ...args: Array<unknown>) => void} fn\n * @returns {(event: Event, ...args: unknown[]) => void}\n */\nexport function preventDefault(fn) {\n\treturn function (...args) {\n\t\tvar event = /** @type {Event} */ (args[0]);\n\t\tevent.preventDefault();\n\t\t// @ts-ignore\n\t\treturn fn?.apply(this, args);\n\t};\n}\n\n/**\n * Substitute for the `passive` event modifier, implemented as an action\n * @deprecated\n * @param {HTMLElement} node\n * @param {[event: string, handler: () => EventListener]} options\n */\nexport function passive(node, [event, handler]) {\n\tuser_pre_effect(() => {\n\t\treturn on(node, event, handler() ?? noop, {\n\t\t\tpassive: true\n\t\t});\n\t});\n}\n\n/**\n * Substitute for the `nonpassive` event modifier, implemented as an action\n * @deprecated\n * @param {HTMLElement} node\n * @param {[event: string, handler: () => EventListener]} options\n */\nexport function nonpassive(node, [event, handler]) {\n\tuser_pre_effect(() => {\n\t\treturn on(node, event, handler() ?? noop, {\n\t\t\tpassive: false\n\t\t});\n\t});\n}\n", "/** @import { ComponentConstructorOptions, ComponentType, SvelteComponent, Component } from 'svelte' */\nimport { DIRTY, LEGACY_PROPS, MAYBE_DIRTY } from '../internal/client/constants.js';\nimport { user_pre_effect } from '../internal/client/reactivity/effects.js';\nimport { mutable_source, set } from '../internal/client/reactivity/sources.js';\nimport { hydrate, mount, unmount } from '../internal/client/render.js';\nimport { active_effect, flushSync, get, set_signal_status } from '../internal/client/runtime.js';\nimport { lifecycle_outside_component } from '../internal/shared/errors.js';\nimport { define_property, is_array } from '../internal/shared/utils.js';\nimport * as w from '../internal/client/warnings.js';\nimport { DEV } from 'esm-env';\nimport { FILENAME } from '../constants.js';\nimport { component_context, dev_current_component_function } from '../internal/client/context.js';\n\n/**\n * Takes the same options as a Svelte 4 component and the component function and returns a Svelte 4 compatible component.\n *\n * @deprecated Use this only as a temporary solution to migrate your imperative component code to Svelte 5.\n *\n * @template {Record<string, any>} Props\n * @template {Record<string, any>} Exports\n * @template {Record<string, any>} Events\n * @template {Record<string, any>} Slots\n *\n * @param {ComponentConstructorOptions<Props> & {\n * \tcomponent: ComponentType<SvelteComponent<Props, Events, Slots>> | Component<Props>;\n * }} options\n * @returns {SvelteComponent<Props, Events, Slots> & Exports}\n */\nexport function createClassComponent(options) {\n\t// @ts-expect-error $$prop_def etc are not actually defined\n\treturn new Svelte4Component(options);\n}\n\n/**\n * Takes the component function and returns a Svelte 4 compatible component constructor.\n *\n * @deprecated Use this only as a temporary solution to migrate your imperative component code to Svelte 5.\n *\n * @template {Record<string, any>} Props\n * @template {Record<string, any>} Exports\n * @template {Record<string, any>} Events\n * @template {Record<string, any>} Slots\n *\n * @param {SvelteComponent<Props, Events, Slots> | Component<Props>} component\n * @returns {ComponentType<SvelteComponent<Props, Events, Slots> & Exports>}\n */\nexport function asClassComponent(component) {\n\t// @ts-expect-error $$prop_def etc are not actually defined\n\treturn class extends Svelte4Component {\n\t\t/** @param {any} options */\n\t\tconstructor(options) {\n\t\t\tsuper({\n\t\t\t\tcomponent,\n\t\t\t\t...options\n\t\t\t});\n\t\t}\n\t};\n}\n\n/**\n * Support using the component as both a class and function during the transition period\n * @typedef  {{new (o: ComponentConstructorOptions): SvelteComponent;(...args: Parameters<Component<Record<string, any>>>): ReturnType<Component<Record<string, any>, Record<string, any>>>;}} LegacyComponentType\n */\n\nclass Svelte4Component {\n\t/** @type {any} */\n\t#events;\n\n\t/** @type {Record<string, any>} */\n\t#instance;\n\n\t/**\n\t * @param {ComponentConstructorOptions & {\n\t *  component: any;\n\t * }} options\n\t */\n\tconstructor(options) {\n\t\tvar sources = new Map();\n\n\t\t/**\n\t\t * @param {string | symbol} key\n\t\t * @param {unknown} value\n\t\t */\n\t\tvar add_source = (key, value) => {\n\t\t\tvar s = mutable_source(value);\n\t\t\tsources.set(key, s);\n\t\t\treturn s;\n\t\t};\n\n\t\t// Replicate coarse-grained props through a proxy that has a version source for\n\t\t// each property, which is incremented on updates to the property itself. Do not\n\t\t// use our $state proxy because that one has fine-grained reactivity.\n\t\tconst props = new Proxy(\n\t\t\t{ ...(options.props || {}), $$events: {} },\n\t\t\t{\n\t\t\t\tget(target, prop) {\n\t\t\t\t\treturn get(sources.get(prop) ?? add_source(prop, Reflect.get(target, prop)));\n\t\t\t\t},\n\t\t\t\thas(target, prop) {\n\t\t\t\t\t// Necessary to not throw \"invalid binding\" validation errors on the component side\n\t\t\t\t\tif (prop === LEGACY_PROPS) return true;\n\n\t\t\t\t\tget(sources.get(prop) ?? add_source(prop, Reflect.get(target, prop)));\n\t\t\t\t\treturn Reflect.has(target, prop);\n\t\t\t\t},\n\t\t\t\tset(target, prop, value) {\n\t\t\t\t\tset(sources.get(prop) ?? add_source(prop, value), value);\n\t\t\t\t\treturn Reflect.set(target, prop, value);\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\n\t\tthis.#instance = (options.hydrate ? hydrate : mount)(options.component, {\n\t\t\ttarget: options.target,\n\t\t\tanchor: options.anchor,\n\t\t\tprops,\n\t\t\tcontext: options.context,\n\t\t\tintro: options.intro ?? false,\n\t\t\trecover: options.recover\n\t\t});\n\n\t\t// We don't flushSync for custom element wrappers or if the user doesn't want it\n\t\tif (!options?.props?.$$host || options.sync === false) {\n\t\t\tflushSync();\n\t\t}\n\n\t\tthis.#events = props.$$events;\n\n\t\tfor (const key of Object.keys(this.#instance)) {\n\t\t\tif (key === '$set' || key === '$destroy' || key === '$on') continue;\n\t\t\tdefine_property(this, key, {\n\t\t\t\tget() {\n\t\t\t\t\treturn this.#instance[key];\n\t\t\t\t},\n\t\t\t\t/** @param {any} value */\n\t\t\t\tset(value) {\n\t\t\t\t\tthis.#instance[key] = value;\n\t\t\t\t},\n\t\t\t\tenumerable: true\n\t\t\t});\n\t\t}\n\n\t\tthis.#instance.$set = /** @param {Record<string, any>} next */ (next) => {\n\t\t\tObject.assign(props, next);\n\t\t};\n\n\t\tthis.#instance.$destroy = () => {\n\t\t\tunmount(this.#instance);\n\t\t};\n\t}\n\n\t/** @param {Record<string, any>} props */\n\t$set(props) {\n\t\tthis.#instance.$set(props);\n\t}\n\n\t/**\n\t * @param {string} event\n\t * @param {(...args: any[]) => any} callback\n\t * @returns {any}\n\t */\n\t$on(event, callback) {\n\t\tthis.#events[event] = this.#events[event] || [];\n\n\t\t/** @param {any[]} args */\n\t\tconst cb = (...args) => callback.call(this, ...args);\n\t\tthis.#events[event].push(cb);\n\t\treturn () => {\n\t\t\tthis.#events[event] = this.#events[event].filter(/** @param {any} fn */ (fn) => fn !== cb);\n\t\t};\n\t}\n\n\t$destroy() {\n\t\tthis.#instance.$destroy();\n\t}\n}\n\n/**\n * Runs the given function once immediately on the server, and works like `$effect.pre` on the client.\n *\n * @deprecated Use this only as a temporary solution to migrate your component code to Svelte 5.\n * @param {() => void | (() => void)} fn\n * @returns {void}\n */\nexport function run(fn) {\n\tuser_pre_effect(() => {\n\t\tfn();\n\t\tvar effect = /** @type {import('#client').Effect} */ (active_effect);\n\t\t// If the effect is immediately made dirty again, mark it as maybe dirty to emulate legacy behaviour\n\t\tif ((effect.f & DIRTY) !== 0) {\n\t\t\tlet filename = \"a file (we can't know which one)\";\n\t\t\tif (DEV) {\n\t\t\t\t// @ts-ignore\n\t\t\t\tfilename = dev_current_component_function?.[FILENAME] ?? filename;\n\t\t\t}\n\t\t\tw.legacy_recursive_reactive_block(filename);\n\t\t\tset_signal_status(effect, MAYBE_DIRTY);\n\t\t}\n\t});\n}\n\n/**\n * Function to mimic the multiple listeners available in svelte 4\n * @deprecated\n * @param {EventListener[]} handlers\n * @returns {EventListener}\n */\nexport function handlers(...handlers) {\n\treturn function (event) {\n\t\tconst { stopImmediatePropagation } = event;\n\t\tlet stopped = false;\n\n\t\tevent.stopImmediatePropagation = () => {\n\t\t\tstopped = true;\n\t\t\tstopImmediatePropagation.call(event);\n\t\t};\n\n\t\tconst errors = [];\n\n\t\tfor (const handler of handlers) {\n\t\t\ttry {\n\t\t\t\t// @ts-expect-error `this` is not typed\n\t\t\t\thandler?.call(this, event);\n\t\t\t} catch (e) {\n\t\t\t\terrors.push(e);\n\t\t\t}\n\n\t\t\tif (stopped) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tfor (let error of errors) {\n\t\t\tqueueMicrotask(() => {\n\t\t\t\tthrow error;\n\t\t\t});\n\t\t}\n\t};\n}\n\n/**\n * Function to create a `bubble` function that mimic the behavior of `on:click` without handler available in svelte 4.\n * @deprecated Use this only as a temporary solution to migrate your automatically delegated events in Svelte 5.\n */\nexport function createBubbler() {\n\tconst active_component_context = component_context;\n\tif (active_component_context === null) {\n\t\tlifecycle_outside_component('createBubbler');\n\t}\n\n\treturn (/**@type {string}*/ type) => (/**@type {Event}*/ event) => {\n\t\tconst events = /** @type {Record<string, Function | Function[]>} */ (\n\t\t\tactive_component_context.s.$$events\n\t\t)?.[/** @type {any} */ (type)];\n\n\t\tif (events) {\n\t\t\tconst callbacks = is_array(events) ? events.slice() : [events];\n\t\t\tfor (const fn of callbacks) {\n\t\t\t\tfn.call(active_component_context.x, event);\n\t\t\t}\n\t\t\treturn !event.defaultPrevented;\n\t\t}\n\t\treturn true;\n\t};\n}\n\nexport {\n\tonce,\n\tpreventDefault,\n\tself,\n\tstopImmediatePropagation,\n\tstopPropagation,\n\ttrusted,\n\tpassive,\n\tnonpassive\n} from '../internal/client/dom/legacy/event-modifiers.js';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAI;AAEG,SAAS,oBAAoB;AACnC,gBAAc;AACf;AAMO,SAAS,KAAK,WAAW;AAG/B,MAAI,wBAAwB;AAC5B,MAAI,gBAAgB;AAGpB,MAAI;AAEJ,MAAI,WAAW;AACd,4BAAwB;AAGxB,QAAI,gBAAgB,QAAW;AAC9B;AAAA,MAA2C,gBAAgB,SAAS,IAAI;AAAA,IACzE;AAEA,WACC,gBAAgB,SACf,YAAY,aAAa;AAAA,IAA6B,YAAa,SAAS,kBAC5E;AACD;AAAA,MAA2C,iBAAiB,WAAW;AAAA,IACxE;AAIA,QAAI,gBAAgB,MAAM;AACzB,oBAAc,KAAK;AAAA,IACpB,OAAO;AACN,oBAAc;AAAA;AAAA,QAA8C,iBAAiB,WAAW;AAAA,MAAE;AAAA,IAC3F;AAAA,EACD;AAEA,MAAI,CAAC,WAAW;AACf,aAAS,SAAS,KAAK,YAAY,YAAY,CAAC;AAAA,EACjD;AAEA,MAAI;AACH,UAAM,MAAM,UAAU,MAAM,GAAG,WAAW;AAAA,EAC3C,UAAE;AACD,QAAI,eAAe;AAClB,oBAAc,IAAI;AAClB,oBAAc;AACd;AAAA;AAAA,QAA8C;AAAA,MAAsB;AAAA,IACrE;AAAA,EACD;AACD;;;ACjEO,SAAS,0BAA0B,MAAM;AAC/C,MAAI,OAAO,SAAS,cAAc,UAAU;AAC5C,OAAK,YAAY;AACjB,SAAO,KAAK;AACb;;;ACMO,SAAS,aAAa,OAAO,KAAK;AACxC,MAAI;AAAA;AAAA,IAAgC;AAAA;AACpC,MAAI,OAAO,gBAAgB,MAAM;AAChC,WAAO,cAAc;AACrB,WAAO,YAAY;AAAA,EACpB;AACD;AAQO,SAAS,SAAS,SAAS,OAAO;AACxC,MAAI,eAAe,QAAQ,uBAAuB;AAClD,MAAI,mBAAmB,QAAQ,8BAA8B;AAG7D,MAAI;AAMJ,MAAI,YAAY,CAAC,QAAQ,WAAW,KAAK;AAEzC,SAAO,MAAM;AACZ,QAAI,WAAW;AACd,mBAAa,cAAc,IAAI;AAC/B,aAAO;AAAA,IACR;AAEA,QAAI,SAAS,QAAW;AACvB,aAAO,0BAA0B,YAAY,UAAU,QAAQ,OAAO;AACtE,UAAI,CAAC,YAAa;AAAA,MAA4B,gBAAgB,IAAI;AAAA,IACnE;AAEA,QAAI;AAAA;AAAA,MACH,mBAAmB,aAAa,SAAS,WAAW,MAAM,IAAI,IAAI,KAAK,UAAU,IAAI;AAAA;AAGtF,QAAI,aAAa;AAChB,UAAI;AAAA;AAAA,QAAqC,gBAAgB,KAAK;AAAA;AAC9D,UAAI;AAAA;AAAA,QAAmC,MAAM;AAAA;AAE7C,mBAAa,OAAO,GAAG;AAAA,IACxB,OAAO;AACN,mBAAa,OAAO,KAAK;AAAA,IAC1B;AAEA,WAAO;AAAA,EACR;AACD;AAQO,SAAS,qBAAqB,SAAS,OAAO;AACpD,MAAI,KAAK,SAAS,SAAS,KAAK;AAChC,SAAO,MAAM;AAAA;AAAA,IAAuD,GAAG;AAAA,EAAE;AAC1E;AASO,SAAS,YAAY,SAAS,OAAO,KAAK,OAAO;AAKvD,MAAI,YAAY,CAAC,QAAQ,WAAW,KAAK;AAEzC,MAAI,eAAe,QAAQ,uBAAuB;AAClD,MAAI,UAAU,IAAI,EAAE,IAAI,YAAY,UAAU,QAAQ,OAAO,KAAK,EAAE;AAGpE,MAAI;AAEJ,SAAO,MAAM;AACZ,QAAI,WAAW;AACd,mBAAa,cAAc,IAAI;AAC/B,aAAO;AAAA,IACR;AAEA,QAAI,CAAC,MAAM;AACV,UAAI;AAAA;AAAA,QAA4C,0BAA0B,OAAO;AAAA;AACjF,UAAI;AAAA;AAAA,QAA+B,gBAAgB,QAAQ;AAAA;AAE3D,UAAI,aAAa;AAChB,eAAO,SAAS,uBAAuB;AACvC,eAAO,gBAAgB,IAAI,GAAG;AAC7B,eAAK;AAAA;AAAA,YAAiC,gBAAgB,IAAI;AAAA,UAAE;AAAA,QAC7D;AAAA,MACD,OAAO;AACN;AAAA,QAA+B,gBAAgB,IAAI;AAAA,MACpD;AAAA,IACD;AAEA,QAAI;AAAA;AAAA,MAAqC,KAAK,UAAU,IAAI;AAAA;AAE5D,QAAI,aAAa;AAChB,UAAI;AAAA;AAAA,QAAqC,gBAAgB,KAAK;AAAA;AAC9D,UAAI;AAAA;AAAA,QAAmC,MAAM;AAAA;AAE7C,mBAAa,OAAO,GAAG;AAAA,IACxB,OAAO;AACN,mBAAa,OAAO,KAAK;AAAA,IAC1B;AAEA,WAAO;AAAA,EACR;AACD;AAQO,SAAS,yBAAyB,SAAS,OAAO;AACxD,MAAI,KAAK,YAAY,SAAS,KAAK;AACnC,SAAO,MAAM;AAAA;AAAA,IAAuD,GAAG;AAAA,EAAE;AAC1E;AAQO,SAAS,gBAAgB,SAAS,OAAO;AAC/C,SAAO,YAAY,SAAS,OAAO,MAAM;AAC1C;AAQA,SAAS,YAAY,MAAM;AAE1B,MAAI,UAAW,QAAO;AAEtB,QAAM,cAAc,KAAK,aAAa;AACtC,QAAM;AAAA;AAAA,IACuB,KAAM,YAAY,WAC3C;AAAA;AAAA,MAAmC;AAAA,IAAK,IACxC,KAAK,iBAAiB,QAAQ;AAAA;AAClC,QAAM;AAAA;AAAA,IAAgC;AAAA;AAEtC,aAAW,UAAU,SAAS;AAC7B,UAAM,QAAQ,SAAS,cAAc,QAAQ;AAC7C,aAAS,aAAa,OAAO,YAAY;AACxC,YAAM,aAAa,UAAU,MAAM,UAAU,KAAK;AAAA,IACnD;AAEA,UAAM,cAAc,OAAO;AAG3B,QAAI,cAAc,KAAK,eAAe,SAAS,SAAS,QAAQ;AAC/D,aAAO,cAAc;AAAA,IACtB;AACA,QAAI,cAAc,KAAK,cAAc,SAAS,SAAS,QAAQ;AAC9D,aAAO,YAAY;AAAA,IACpB;AAEA,WAAO,YAAY,KAAK;AAAA,EACzB;AACA,SAAO;AACR;AAMO,SAAS,KAAK,QAAQ,IAAI;AAChC,MAAI,CAAC,WAAW;AACf,QAAI,IAAI,YAAY,QAAQ,EAAE;AAC9B,iBAAa,GAAG,CAAC;AACjB,WAAO;AAAA,EACR;AAEA,MAAI,OAAO;AAEX,MAAI,KAAK,aAAa,GAAG;AAExB,SAAK,OAAQ,OAAO,YAAY,CAAE;AAClC,qBAAiB,IAAI;AAAA,EACtB;AAEA,eAAa,MAAM,IAAI;AACvB,SAAO;AACR;AAEO,SAAS,UAAU;AAEzB,MAAI,WAAW;AACd,iBAAa,cAAc,IAAI;AAC/B,WAAO;AAAA,EACR;AAEA,MAAI,OAAO,SAAS,uBAAuB;AAC3C,MAAI,QAAQ,SAAS,cAAc,EAAE;AACrC,MAAI,SAAS,YAAY;AACzB,OAAK,OAAO,OAAO,MAAM;AAEzB,eAAa,OAAO,MAAM;AAE1B,SAAO;AACR;AAQO,SAAS,OAAO,QAAQ,KAAK;AACnC,MAAI,WAAW;AACQ,IAAC,cAAe,YAAY;AAClD,iBAAa;AACb;AAAA,EACD;AAEA,MAAI,WAAW,MAAM;AAEpB;AAAA,EACD;AAEA,SAAO;AAAA;AAAA,IAA4B;AAAA,EAAI;AACxC;AAKO,SAAS,WAAW;AA/P3B;AAgQC,MACC,aACA,gBACA,aAAa,aAAa,OAC1B,kBAAa,gBAAb,mBAA0B,WAAW,OACpC;AACD,UAAM,KAAK,aAAa,YAAY,UAAU,CAAC;AAC/C,iBAAa;AACb,WAAO;AAAA,EACR;AAGA,GAAC,YAAO,aAAP,OAAO,WAAa,CAAC,IAAG,QAAxB,GAAwB,MAAQ;AAGjC,SAAO,IAAI,OAAO,SAAS,KAAK;AACjC;;;AChRA,IAAM,0BAA0B;AAMzB,SAAS,KAAK,KAAK;AACzB,QAAM,IAAI,QAAQ,yBAAyB,EAAE;AAC7C,MAAIA,QAAO;AACX,MAAI,IAAI,IAAI;AAEZ,SAAO,IAAK,CAAAA,SAASA,SAAQ,KAAKA,QAAQ,IAAI,WAAW,CAAC;AAC1D,UAAQA,UAAS,GAAG,SAAS,EAAE;AAChC;AAEA,IAAM,qBAAqB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAMO,SAAS,QAAQ,MAAM;AAC7B,SAAO,mBAAmB,SAAS,IAAI,KAAK,KAAK,YAAY,MAAM;AACpE;AAgEO,SAAS,iBAAiB,MAAM;AACtC,SAAO,KAAK,SAAS,SAAS,KAAK,SAAS,uBAAuB,SAAS;AAC7E;AAGA,IAAM,mBAAmB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAMO,SAAS,aAAa,YAAY;AACxC,SAAO,iBAAiB,SAAS,UAAU;AAC5C;AAKA,IAAM,yBAAyB;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAgBA,IAAM,oBAAoB;AAAA;AAAA,EAEzB,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,EACV,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,yBAAyB;AAAA,EACzB,uBAAuB;AACxB;AAKO,SAAS,oBAAoB,MAAM;AACzC,SAAO,KAAK,YAAY;AACxB,SAAO,kBAAkB,IAAI,KAAK;AACnC;AAEA,IAAM,iBAAiB;AAAA,EACtB,GAAG;AAAA,EACH;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AA6BA,IAAM,iBAAiB,CAAC,cAAc,WAAW;AAM1C,SAAS,iBAAiB,MAAM;AACtC,SAAO,eAAe,SAAS,IAAI;AACpC;AA6LA,IAAM;AAAA;AAAA,EAA0C,CAAC,YAAY,UAAU,SAAS,OAAO;AAAA;AAGhF,SAAS,oBAAoB,MAAM;AACzC,SAAO,kBAAkB;AAAA;AAAA,IAAmD;AAAA,EAAK;AAClF;AAQO,SAAS,kBAAkB,UAAU;AAC3C;AAAA;AAAA,IAAyB,qCAAU,QAAQ,OAAO;AAAA;AACnD;;;ACnbO,IAAI,eAAe;AAGnB,SAAS,iBAAiB,OAAO;AACvC,iBAAe;AAChB;AAOO,SAAS,SAASC,OAAM,OAAO;AAErC,MAAI,MAAM,SAAS,OAAO,KAAK,OAAO,UAAU,WAAW,QAAQ,KAAK;AAExE,MAAI,SAASA,MAAK,QAALA,MAAK,MAAQA,MAAK,aAAY;AAE1C,IAAAA,MAAK,MAAM;AACX,IAAAA,MAAK,YAAY,MAAM;AAAA,EACxB;AACD;AAYO,SAAS,MAAM,WAAW,SAAS;AACzC,SAAO,OAAO,WAAW,OAAO;AACjC;AAyBO,SAAS,QAAQ,WAAW,SAAS;AAC3C,kBAAgB;AAChB,UAAQ,QAAQ,QAAQ,SAAS;AACjC,QAAM,SAAS,QAAQ;AACvB,QAAM,gBAAgB;AACtB,QAAM,wBAAwB;AAE9B,MAAI;AACH,QAAI;AAAA;AAAA,MAAsC,gBAAgB,MAAM;AAAA;AAChE,WACC,WACC,OAAO,aAAa;AAAA,IAA6B,OAAQ,SAAS,kBAClE;AACD;AAAA,MAAsC,iBAAiB,MAAM;AAAA,IAC9D;AAEA,QAAI,CAAC,QAAQ;AACZ,YAAM;AAAA,IACP;AAEA,kBAAc,IAAI;AAClB;AAAA;AAAA,MAAyC;AAAA,IAAO;AAChD,iBAAa;AAEb,UAAM,WAAW,OAAO,WAAW,EAAE,GAAG,SAAS,OAAO,CAAC;AAEzD,QACC,iBAAiB,QACjB,aAAa,aAAa;AAAA,IACF,aAAc,SAAS,eAC9C;AACD,MAAE,mBAAmB;AACrB,YAAM;AAAA,IACP;AAEA,kBAAc,KAAK;AAEnB;AAAA;AAAA,MAAgC;AAAA;AAAA,EACjC,SAAS,OAAO;AACf,QAAI,UAAU,iBAAiB;AAC9B,UAAI,QAAQ,YAAY,OAAO;AAC9B,QAAE,iBAAiB;AAAA,MACpB;AAGA,sBAAgB;AAChB,yBAAmB,MAAM;AAEzB,oBAAc,KAAK;AACnB,aAAO,MAAM,WAAW,OAAO;AAAA,IAChC;AAEA,UAAM;AAAA,EACP,UAAE;AACD,kBAAc,aAAa;AAC3B,qBAAiB,qBAAqB;AACtC,sBAAkB;AAAA,EACnB;AACD;AAGA,IAAM,qBAAqB,oBAAI,IAAI;AAQnC,SAAS,OAAO,WAAW,EAAE,QAAQ,QAAQ,QAAQ,CAAC,GAAG,QAAQ,SAAS,QAAQ,KAAK,GAAG;AACzF,kBAAgB;AAEhB,MAAI,oBAAoB,oBAAI,IAAI;AAGhC,MAAI,eAAe,CAACC,YAAW;AAC9B,aAAS,IAAI,GAAG,IAAIA,QAAO,QAAQ,KAAK;AACvC,UAAI,aAAaA,QAAO,CAAC;AAEzB,UAAI,kBAAkB,IAAI,UAAU,EAAG;AACvC,wBAAkB,IAAI,UAAU;AAEhC,UAAIC,WAAU,iBAAiB,UAAU;AAKzC,aAAO,iBAAiB,YAAY,0BAA0B,EAAE,SAAAA,SAAQ,CAAC;AAEzE,UAAI,IAAI,mBAAmB,IAAI,UAAU;AAEzC,UAAI,MAAM,QAAW;AAGpB,iBAAS,iBAAiB,YAAY,0BAA0B,EAAE,SAAAA,SAAQ,CAAC;AAC3E,2BAAmB,IAAI,YAAY,CAAC;AAAA,MACrC,OAAO;AACN,2BAAmB,IAAI,YAAY,IAAI,CAAC;AAAA,MACzC;AAAA,IACD;AAAA,EACD;AAEA,eAAa,WAAW,qBAAqB,CAAC;AAC9C,qBAAmB,IAAI,YAAY;AAInC,MAAI,YAAY;AAEhB,MAAIC,WAAU,eAAe,MAAM;AAClC,QAAI,cAAc,UAAU,OAAO,YAAY,YAAY,CAAC;AAE5D,WAAO,MAAM;AACZ,UAAI,SAAS;AACZ,aAAK,CAAC,CAAC;AACP,YAAI;AAAA;AAAA,UAAuC;AAAA;AAC3C,YAAI,IAAI;AAAA,MACT;AAEA,UAAI,QAAQ;AAEQ,QAAC,MAAO,WAAW;AAAA,MACvC;AAEA,UAAI,WAAW;AACd;AAAA;AAAA,UAA0C;AAAA,UAAc;AAAA,QAAI;AAAA,MAC7D;AAEA,qBAAe;AAEf,kBAAY,UAAU,aAAa,KAAK,KAAK,CAAC;AAC9C,qBAAe;AAEf,UAAI,WAAW;AACQ,QAAC,cAAe,YAAY;AAAA,MACnD;AAEA,UAAI,SAAS;AACZ,YAAI;AAAA,MACL;AAAA,IACD,CAAC;AAED,WAAO,MAAM;AAhPf;AAiPG,eAAS,cAAc,mBAAmB;AACzC,eAAO,oBAAoB,YAAY,wBAAwB;AAE/D,YAAI;AAAA;AAAA,UAA2B,mBAAmB,IAAI,UAAU;AAAA;AAEhE,YAAI,EAAE,MAAM,GAAG;AACd,mBAAS,oBAAoB,YAAY,wBAAwB;AACjE,6BAAmB,OAAO,UAAU;AAAA,QACrC,OAAO;AACN,6BAAmB,IAAI,YAAY,CAAC;AAAA,QACrC;AAAA,MACD;AAEA,yBAAmB,OAAO,YAAY;AAEtC,UAAI,gBAAgB,QAAQ;AAC3B,0BAAY,eAAZ,mBAAwB,YAAY;AAAA,MACrC;AAAA,IACD;AAAA,EACD,CAAC;AAED,qBAAmB,IAAI,WAAWA,QAAO;AACzC,SAAO;AACR;AAMA,IAAI,qBAAqB,oBAAI,QAAQ;AAsB9B,SAAS,QAAQ,WAAW,SAAS;AAC3C,QAAM,KAAK,mBAAmB,IAAI,SAAS;AAE3C,MAAI,IAAI;AACP,uBAAmB,OAAO,SAAS;AACnC,WAAO,GAAG,OAAO;AAAA,EAClB;AAEA,MAAI,cAAK;AACR,IAAE,yBAAyB;AAAA,EAC5B;AAEA,SAAO,QAAQ,QAAQ;AACxB;;;ACtSO,SAAS,QAAQ,IAAI;AAC3B,SAAO,YAAa,MAAM;AACzB,QAAI;AAAA;AAAA,MAA8B,KAAK,CAAC;AAAA;AACxC,QAAI,MAAM,WAAW;AAEpB,+BAAI,MAAM,MAAM;AAAA,IACjB;AAAA,EACD;AACD;AAQO,SAAS,KAAK,IAAI;AACxB,SAAO,YAAa,MAAM;AACzB,QAAI;AAAA;AAAA,MAA8B,KAAK,CAAC;AAAA;AAExC,QAAI,MAAM,WAAW,MAAM;AAE1B,+BAAI,MAAM,MAAM;AAAA,IACjB;AAAA,EACD;AACD;AAQO,SAAS,gBAAgB,IAAI;AACnC,SAAO,YAAa,MAAM;AACzB,QAAI;AAAA;AAAA,MAA8B,KAAK,CAAC;AAAA;AACxC,UAAM,gBAAgB;AAEtB,WAAO,yBAAI,MAAM,MAAM;AAAA,EACxB;AACD;AAQO,SAAS,KAAK,IAAI;AACxB,MAAI,MAAM;AAEV,SAAO,YAAa,MAAM;AACzB,QAAI,IAAK;AACT,UAAM;AAGN,WAAO,yBAAI,MAAM,MAAM;AAAA,EACxB;AACD;AAQO,SAAS,yBAAyB,IAAI;AAC5C,SAAO,YAAa,MAAM;AACzB,QAAI;AAAA;AAAA,MAA8B,KAAK,CAAC;AAAA;AACxC,UAAM,yBAAyB;AAE/B,WAAO,yBAAI,MAAM,MAAM;AAAA,EACxB;AACD;AAQO,SAAS,eAAe,IAAI;AAClC,SAAO,YAAa,MAAM;AACzB,QAAI;AAAA;AAAA,MAA8B,KAAK,CAAC;AAAA;AACxC,UAAM,eAAe;AAErB,WAAO,yBAAI,MAAM,MAAM;AAAA,EACxB;AACD;AAQO,SAAS,QAAQ,MAAM,CAAC,OAAO,OAAO,GAAG;AAC/C,kBAAgB,MAAM;AACrB,WAAO,GAAG,MAAM,OAAO,QAAQ,KAAK,MAAM;AAAA,MACzC,SAAS;AAAA,IACV,CAAC;AAAA,EACF,CAAC;AACF;AAQO,SAAS,WAAW,MAAM,CAAC,OAAO,OAAO,GAAG;AAClD,kBAAgB,MAAM;AACrB,WAAO,GAAG,MAAM,OAAO,QAAQ,KAAK,MAAM;AAAA,MACzC,SAAS;AAAA,IACV,CAAC;AAAA,EACF,CAAC;AACF;;;ACnGO,SAAS,qBAAqB,SAAS;AAE7C,SAAO,IAAI,iBAAiB,OAAO;AACpC;AAeO,SAAS,iBAAiB,WAAW;AAE3C,SAAO,cAAc,iBAAiB;AAAA;AAAA,IAErC,YAAY,SAAS;AACpB,YAAM;AAAA,QACL;AAAA,QACA,GAAG;AAAA,MACJ,CAAC;AAAA,IACF;AAAA,EACD;AACD;AAzDA;AAgEA,IAAM,mBAAN,MAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYtB,YAAY,SAAS;AAVrB;AAAA;AAGA;AAAA;AArED;AA6EE,QAAI,UAAU,oBAAI,IAAI;AAMtB,QAAI,aAAa,CAAC,KAAK,UAAU;AAChC,UAAI,IAAI,eAAe,KAAK;AAC5B,cAAQ,IAAI,KAAK,CAAC;AAClB,aAAO;AAAA,IACR;AAKA,UAAM,QAAQ,IAAI;AAAA,MACjB,EAAE,GAAI,QAAQ,SAAS,CAAC,GAAI,UAAU,CAAC,EAAE;AAAA,MACzC;AAAA,QACC,IAAI,QAAQ,MAAM;AACjB,iBAAO,IAAI,QAAQ,IAAI,IAAI,KAAK,WAAW,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,CAAC;AAAA,QAC5E;AAAA,QACA,IAAI,QAAQ,MAAM;AAEjB,cAAI,SAAS,aAAc,QAAO;AAElC,cAAI,QAAQ,IAAI,IAAI,KAAK,WAAW,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,CAAC;AACpE,iBAAO,QAAQ,IAAI,QAAQ,IAAI;AAAA,QAChC;AAAA,QACA,IAAI,QAAQ,MAAM,OAAO;AACxB,cAAI,QAAQ,IAAI,IAAI,KAAK,WAAW,MAAM,KAAK,GAAG,KAAK;AACvD,iBAAO,QAAQ,IAAI,QAAQ,MAAM,KAAK;AAAA,QACvC;AAAA,MACD;AAAA,IACD;AAEA,uBAAK,YAAa,QAAQ,UAAU,UAAU,OAAO,QAAQ,WAAW;AAAA,MACvE,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,MAChB;AAAA,MACA,SAAS,QAAQ;AAAA,MACjB,OAAO,QAAQ,SAAS;AAAA,MACxB,SAAS,QAAQ;AAAA,IAClB,CAAC;AAGD,QAAI,GAAC,wCAAS,UAAT,mBAAgB,WAAU,QAAQ,SAAS,OAAO;AACtD,gBAAU;AAAA,IACX;AAEA,uBAAK,SAAU,MAAM;AAErB,eAAW,OAAO,OAAO,KAAK,mBAAK,UAAS,GAAG;AAC9C,UAAI,QAAQ,UAAU,QAAQ,cAAc,QAAQ,MAAO;AAC3D,sBAAgB,MAAM,KAAK;AAAA,QAC1B,MAAM;AACL,iBAAO,mBAAK,WAAU,GAAG;AAAA,QAC1B;AAAA;AAAA,QAEA,IAAI,OAAO;AACV,6BAAK,WAAU,GAAG,IAAI;AAAA,QACvB;AAAA,QACA,YAAY;AAAA,MACb,CAAC;AAAA,IACF;AAEA,uBAAK,WAAU;AAAA,IAAgD,CAAC,SAAS;AACxE,aAAO,OAAO,OAAO,IAAI;AAAA,IAC1B;AAEA,uBAAK,WAAU,WAAW,MAAM;AAC/B,cAAQ,mBAAK,UAAS;AAAA,IACvB;AAAA,EACD;AAAA;AAAA,EAGA,KAAK,OAAO;AACX,uBAAK,WAAU,KAAK,KAAK;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO,UAAU;AACpB,uBAAK,SAAQ,KAAK,IAAI,mBAAK,SAAQ,KAAK,KAAK,CAAC;AAG9C,UAAM,KAAK,IAAI,SAAS,SAAS,KAAK,MAAM,GAAG,IAAI;AACnD,uBAAK,SAAQ,KAAK,EAAE,KAAK,EAAE;AAC3B,WAAO,MAAM;AACZ,yBAAK,SAAQ,KAAK,IAAI,mBAAK,SAAQ,KAAK,EAAE;AAAA;AAAA,QAA8B,CAAC,OAAO,OAAO;AAAA,MAAE;AAAA,IAC1F;AAAA,EACD;AAAA,EAEA,WAAW;AACV,uBAAK,WAAU,SAAS;AAAA,EACzB;AACD;AA7GC;AAGA;AAmHM,SAAS,IAAI,IAAI;AACvB,kBAAgB,MAAM;AAzLvB;AA0LE,OAAG;AACH,QAAI;AAAA;AAAA,MAAkD;AAAA;AAEtD,SAAK,OAAO,IAAI,WAAW,GAAG;AAC7B,UAAI,WAAW;AACf,UAAI,cAAK;AAER,qBAAW,2DAAiC,cAAa;AAAA,MAC1D;AACA,MAAE,gCAAgC,QAAQ;AAC1C,wBAAkB,QAAQ,WAAW;AAAA,IACtC;AAAA,EACD,CAAC;AACF;AAQO,SAAS,YAAYC,WAAU;AACrC,SAAO,SAAU,OAAO;AACvB,UAAM,EAAE,0BAAAC,0BAAyB,IAAI;AACrC,QAAI,UAAU;AAEd,UAAM,2BAA2B,MAAM;AACtC,gBAAU;AACV,MAAAA,0BAAyB,KAAK,KAAK;AAAA,IACpC;AAEA,UAAM,SAAS,CAAC;AAEhB,eAAW,WAAWD,WAAU;AAC/B,UAAI;AAEH,2CAAS,KAAK,MAAM;AAAA,MACrB,SAAS,GAAG;AACX,eAAO,KAAK,CAAC;AAAA,MACd;AAEA,UAAI,SAAS;AACZ;AAAA,MACD;AAAA,IACD;AAEA,aAAS,SAAS,QAAQ;AACzB,qBAAe,MAAM;AACpB,cAAM;AAAA,MACP,CAAC;AAAA,IACF;AAAA,EACD;AACD;AAMO,SAAS,gBAAgB;AAC/B,QAAM,2BAA2B;AACjC,MAAI,6BAA6B,MAAM;AACtC,gCAA4B,eAAe;AAAA,EAC5C;AAEA,SAAO,CAAqB,SAAS,CAAoB,UAAU;AA1PpE;AA2PE,UAAM;AAAA;AAAA,OACL,8BAAyB,EAAE,aAA3B;AAAA;AAAA,QACuB;AAAA;AAAA;AAExB,QAAI,QAAQ;AACX,YAAM,YAAY,SAAS,MAAM,IAAI,OAAO,MAAM,IAAI,CAAC,MAAM;AAC7D,iBAAW,MAAM,WAAW;AAC3B,WAAG,KAAK,yBAAyB,GAAG,KAAK;AAAA,MAC1C;AACA,aAAO,CAAC,MAAM;AAAA,IACf;AACA,WAAO;AAAA,EACR;AACD;", "names": ["hash", "text", "events", "passive", "unmount", "handlers", "stopImmediatePropagation"]}