import { json } from '@sveltejs/kit';
import { getFeatureById, getFeatureLimitById } from '$lib/models/features/registry';
import { hasReachedLimit } from '$lib/server/feature-usage';

/**
 * Check if a user has access to a feature
 * GET /api/feature-access?featureId=feature_id
 */
export async function GET({ url, locals }) {
  // Get the session from locals
  const session = locals.session;

  // Check if the user is authenticated
  if (!session?.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Get the feature ID from the query parameters
  const featureId = url.searchParams.get('featureId');

  if (!featureId) {
    return json({ error: 'Feature ID is required' }, { status: 400 });
  }

  // Get the feature
  const feature = getFeatureById(featureId);

  if (!feature) {
    return json({ error: 'Feature not found' }, { status: 404 });
  }

  // Check if the user has access to the feature
  try {
    // If the feature has no limits, assume the user has access
    if (!feature.limits || feature.limits.length === 0) {
      return json({ hasAccess: true });
    }

    // Check each limit
    for (const limit of feature.limits) {
      const limitReached = await hasReachedLimit(session.user.id, featureId, limit.id);

      if (limitReached) {
        return json({ hasAccess: false, limitReached: true, limitId: limit.id });
      }
    }

    // If no limits are reached, the user has access
    return json({ hasAccess: true });
  } catch (error) {
    console.error('Error checking feature access:', error);
    return json({ error: 'Failed to check feature access' }, { status: 500 });
  }
}

/**
 * Check if a user can use a specific feature limit
 * POST /api/feature-access
 * Body: { featureId: string, limitId: string }
 */
export async function POST({ request, locals }) {
  // Get the session from locals
  const session = locals.session;

  // Check if the user is authenticated
  if (!session?.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Get the request body
  const body = await request.json();
  const { featureId, limitId } = body;

  if (!featureId) {
    return json({ error: 'Feature ID is required' }, { status: 400 });
  }

  if (!limitId) {
    return json({ error: 'Limit ID is required' }, { status: 400 });
  }

  // Get the feature and limit
  const feature = getFeatureById(featureId);
  const limit = getFeatureLimitById(limitId);

  if (!feature) {
    return json({ error: 'Feature not found' }, { status: 404 });
  }

  if (!limit) {
    return json({ error: 'Limit not found' }, { status: 404 });
  }

  // Check if the user has access to the feature
  try {
    const limitReached = await hasReachedLimit(session.user.id, featureId, limitId);

    return json({
      hasAccess: !limitReached,
      limitReached,
      featureId,
      limitId,
      featureName: feature.name,
      limitName: limit.name,
    });
  } catch (error) {
    console.error('Error checking feature access:', error);
    return json({ error: 'Failed to check feature access' }, { status: 500 });
  }
}
