{"name": "auto-submit", "private": true, "version": "0.0.0", "scripts": {"dev": "start cmd /c \"vite dev\" && start cmd /c \"node ws-server.js\"", "dev:sanity": "cd ../marketing && npm run dev", "dev:all": "start cmd /c \"npm run dev:combined\" && start cmd /c \"npm run dev:sanity\"", "build": "vite build", "build:sanity": "node scripts/build-sanity-studio.js", "preview": "vite preview", "lint": "eslint . --ext .js,.ts,.svelte", "lint:fix": "eslint . --ext .js,.ts,.svelte --fix", "prepare": "svelte-kit sync || husky install", "prepack": "svelte-kit sync && svelte-package && publint", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "start:worker": "node ./workers/dist/index.js", "start": "node server.js", "format": "prettier --write .", "format:check": "prettier --check .", "fetch-plans": "node scripts/fetch-plans-from-stripe.js", "delete-plans": "node scripts/delete-all-plans.js", "reset-plans": "npm run delete-plans && npm run fetch-plans", "setup-feature-limits": "tsx scripts/setup-feature-limits.ts", "seed-feature-limits": "tsx scripts/seed-feature-limits.ts"}, "files": ["dist", "!dist/**/*.test.*", "!dist/**/*.spec.*"], "sideEffects": ["**/*.css"], "engines": {"node": ">=14.0.0", "npm": ">=7.0.0"}, "svelte": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "svelte": "./dist/index.js"}}, "peerDependencies": {"svelte": "^5.0.0"}, "devDependencies": {"@auth/core": "^0.39.0", "@auth/sveltekit": "^1.9.1", "@internationalized/date": "^3.8.0", "@lucide/svelte": "^0.482.0", "@rollup/plugin-node-resolve": "^16.0.1", "@stripe/stripe-js": "^4.10.0", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.0.0", "@sveltejs/package": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^4.0.4", "@tailwindcss/postcss": "^4.0.17", "@tailwindcss/vite": "^4.1.3", "@tiptap/core": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@tsconfig/svelte": "^5.0.4", "@types/bcryptjs": "^3.0.0", "@types/dotenv": "^6.1.1", "@types/ioredis": "^4.28.10", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.13.14", "@types/pdfjs-dist": "^2.10.377", "@types/stripe": "^8.0.417", "@types/user-agents": "^1.0.4", "@types/uuid": "^10.0.0", "@types/web-push": "^3.6.4", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "autoprefixer": "^10.4.21", "bits-ui": "^1.8.0", "concurrently": "^9.1.2", "embla-carousel": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-svelte": "^8.6.0", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-svelte": "^2.46.1", "formsnap": "^2.0.1", "globals": "^16.0.0", "husky": "^9.1.7", "layerchart": "^2.0.0-next.10", "lint-staged": "^15.5.0", "lucide-svelte": "^0.486.0", "mode-watcher": "^1.0.7", "nodemailer": "^6.10.0", "paneforge": "^1.0.0-next.5", "patch-package": "^8.0.0", "postcss": "^8.5.3", "postinstall-postinstall": "^2.1.0", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.5.14", "prisma": "^6.8.1", "puppeteer-cluster": "^0.24.0", "stripe": "^18.1.0", "svelte": "^5.25.12", "svelte-check": "^4.1.4", "svelte-forms-lib": "^2.0.1", "svelte-sonner": "^0.3.28", "svelte-stripe": "^1.3.0", "sveltekit-superforms": "^2.25.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.3", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.2", "typescript": "~5.7.2", "typescript-eslint": "^8.29.0", "web-push": "^3.6.7", "ws": "^8.18.2", "zod": "^3.24.3"}, "dependencies": {"@auth/prisma-adapter": "^2.9.0", "@botpress/client": "^1.5.0", "@graphql-tools/schema": "^10.0.23", "@nextlint/svelte": "^3.1.0", "@portabletext/types": "^2.0.8", "@prisma/client": "^6.8.1", "@react-oauth/google": "^0.12.1", "@sanity/client": "^6.29.1", "@simplewebauthn/browser": "^9.0.1", "@simplewebauthn/server": "^9.0.3", "@tanstack/table-core": "^8.21.3", "@tiptap/extension-bullet-list": "^2.11.7", "@tiptap/extension-list-item": "^2.11.7", "@tiptap/pm": "^2.11.7", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.8.0", "csv-parse": "^5.6.0", "date-fns": "^4.1.0", "docx-preview": "^0.3.5", "dotenv": "^16.4.7", "express": "^4.21.2", "file-saver": "^2.0.5", "google-auth-library": "^9.15.1", "graphql": "^16.10.0", "graphql-tag": "^2.12.6", "graphql-yoga": "^5.13.4", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "open": "^10.1.0", "openai": "^5.0.1", "otplib": "^12.0.1", "pdf2json": "^3.1.5", "pdfjs-dist": "^5.2.133", "playwright": "^1.51.1", "qrcode": "^1.5.4", "redis": "^4.7.0", "resend": "^4.4.1", "shadcn-svelte": "^0.14.0", "svelte": "^5.0.0", "svelte-dnd-action": "^0.9.60", "svelte-radix": "^2.0.1", "tailwind-merge": "^3.1.0", "ua-parser-js": "^2.0.3", "user-agents": "^1.1.492", "vite": "^6.3.5", "xlsx": "^0.18.5"}, "prisma": {"seed": "node prisma/seed.ts"}, "lint-staged": {"*.{ts,js,svelte,css,html,json}": "prettier --write"}, "postinstall": "patch-package", "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run check"}}, "overrides": {"svelte-forms-lib": {"exports": {".": {"svelte": "./index.js", "import": "./index.js", "require": "./index.js", "default": "./index.js"}}}, "radix-icons-svelte": {"exports": {".": {"svelte": "./index.js", "import": "./index.js", "require": "./index.js", "default": "./index.js"}}}}}