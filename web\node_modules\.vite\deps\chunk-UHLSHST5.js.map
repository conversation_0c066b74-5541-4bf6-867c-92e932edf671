{"version": 3, "sources": ["../../svelte/src/reactivity/utils.js", "../../svelte/src/reactivity/create-subscriber.js"], "sourcesContent": ["/** @import { Source } from '#client' */\nimport { set } from '../internal/client/reactivity/sources.js';\n\n/** @param {Source<number>} source */\nexport function increment(source) {\n\tset(source, source.v + 1);\n}\n", "import { get, tick, untrack } from '../internal/client/runtime.js';\nimport { effect_tracking, render_effect } from '../internal/client/reactivity/effects.js';\nimport { source } from '../internal/client/reactivity/sources.js';\nimport { increment } from './utils.js';\n\n/**\n * Returns a `subscribe` function that, if called in an effect (including expressions in the template),\n * calls its `start` callback with an `update` function. Whenever `update` is called, the effect re-runs.\n *\n * If `start` returns a function, it will be called when the effect is destroyed.\n *\n * If `subscribe` is called in multiple effects, `start` will only be called once as long as the effects\n * are active, and the returned teardown function will only be called when all effects are destroyed.\n *\n * It's best understood with an example. Here's an implementation of [`MediaQuery`](https://svelte.dev/docs/svelte/svelte-reactivity#MediaQuery):\n *\n * ```js\n * import { createSubscriber } from 'svelte/reactivity';\n * import { on } from 'svelte/events';\n *\n * export class MediaQuery {\n * \t#query;\n * \t#subscribe;\n *\n * \tconstructor(query) {\n * \t\tthis.#query = window.matchMedia(`(${query})`);\n *\n * \t\tthis.#subscribe = createSubscriber((update) => {\n * \t\t\t// when the `change` event occurs, re-run any effects that read `this.current`\n * \t\t\tconst off = on(this.#query, 'change', update);\n *\n * \t\t\t// stop listening when all the effects are destroyed\n * \t\t\treturn () => off();\n * \t\t});\n * \t}\n *\n * \tget current() {\n * \t\tthis.#subscribe();\n *\n * \t\t// Return the current state of the query, whether or not we're in an effect\n * \t\treturn this.#query.matches;\n * \t}\n * }\n * ```\n * @param {(update: () => void) => (() => void) | void} start\n * @since 5.7.0\n */\nexport function createSubscriber(start) {\n\tlet subscribers = 0;\n\tlet version = source(0);\n\t/** @type {(() => void) | void} */\n\tlet stop;\n\n\treturn () => {\n\t\tif (effect_tracking()) {\n\t\t\tget(version);\n\n\t\t\trender_effect(() => {\n\t\t\t\tif (subscribers === 0) {\n\t\t\t\t\tstop = untrack(() => start(() => increment(version)));\n\t\t\t\t}\n\n\t\t\t\tsubscribers += 1;\n\n\t\t\t\treturn () => {\n\t\t\t\t\ttick().then(() => {\n\t\t\t\t\t\t// Only count down after timeout, else we would reach 0 before our own render effect reruns,\n\t\t\t\t\t\t// but reach 1 again when the tick callback of the prior teardown runs. That would mean we\n\t\t\t\t\t\t// re-subcribe unnecessarily and create a memory leak because the old subscription is never cleaned up.\n\t\t\t\t\t\tsubscribers -= 1;\n\n\t\t\t\t\t\tif (subscribers === 0) {\n\t\t\t\t\t\t\tstop?.();\n\t\t\t\t\t\t\tstop = undefined;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t};\n\t\t\t});\n\t\t}\n\t};\n}\n"], "mappings": ";;;;;;;;;;;AAIO,SAAS,UAAUA,SAAQ;AACjC,MAAIA,SAAQA,QAAO,IAAI,CAAC;AACzB;;;ACyCO,SAAS,iBAAiB,OAAO;AACvC,MAAI,cAAc;AAClB,MAAI,UAAU,OAAO,CAAC;AAEtB,MAAI;AAEJ,SAAO,MAAM;AACZ,QAAI,gBAAgB,GAAG;AACtB,UAAI,OAAO;AAEX,oBAAc,MAAM;AACnB,YAAI,gBAAgB,GAAG;AACtB,iBAAO,QAAQ,MAAM,MAAM,MAAM,UAAU,OAAO,CAAC,CAAC;AAAA,QACrD;AAEA,uBAAe;AAEf,eAAO,MAAM;AACZ,eAAK,EAAE,KAAK,MAAM;AAIjB,2BAAe;AAEf,gBAAI,gBAAgB,GAAG;AACtB;AACA,qBAAO;AAAA,YACR;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AACD;", "names": ["source"]}