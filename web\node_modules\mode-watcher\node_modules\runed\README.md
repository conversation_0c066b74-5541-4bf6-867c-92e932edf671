# Runed

<!-- automd:badges license name="runed" color="green" github="svecosystem/runed" -->

[![npm version](https://flat.badgen.net/npm/v/runed?color=green)](https://npmjs.com/package/runed)
[![npm downloads](https://flat.badgen.net/npm/dm/runed?color=green)](https://npmjs.com/package/runed)
[![license](https://flat.badgen.net/github/license/svecosystem/runed?color=green)](https://github.com/svecosystem/runed/blob/main/LICENSE)

<!-- /automd -->

Runed provides utilities to power your applications using the magic of
[Svelte Runes](https://svelte.dev/blog/runes).

## Features

<!-- TODO -->

## Installation

Runed will be published to NPM once Svelte 5 is released.

## License

<!-- automd:contributors license=MIT author="huntabyte" -->

Published under the [MIT](https://github.com/svecosystem/runed/blob/main/LICENSE) license. Made by
[@tglide](https://github.com/tglide), [@huntabyte](https://github.com/huntabyte) and
[community](https://github.com/svecosystem/runed/graphs/contributors) 💛 <br><br>
<a href="https://github.com/svecosystem/runed/graphs/contributors">
<img src="https://contrib.rocks/image?repo=svecosystem/runed" /> </a>

<!-- /automd -->
