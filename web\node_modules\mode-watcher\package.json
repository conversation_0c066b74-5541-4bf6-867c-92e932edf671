{"name": "mode-watcher", "version": "1.0.7", "description": "SSR-friendly light and dark mode for SvelteKit", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/svecosystem/mode-watcher.git", "directory": "packages/mode-watcher"}, "exports": {".": {"types": "./dist/index.d.ts", "svelte": "./dist/index.js"}}, "files": ["dist", "!dist/**/*.test.*", "!dist/**/*.spec.*"], "peerDependencies": {"svelte": "^5.27.0"}, "devDependencies": {"@playwright/test": "^1.28.1", "@sveltejs/kit": "^2.20.7", "@sveltejs/package": "^2.3.11", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@svitejs/changesets-changelog-github-compact": "^1.1.0", "@testing-library/dom": "^10.3.1", "@testing-library/jest-dom": "^6.4.6", "@testing-library/svelte": "^5.2.0", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.14.10", "autoprefixer": "^10.4.14", "jsdom": "^24.1.0", "postcss": "^8.4.24", "postcss-load-config": "^4.0.1", "publint": "^0.1.9", "svelte": "^5.27.0", "svelte-check": "^4.1.6", "tailwindcss": "^3.3.2", "tslib": "^2.8.1", "typescript": "^5.8.3", "vite": "^6.3.0", "vitest": "^3.1.1", "vitest-localstorage-mock": "^0.1.2"}, "svelte": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "dependencies": {"runed": "^0.25.0", "svelte-toolbelt": "^0.7.1"}, "scripts": {"dev": "pnpm watch", "dev:sandbox": "vite", "build": "pnpm package", "preview": "vite preview", "package": "svelte-kit sync && svelte-package && publint", "test": "vitest", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "watch": "svelte-kit sync && svelte-package --watch"}}