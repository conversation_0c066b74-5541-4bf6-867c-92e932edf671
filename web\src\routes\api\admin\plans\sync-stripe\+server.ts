import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types.js';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';
import { syncPlanWithStripe } from '$lib/server/stripe';
import type { PlanTier } from '$lib/models/features/types';

export const POST: RequestHandler = async ({ cookies, request }) => {
  const token = cookies.get('auth_token');

  if (!token) return json({ error: 'Unauthorized', details: 'No token provided' }, { status: 401 });

  console.log('Verifying token:', token ? token.substring(0, 10) + '...' : 'no token');
  const userData = await verifySessionToken(token);
  console.log(
    'User data from token:',
    userData
      ? { id: userData.id, email: userData.email, role: userData.role, isAdmin: userData.isAdmin }
      : 'null'
  );

  if (!userData?.id) {
    console.log('Authentication failed: Invalid or expired token');
    return json({ error: 'Unauthorized', details: 'Invalid token' }, { status: 401 });
  }

  // Check if user is an admin
  console.log('Checking admin status for user ID:', userData.id);
  const user = await prisma.user.findUnique({
    where: { id: userData.id },
    select: { isAdmin: true, role: true, email: true },
  });

  console.log('User database record:', user);

  // Allow access if either isAdmin is true OR role is 'admin'
  if (!user) {
    console.log('User not found in database');
    return json({ error: 'Unauthorized', details: 'User not found' }, { status: 401 });
  }

  if (!user.isAdmin && user.role !== 'admin') {
    console.log('User is not an admin:', {
      userId: userData.id,
      email: user.email,
      isAdmin: user.isAdmin,
      role: user.role,
    });
    return json({ error: 'Unauthorized', details: 'User is not an admin' }, { status: 401 });
  }

  console.log('Admin check passed for user:', {
    userId: userData.id,
    email: user.email,
    isAdmin: user.isAdmin,
    role: user.role,
  });

  try {
    const data = await request.json();
    const plan = data.planId
      ? await prisma.plan.findUnique({
          where: { id: data.planId },
          include: {
            features: {
              include: {
                limits: true,
              },
            },
          },
        })
      : data.plan;

    if (!plan || !plan.id) {
      return json({ error: 'Invalid plan data' }, { status: 400 });
    }

    // Sync plan with Stripe
    const updatedPlan = await syncPlanWithStripe(plan);

    // Update the plan in the database with the new Stripe price IDs
    await prisma.plan.update({
      where: { id: plan.id },
      data: {
        stripePriceMonthlyId: updatedPlan.stripePriceMonthlyId,
        stripePriceYearlyId: updatedPlan.stripePriceYearlyId,
      },
    });

    return json({
      success: true,
      plan: updatedPlan,
      message: `Plan "${plan.name}" successfully synced with Stripe`,
    });
  } catch (error) {
    console.error('Error syncing plan with Stripe:', error);
    return json(
      {
        success: false,
        error: error.message,
      },
      { status: 500 }
    );
  }
};

// Sync all plans with Stripe
export const PUT: RequestHandler = async ({ cookies }) => {
  const token = cookies.get('auth_token');

  if (!token) return json({ error: 'Unauthorized', details: 'No token provided' }, { status: 401 });

  console.log(
    'Verifying token for PUT request:',
    token ? token.substring(0, 10) + '...' : 'no token'
  );
  const userData = await verifySessionToken(token);
  console.log(
    'User data from token (PUT):',
    userData
      ? { id: userData.id, email: userData.email, role: userData.role, isAdmin: userData.isAdmin }
      : 'null'
  );

  if (!userData?.id) {
    console.log('Authentication failed: Invalid or expired token (PUT)');
    return json({ error: 'Unauthorized', details: 'Invalid token' }, { status: 401 });
  }

  // Check if user is an admin
  const user = await prisma.user.findUnique({
    where: { id: userData.id },
    select: { isAdmin: true, role: true },
  });

  // Allow access if either isAdmin is true OR role is 'admin'
  if (!user) {
    return json({ error: 'Unauthorized', details: 'User not found' }, { status: 401 });
  }

  if (!user.isAdmin && user.role !== 'admin') {
    console.log('User is not an admin:', {
      userId: userData.id,
      isAdmin: user.isAdmin,
      role: user.role,
    });
    return json({ error: 'Unauthorized', details: 'User is not an admin' }, { status: 401 });
  }

  try {
    // Get all plans from the database
    const plans = await prisma.plan.findMany({
      where: {
        // Skip free plans (price = 0)
        OR: [{ monthlyPrice: { gt: 0 } }, { annualPrice: { gt: 0 } }],
      },
      include: {
        features: {
          include: {
            limits: true,
          },
        },
      },
    });

    // Convert database plans to PlanTier format
    const planTiers: PlanTier[] = plans.map((plan) => ({
      id: plan.id,
      name: plan.name,
      description: plan.description,
      section: plan.section,
      monthlyPrice: plan.monthlyPrice,
      annualPrice: plan.annualPrice,
      stripePriceMonthlyId: plan.stripePriceMonthlyId || undefined,
      stripePriceYearlyId: plan.stripePriceYearlyId || undefined,
      popular: plan.popular,
      features: plan.features.map((feature) => ({
        featureId: feature.featureId,
        accessLevel: feature.accessLevel,
        limits: feature.limits.map((limit) => ({
          limitId: limit.limitId,
          value: limit.value,
        })),
      })),
    }));

    // Sync each plan with Stripe
    const results = await Promise.all(
      planTiers.map(async (plan) => {
        try {
          const updatedPlan = await syncPlanWithStripe(plan);

          // Update the plan in the database with the new Stripe price IDs
          await prisma.plan.update({
            where: { id: plan.id },
            data: {
              stripePriceMonthlyId: updatedPlan.stripePriceMonthlyId,
              stripePriceYearlyId: updatedPlan.stripePriceYearlyId,
            },
          });

          return {
            id: plan.id,
            name: plan.name,
            success: true,
            stripePriceMonthlyId: updatedPlan.stripePriceMonthlyId,
            stripePriceYearlyId: updatedPlan.stripePriceYearlyId,
          };
        } catch (error) {
          return {
            id: plan.id,
            name: plan.name,
            success: false,
            error: error.message,
          };
        }
      })
    );

    return json({
      success: true,
      results,
      message: `${results.filter((r) => r.success).length} of ${results.length} plans successfully synced with Stripe`,
    });
  } catch (error) {
    console.error('Error syncing plans with Stripe:', error);
    return json(
      {
        success: false,
        error: error.message,
      },
      { status: 500 }
    );
  }
};
