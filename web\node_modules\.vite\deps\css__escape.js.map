{"version": 3, "sources": ["../../css.escape/css.escape.js"], "sourcesContent": ["/*! https://mths.be/cssescape v1.5.1 by @mathias | MIT license */\n;(function(root, factory) {\n\t// https://github.com/umdjs/umd/blob/master/returnExports.js\n\tif (typeof exports == 'object') {\n\t\t// For Node.js.\n\t\tmodule.exports = factory(root);\n\t} else if (typeof define == 'function' && define.amd) {\n\t\t// For AMD. Register as an anonymous module.\n\t\tdefine([], factory.bind(root, root));\n\t} else {\n\t\t// For browser globals (not exposing the function separately).\n\t\tfactory(root);\n\t}\n}(typeof global != 'undefined' ? global : this, function(root) {\n\n\tif (root.CSS && root.CSS.escape) {\n\t\treturn root.CSS.escape;\n\t}\n\n\t// https://drafts.csswg.org/cssom/#serialize-an-identifier\n\tvar cssEscape = function(value) {\n\t\tif (arguments.length == 0) {\n\t\t\tthrow new TypeError('`CSS.escape` requires an argument.');\n\t\t}\n\t\tvar string = String(value);\n\t\tvar length = string.length;\n\t\tvar index = -1;\n\t\tvar codeUnit;\n\t\tvar result = '';\n\t\tvar firstCodeUnit = string.charCodeAt(0);\n\t\twhile (++index < length) {\n\t\t\tcodeUnit = string.charCodeAt(index);\n\t\t\t// Note: there’s no need to special-case astral symbols, surrogate\n\t\t\t// pairs, or lone surrogates.\n\n\t\t\t// If the character is NULL (U+0000), then the REPLACEMENT CHARACTER\n\t\t\t// (U+FFFD).\n\t\t\tif (codeUnit == 0x0000) {\n\t\t\t\tresult += '\\uFFFD';\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\t// If the character is in the range [\\1-\\1F] (U+0001 to U+001F) or is\n\t\t\t\t// U+007F, […]\n\t\t\t\t(codeUnit >= 0x0001 && codeUnit <= 0x001F) || codeUnit == 0x007F ||\n\t\t\t\t// If the character is the first character and is in the range [0-9]\n\t\t\t\t// (U+0030 to U+0039), […]\n\t\t\t\t(index == 0 && codeUnit >= 0x0030 && codeUnit <= 0x0039) ||\n\t\t\t\t// If the character is the second character and is in the range [0-9]\n\t\t\t\t// (U+0030 to U+0039) and the first character is a `-` (U+002D), […]\n\t\t\t\t(\n\t\t\t\t\tindex == 1 &&\n\t\t\t\t\tcodeUnit >= 0x0030 && codeUnit <= 0x0039 &&\n\t\t\t\t\tfirstCodeUnit == 0x002D\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\t// https://drafts.csswg.org/cssom/#escape-a-character-as-code-point\n\t\t\t\tresult += '\\\\' + codeUnit.toString(16) + ' ';\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\t// If the character is the first character and is a `-` (U+002D), and\n\t\t\t\t// there is no second character, […]\n\t\t\t\tindex == 0 &&\n\t\t\t\tlength == 1 &&\n\t\t\t\tcodeUnit == 0x002D\n\t\t\t) {\n\t\t\t\tresult += '\\\\' + string.charAt(index);\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t// If the character is not handled by one of the above rules and is\n\t\t\t// greater than or equal to U+0080, is `-` (U+002D) or `_` (U+005F), or\n\t\t\t// is in one of the ranges [0-9] (U+0030 to U+0039), [A-Z] (U+0041 to\n\t\t\t// U+005A), or [a-z] (U+0061 to U+007A), […]\n\t\t\tif (\n\t\t\t\tcodeUnit >= 0x0080 ||\n\t\t\t\tcodeUnit == 0x002D ||\n\t\t\t\tcodeUnit == 0x005F ||\n\t\t\t\tcodeUnit >= 0x0030 && codeUnit <= 0x0039 ||\n\t\t\t\tcodeUnit >= 0x0041 && codeUnit <= 0x005A ||\n\t\t\t\tcodeUnit >= 0x0061 && codeUnit <= 0x007A\n\t\t\t) {\n\t\t\t\t// the character itself\n\t\t\t\tresult += string.charAt(index);\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t// Otherwise, the escaped character.\n\t\t\t// https://drafts.csswg.org/cssom/#escape-a-character\n\t\t\tresult += '\\\\' + string.charAt(index);\n\n\t\t}\n\t\treturn result;\n\t};\n\n\tif (!root.CSS) {\n\t\troot.CSS = {};\n\t}\n\n\troot.CSS.escape = cssEscape;\n\treturn cssEscape;\n\n}));\n"], "mappings": ";;;;;AAAA;AAAA;AACC,KAAC,SAAS,MAAM,SAAS;AAEzB,UAAI,OAAO,WAAW,UAAU;AAE/B,eAAO,UAAU,QAAQ,IAAI;AAAA,MAC9B,WAAW,OAAO,UAAU,cAAc,OAAO,KAAK;AAErD,eAAO,CAAC,GAAG,QAAQ,KAAK,MAAM,IAAI,CAAC;AAAA,MACpC,OAAO;AAEN,gBAAQ,IAAI;AAAA,MACb;AAAA,IACD,GAAE,OAAO,UAAU,cAAc,SAAS,SAAM,SAAS,MAAM;AAE9D,UAAI,KAAK,OAAO,KAAK,IAAI,QAAQ;AAChC,eAAO,KAAK,IAAI;AAAA,MACjB;AAGA,UAAI,YAAY,SAAS,OAAO;AAC/B,YAAI,UAAU,UAAU,GAAG;AAC1B,gBAAM,IAAI,UAAU,oCAAoC;AAAA,QACzD;AACA,YAAI,SAAS,OAAO,KAAK;AACzB,YAAI,SAAS,OAAO;AACpB,YAAI,QAAQ;AACZ,YAAI;AACJ,YAAI,SAAS;AACb,YAAI,gBAAgB,OAAO,WAAW,CAAC;AACvC,eAAO,EAAE,QAAQ,QAAQ;AACxB,qBAAW,OAAO,WAAW,KAAK;AAMlC,cAAI,YAAY,GAAQ;AACvB,sBAAU;AACV;AAAA,UACD;AAEA;AAAA;AAAA;AAAA,YAGE,YAAY,KAAU,YAAY,MAAW,YAAY;AAAA;AAAA,YAGzD,SAAS,KAAK,YAAY,MAAU,YAAY;AAAA;AAAA,YAIhD,SAAS,KACT,YAAY,MAAU,YAAY,MAClC,iBAAiB;AAAA,YAEjB;AAED,sBAAU,OAAO,SAAS,SAAS,EAAE,IAAI;AACzC;AAAA,UACD;AAEA;AAAA;AAAA;AAAA,YAGC,SAAS,KACT,UAAU,KACV,YAAY;AAAA,YACX;AACD,sBAAU,OAAO,OAAO,OAAO,KAAK;AACpC;AAAA,UACD;AAMA,cACC,YAAY,OACZ,YAAY,MACZ,YAAY,MACZ,YAAY,MAAU,YAAY,MAClC,YAAY,MAAU,YAAY,MAClC,YAAY,MAAU,YAAY,KACjC;AAED,sBAAU,OAAO,OAAO,KAAK;AAC7B;AAAA,UACD;AAIA,oBAAU,OAAO,OAAO,OAAO,KAAK;AAAA,QAErC;AACA,eAAO;AAAA,MACR;AAEA,UAAI,CAAC,KAAK,KAAK;AACd,aAAK,MAAM,CAAC;AAAA,MACb;AAEA,WAAK,IAAI,SAAS;AAClB,aAAO;AAAA,IAER,CAAC;AAAA;AAAA;", "names": []}