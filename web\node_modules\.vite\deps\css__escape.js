import {
  __commonJS
} from "./chunk-NNIHVWYK.js";

// node_modules/css.escape/css.escape.js
var require_css_escape = __commonJS({
  "node_modules/css.escape/css.escape.js"(exports, module) {
    (function(root, factory) {
      if (typeof exports == "object") {
        module.exports = factory(root);
      } else if (typeof define == "function" && define.amd) {
        define([], factory.bind(root, root));
      } else {
        factory(root);
      }
    })(typeof global != "undefined" ? global : exports, function(root) {
      if (root.CSS && root.CSS.escape) {
        return root.CSS.escape;
      }
      var cssEscape = function(value) {
        if (arguments.length == 0) {
          throw new TypeError("`CSS.escape` requires an argument.");
        }
        var string = String(value);
        var length = string.length;
        var index = -1;
        var codeUnit;
        var result = "";
        var firstCodeUnit = string.charCodeAt(0);
        while (++index < length) {
          codeUnit = string.charCodeAt(index);
          if (codeUnit == 0) {
            result += "�";
            continue;
          }
          if (
            // If the character is in the range [\1-\1F] (U+0001 to U+001F) or is
            // U+007F, […]
            codeUnit >= 1 && codeUnit <= 31 || codeUnit == 127 || // If the character is the first character and is in the range [0-9]
            // (U+0030 to U+0039), […]
            index == 0 && codeUnit >= 48 && codeUnit <= 57 || // If the character is the second character and is in the range [0-9]
            // (U+0030 to U+0039) and the first character is a `-` (U+002D), […]
            index == 1 && codeUnit >= 48 && codeUnit <= 57 && firstCodeUnit == 45
          ) {
            result += "\\" + codeUnit.toString(16) + " ";
            continue;
          }
          if (
            // If the character is the first character and is a `-` (U+002D), and
            // there is no second character, […]
            index == 0 && length == 1 && codeUnit == 45
          ) {
            result += "\\" + string.charAt(index);
            continue;
          }
          if (codeUnit >= 128 || codeUnit == 45 || codeUnit == 95 || codeUnit >= 48 && codeUnit <= 57 || codeUnit >= 65 && codeUnit <= 90 || codeUnit >= 97 && codeUnit <= 122) {
            result += string.charAt(index);
            continue;
          }
          result += "\\" + string.charAt(index);
        }
        return result;
      };
      if (!root.CSS) {
        root.CSS = {};
      }
      root.CSS.escape = cssEscape;
      return cssEscape;
    });
  }
});
export default require_css_escape();
/*! Bundled license information:

css.escape/css.escape.js:
  (*! https://mths.be/cssescape v1.5.1 by @mathias | MIT license *)
*/
//# sourceMappingURL=css__escape.js.map
