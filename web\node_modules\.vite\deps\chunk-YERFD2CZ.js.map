{"version": 3, "sources": ["../../svelte/src/easing/index.js"], "sourcesContent": ["/*\nAdapted from https://github.com/mattdesl\nDistributed under MIT License https://github.com/mattdesl/eases/blob/master/LICENSE.md\n*/\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function linear(t) {\n\treturn t;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function backInOut(t) {\n\tconst s = 1.70158 * 1.525;\n\tif ((t *= 2) < 1) return 0.5 * (t * t * ((s + 1) * t - s));\n\treturn 0.5 * ((t -= 2) * t * ((s + 1) * t + s) + 2);\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function backIn(t) {\n\tconst s = 1.70158;\n\treturn t * t * ((s + 1) * t - s);\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function backOut(t) {\n\tconst s = 1.70158;\n\treturn --t * t * ((s + 1) * t + s) + 1;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function bounceOut(t) {\n\tconst a = 4.0 / 11.0;\n\tconst b = 8.0 / 11.0;\n\tconst c = 9.0 / 10.0;\n\tconst ca = 4356.0 / 361.0;\n\tconst cb = 35442.0 / 1805.0;\n\tconst cc = 16061.0 / 1805.0;\n\tconst t2 = t * t;\n\treturn t < a\n\t\t? 7.5625 * t2\n\t\t: t < b\n\t\t\t? 9.075 * t2 - 9.9 * t + 3.4\n\t\t\t: t < c\n\t\t\t\t? ca * t2 - cb * t + cc\n\t\t\t\t: 10.8 * t * t - 20.52 * t + 10.72;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function bounceInOut(t) {\n\treturn t < 0.5 ? 0.5 * (1.0 - bounceOut(1.0 - t * 2.0)) : 0.5 * bounceOut(t * 2.0 - 1.0) + 0.5;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function bounceIn(t) {\n\treturn 1.0 - bounceOut(1.0 - t);\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function circInOut(t) {\n\tif ((t *= 2) < 1) return -0.5 * (Math.sqrt(1 - t * t) - 1);\n\treturn 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1);\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function circIn(t) {\n\treturn 1.0 - Math.sqrt(1.0 - t * t);\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function circOut(t) {\n\treturn Math.sqrt(1 - --t * t);\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function cubicInOut(t) {\n\treturn t < 0.5 ? 4.0 * t * t * t : 0.5 * Math.pow(2.0 * t - 2.0, 3.0) + 1.0;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function cubicIn(t) {\n\treturn t * t * t;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function cubicOut(t) {\n\tconst f = t - 1.0;\n\treturn f * f * f + 1.0;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function elasticInOut(t) {\n\treturn t < 0.5\n\t\t? 0.5 * Math.sin(((+13.0 * Math.PI) / 2) * 2.0 * t) * Math.pow(2.0, 10.0 * (2.0 * t - 1.0))\n\t\t: 0.5 *\n\t\t\t\tMath.sin(((-13.0 * Math.PI) / 2) * (2.0 * t - 1.0 + 1.0)) *\n\t\t\t\tMath.pow(2.0, -10.0 * (2.0 * t - 1.0)) +\n\t\t\t\t1.0;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function elasticIn(t) {\n\treturn Math.sin((13.0 * t * Math.PI) / 2) * Math.pow(2.0, 10.0 * (t - 1.0));\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function elasticOut(t) {\n\treturn Math.sin((-13.0 * (t + 1.0) * Math.PI) / 2) * Math.pow(2.0, -10.0 * t) + 1.0;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function expoInOut(t) {\n\treturn t === 0.0 || t === 1.0\n\t\t? t\n\t\t: t < 0.5\n\t\t\t? +0.5 * Math.pow(2.0, 20.0 * t - 10.0)\n\t\t\t: -0.5 * Math.pow(2.0, 10.0 - t * 20.0) + 1.0;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function expoIn(t) {\n\treturn t === 0.0 ? t : Math.pow(2.0, 10.0 * (t - 1.0));\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function expoOut(t) {\n\treturn t === 1.0 ? t : 1.0 - Math.pow(2.0, -10.0 * t);\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function quadInOut(t) {\n\tt /= 0.5;\n\tif (t < 1) return 0.5 * t * t;\n\tt--;\n\treturn -0.5 * (t * (t - 2) - 1);\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function quadIn(t) {\n\treturn t * t;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function quadOut(t) {\n\treturn -t * (t - 2.0);\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function quartInOut(t) {\n\treturn t < 0.5 ? +8.0 * Math.pow(t, 4.0) : -8.0 * Math.pow(t - 1.0, 4.0) + 1.0;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function quartIn(t) {\n\treturn Math.pow(t, 4.0);\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function quartOut(t) {\n\treturn Math.pow(t - 1.0, 3.0) * (1.0 - t) + 1.0;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function quintInOut(t) {\n\tif ((t *= 2) < 1) return 0.5 * t * t * t * t * t;\n\treturn 0.5 * ((t -= 2) * t * t * t * t + 2);\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function quintIn(t) {\n\treturn t * t * t * t * t;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function quintOut(t) {\n\treturn --t * t * t * t * t + 1;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function sineInOut(t) {\n\treturn -0.5 * (Math.cos(Math.PI * t) - 1);\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function sineIn(t) {\n\tconst v = Math.cos(t * Math.PI * 0.5);\n\tif (Math.abs(v) < 1e-14) return 1;\n\telse return 1 - v;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nexport function sineOut(t) {\n\treturn Math.sin((t * Math.PI) / 2);\n}\n"], "mappings": ";AASO,SAAS,OAAO,GAAG;AACzB,SAAO;AACR;AAMO,SAAS,UAAU,GAAG;AAC5B,QAAM,IAAI,UAAU;AACpB,OAAK,KAAK,KAAK,EAAG,QAAO,OAAO,IAAI,MAAM,IAAI,KAAK,IAAI;AACvD,SAAO,QAAQ,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AAClD;AAMO,SAAS,OAAO,GAAG;AACzB,QAAM,IAAI;AACV,SAAO,IAAI,MAAM,IAAI,KAAK,IAAI;AAC/B;AAMO,SAAS,QAAQ,GAAG;AAC1B,QAAM,IAAI;AACV,SAAO,EAAE,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK;AACtC;AAMO,SAAS,UAAU,GAAG;AAC5B,QAAM,IAAI,IAAM;AAChB,QAAM,IAAI,IAAM;AAChB,QAAM,IAAI,IAAM;AAChB,QAAM,KAAK,OAAS;AACpB,QAAM,KAAK,QAAU;AACrB,QAAM,KAAK,QAAU;AACrB,QAAM,KAAK,IAAI;AACf,SAAO,IAAI,IACR,SAAS,KACT,IAAI,IACH,QAAQ,KAAK,MAAM,IAAI,MACvB,IAAI,IACH,KAAK,KAAK,KAAK,IAAI,KACnB,OAAO,IAAI,IAAI,QAAQ,IAAI;AACjC;AAMO,SAAS,YAAY,GAAG;AAC9B,SAAO,IAAI,MAAM,OAAO,IAAM,UAAU,IAAM,IAAI,CAAG,KAAK,MAAM,UAAU,IAAI,IAAM,CAAG,IAAI;AAC5F;AAMO,SAAS,SAAS,GAAG;AAC3B,SAAO,IAAM,UAAU,IAAM,CAAC;AAC/B;AAMO,SAAS,UAAU,GAAG;AAC5B,OAAK,KAAK,KAAK,EAAG,QAAO,QAAQ,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI;AACxD,SAAO,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI;AAC7C;AAMO,SAAS,OAAO,GAAG;AACzB,SAAO,IAAM,KAAK,KAAK,IAAM,IAAI,CAAC;AACnC;AAMO,SAAS,QAAQ,GAAG;AAC1B,SAAO,KAAK,KAAK,IAAI,EAAE,IAAI,CAAC;AAC7B;AAMO,SAAS,WAAW,GAAG;AAC7B,SAAO,IAAI,MAAM,IAAM,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,IAAM,IAAI,GAAK,CAAG,IAAI;AACzE;AAMO,SAAS,QAAQ,GAAG;AAC1B,SAAO,IAAI,IAAI;AAChB;AAMO,SAAS,SAAS,GAAG;AAC3B,QAAM,IAAI,IAAI;AACd,SAAO,IAAI,IAAI,IAAI;AACpB;AAMO,SAAS,aAAa,GAAG;AAC/B,SAAO,IAAI,MACR,MAAM,KAAK,IAAM,KAAQ,KAAK,KAAM,IAAK,IAAM,CAAC,IAAI,KAAK,IAAI,GAAK,MAAQ,IAAM,IAAI,EAAI,IACxF,MACA,KAAK,IAAM,MAAQ,KAAK,KAAM,KAAM,IAAM,IAAI,IAAM,EAAI,IACxD,KAAK,IAAI,GAAK,OAAS,IAAM,IAAI,EAAI,IACrC;AACJ;AAMO,SAAS,UAAU,GAAG;AAC5B,SAAO,KAAK,IAAK,KAAO,IAAI,KAAK,KAAM,CAAC,IAAI,KAAK,IAAI,GAAK,MAAQ,IAAI,EAAI;AAC3E;AAMO,SAAS,WAAW,GAAG;AAC7B,SAAO,KAAK,IAAK,OAAS,IAAI,KAAO,KAAK,KAAM,CAAC,IAAI,KAAK,IAAI,GAAK,MAAQ,CAAC,IAAI;AACjF;AAMO,SAAS,UAAU,GAAG;AAC5B,SAAO,MAAM,KAAO,MAAM,IACvB,IACA,IAAI,MACH,MAAO,KAAK,IAAI,GAAK,KAAO,IAAI,EAAI,IACpC,OAAO,KAAK,IAAI,GAAK,KAAO,IAAI,EAAI,IAAI;AAC7C;AAMO,SAAS,OAAO,GAAG;AACzB,SAAO,MAAM,IAAM,IAAI,KAAK,IAAI,GAAK,MAAQ,IAAI,EAAI;AACtD;AAMO,SAAS,QAAQ,GAAG;AAC1B,SAAO,MAAM,IAAM,IAAI,IAAM,KAAK,IAAI,GAAK,MAAQ,CAAC;AACrD;AAMO,SAAS,UAAU,GAAG;AAC5B,OAAK;AACL,MAAI,IAAI,EAAG,QAAO,MAAM,IAAI;AAC5B;AACA,SAAO,QAAQ,KAAK,IAAI,KAAK;AAC9B;AAMO,SAAS,OAAO,GAAG;AACzB,SAAO,IAAI;AACZ;AAMO,SAAS,QAAQ,GAAG;AAC1B,SAAO,CAAC,KAAK,IAAI;AAClB;AAMO,SAAS,WAAW,GAAG;AAC7B,SAAO,IAAI,MAAM,IAAO,KAAK,IAAI,GAAG,CAAG,IAAI,KAAO,KAAK,IAAI,IAAI,GAAK,CAAG,IAAI;AAC5E;AAMO,SAAS,QAAQ,GAAG;AAC1B,SAAO,KAAK,IAAI,GAAG,CAAG;AACvB;AAMO,SAAS,SAAS,GAAG;AAC3B,SAAO,KAAK,IAAI,IAAI,GAAK,CAAG,KAAK,IAAM,KAAK;AAC7C;AAMO,SAAS,WAAW,GAAG;AAC7B,OAAK,KAAK,KAAK,EAAG,QAAO,MAAM,IAAI,IAAI,IAAI,IAAI;AAC/C,SAAO,QAAQ,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI;AAC1C;AAMO,SAAS,QAAQ,GAAG;AAC1B,SAAO,IAAI,IAAI,IAAI,IAAI;AACxB;AAMO,SAAS,SAAS,GAAG;AAC3B,SAAO,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI;AAC9B;AAMO,SAAS,UAAU,GAAG;AAC5B,SAAO,QAAQ,KAAK,IAAI,KAAK,KAAK,CAAC,IAAI;AACxC;AAMO,SAAS,OAAO,GAAG;AACzB,QAAM,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,GAAG;AACpC,MAAI,KAAK,IAAI,CAAC,IAAI,MAAO,QAAO;AAAA,MAC3B,QAAO,IAAI;AACjB;AAMO,SAAS,QAAQ,GAAG;AAC1B,SAAO,KAAK,IAAK,IAAI,KAAK,KAAM,CAAC;AAClC;", "names": []}