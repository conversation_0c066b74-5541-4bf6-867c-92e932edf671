{"version": 3, "sources": ["../../csv-parse/lib/api/CsvError.js", "../../csv-parse/lib/utils/is_object.js", "../../csv-parse/lib/api/normalize_columns_array.js", "../../csv-parse/lib/utils/ResizeableBuffer.js", "../../csv-parse/lib/api/init_state.js", "../../csv-parse/lib/utils/underscore.js", "../../csv-parse/lib/api/normalize_options.js", "../../csv-parse/lib/api/index.js", "../../csv-parse/lib/sync.js"], "sourcesContent": ["class CsvError extends Error {\n  constructor(code, message, options, ...contexts) {\n    if (Array.isArray(message)) message = message.join(\" \").trim();\n    super(message);\n    if (Error.captureStackTrace !== undefined) {\n      Error.captureStackTrace(this, CsvError);\n    }\n    this.code = code;\n    for (const context of contexts) {\n      for (const key in context) {\n        const value = context[key];\n        this[key] = Buffer.isBuffer(value)\n          ? value.toString(options.encoding)\n          : value == null\n            ? value\n            : JSON.parse(JSON.stringify(value));\n      }\n    }\n  }\n}\n\nexport { CsvError };\n", "const is_object = function (obj) {\n  return typeof obj === \"object\" && obj !== null && !Array.isArray(obj);\n};\n\nexport { is_object };\n", "import { CsvError } from \"./CsvError.js\";\nimport { is_object } from \"../utils/is_object.js\";\n\nconst normalize_columns_array = function (columns) {\n  const normalizedColumns = [];\n  for (let i = 0, l = columns.length; i < l; i++) {\n    const column = columns[i];\n    if (column === undefined || column === null || column === false) {\n      normalizedColumns[i] = { disabled: true };\n    } else if (typeof column === \"string\") {\n      normalizedColumns[i] = { name: column };\n    } else if (is_object(column)) {\n      if (typeof column.name !== \"string\") {\n        throw new CsvError(\"CSV_OPTION_COLUMNS_MISSING_NAME\", [\n          \"Option columns missing name:\",\n          `property \"name\" is required at position ${i}`,\n          \"when column is an object literal\",\n        ]);\n      }\n      normalizedColumns[i] = column;\n    } else {\n      throw new CsvError(\"CSV_INVALID_COLUMN_DEFINITION\", [\n        \"Invalid column definition:\",\n        \"expect a string or a literal object,\",\n        `got ${JSON.stringify(column)} at position ${i}`,\n      ]);\n    }\n  }\n  return normalizedColumns;\n};\n\nexport { normalize_columns_array };\n", "class ResizeableBuffer {\n  constructor(size = 100) {\n    this.size = size;\n    this.length = 0;\n    this.buf = Buffer.allocUnsafe(size);\n  }\n  prepend(val) {\n    if (Buffer.isBuffer(val)) {\n      const length = this.length + val.length;\n      if (length >= this.size) {\n        this.resize();\n        if (length >= this.size) {\n          throw Error(\"INVALID_BUFFER_STATE\");\n        }\n      }\n      const buf = this.buf;\n      this.buf = Buffer.allocUnsafe(this.size);\n      val.copy(this.buf, 0);\n      buf.copy(this.buf, val.length);\n      this.length += val.length;\n    } else {\n      const length = this.length++;\n      if (length === this.size) {\n        this.resize();\n      }\n      const buf = this.clone();\n      this.buf[0] = val;\n      buf.copy(this.buf, 1, 0, length);\n    }\n  }\n  append(val) {\n    const length = this.length++;\n    if (length === this.size) {\n      this.resize();\n    }\n    this.buf[length] = val;\n  }\n  clone() {\n    return Buffer.from(this.buf.slice(0, this.length));\n  }\n  resize() {\n    const length = this.length;\n    this.size = this.size * 2;\n    const buf = Buffer.allocUnsafe(this.size);\n    this.buf.copy(buf, 0, 0, length);\n    this.buf = buf;\n  }\n  toString(encoding) {\n    if (encoding) {\n      return this.buf.slice(0, this.length).toString(encoding);\n    } else {\n      return Uint8Array.prototype.slice.call(this.buf.slice(0, this.length));\n    }\n  }\n  toJSON() {\n    return this.toString(\"utf8\");\n  }\n  reset() {\n    this.length = 0;\n  }\n}\n\nexport default ResizeableBuffer;\n", "import ResizeableBuffer from \"../utils/ResizeableBuffer.js\";\n\n// white space characters\n// https://en.wikipedia.org/wiki/Whitespace_character\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions/Character_Classes#Types\n// \\f\\n\\r\\t\\v\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff\nconst np = 12;\nconst cr = 13; // `\\r`, carriage return, 0x0D in hexadécimal, 13 in decimal\nconst nl = 10; // `\\n`, newline, 0x0A in hexadecimal, 10 in decimal\nconst space = 32;\nconst tab = 9;\n\nconst init_state = function (options) {\n  return {\n    bomSkipped: false,\n    bufBytesStart: 0,\n    castField: options.cast_function,\n    commenting: false,\n    // Current error encountered by a record\n    error: undefined,\n    enabled: options.from_line === 1,\n    escaping: false,\n    escapeIsQuote:\n      Buffer.isBuffer(options.escape) &&\n      Buffer.isBuffer(options.quote) &&\n      Buffer.compare(options.escape, options.quote) === 0,\n    // columns can be `false`, `true`, `Array`\n    expectedRecordLength: Array.isArray(options.columns)\n      ? options.columns.length\n      : undefined,\n    field: new ResizeableBuffer(20),\n    firstLineToHeaders: options.cast_first_line_to_header,\n    needMoreDataSize: Math.max(\n      // Skip if the remaining buffer smaller than comment\n      options.comment !== null ? options.comment.length : 0,\n      // Skip if the remaining buffer can be delimiter\n      ...options.delimiter.map((delimiter) => delimiter.length),\n      // Skip if the remaining buffer can be escape sequence\n      options.quote !== null ? options.quote.length : 0,\n    ),\n    previousBuf: undefined,\n    quoting: false,\n    stop: false,\n    rawBuffer: new ResizeableBuffer(100),\n    record: [],\n    recordHasError: false,\n    record_length: 0,\n    recordDelimiterMaxLength:\n      options.record_delimiter.length === 0\n        ? 0\n        : Math.max(...options.record_delimiter.map((v) => v.length)),\n    trimChars: [\n      Buffer.from(\" \", options.encoding)[0],\n      Buffer.from(\"\\t\", options.encoding)[0],\n    ],\n    wasQuoting: false,\n    wasRowDelimiter: false,\n    timchars: [\n      Buffer.from(Buffer.from([cr], \"utf8\").toString(), options.encoding),\n      Buffer.from(Buffer.from([nl], \"utf8\").toString(), options.encoding),\n      Buffer.from(Buffer.from([np], \"utf8\").toString(), options.encoding),\n      Buffer.from(Buffer.from([space], \"utf8\").toString(), options.encoding),\n      Buffer.from(Buffer.from([tab], \"utf8\").toString(), options.encoding),\n    ],\n  };\n};\n\nexport { init_state };\n", "const underscore = function (str) {\n  return str.replace(/([A-Z])/g, function (_, match) {\n    return \"_\" + match.toLowerCase();\n  });\n};\n\nexport { underscore };\n", "import { normalize_columns_array } from \"./normalize_columns_array.js\";\nimport { CsvError } from \"./CsvError.js\";\nimport { underscore } from \"../utils/underscore.js\";\n\nconst normalize_options = function (opts) {\n  const options = {};\n  // Merge with user options\n  for (const opt in opts) {\n    options[underscore(opt)] = opts[opt];\n  }\n  // Normalize option `encoding`\n  // Note: defined first because other options depends on it\n  // to convert chars/strings into buffers.\n  if (options.encoding === undefined || options.encoding === true) {\n    options.encoding = \"utf8\";\n  } else if (options.encoding === null || options.encoding === false) {\n    options.encoding = null;\n  } else if (\n    typeof options.encoding !== \"string\" &&\n    options.encoding !== null\n  ) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_ENCODING\",\n      [\n        \"Invalid option encoding:\",\n        \"encoding must be a string or null to return a buffer,\",\n        `got ${JSON.stringify(options.encoding)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `bom`\n  if (\n    options.bom === undefined ||\n    options.bom === null ||\n    options.bom === false\n  ) {\n    options.bom = false;\n  } else if (options.bom !== true) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_BOM\",\n      [\n        \"Invalid option bom:\",\n        \"bom must be true,\",\n        `got ${JSON.stringify(options.bom)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `cast`\n  options.cast_function = null;\n  if (\n    options.cast === undefined ||\n    options.cast === null ||\n    options.cast === false ||\n    options.cast === \"\"\n  ) {\n    options.cast = undefined;\n  } else if (typeof options.cast === \"function\") {\n    options.cast_function = options.cast;\n    options.cast = true;\n  } else if (options.cast !== true) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_CAST\",\n      [\n        \"Invalid option cast:\",\n        \"cast must be true or a function,\",\n        `got ${JSON.stringify(options.cast)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `cast_date`\n  if (\n    options.cast_date === undefined ||\n    options.cast_date === null ||\n    options.cast_date === false ||\n    options.cast_date === \"\"\n  ) {\n    options.cast_date = false;\n  } else if (options.cast_date === true) {\n    options.cast_date = function (value) {\n      const date = Date.parse(value);\n      return !isNaN(date) ? new Date(date) : value;\n    };\n  } else if (typeof options.cast_date !== \"function\") {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_CAST_DATE\",\n      [\n        \"Invalid option cast_date:\",\n        \"cast_date must be true or a function,\",\n        `got ${JSON.stringify(options.cast_date)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `columns`\n  options.cast_first_line_to_header = null;\n  if (options.columns === true) {\n    // Fields in the first line are converted as-is to columns\n    options.cast_first_line_to_header = undefined;\n  } else if (typeof options.columns === \"function\") {\n    options.cast_first_line_to_header = options.columns;\n    options.columns = true;\n  } else if (Array.isArray(options.columns)) {\n    options.columns = normalize_columns_array(options.columns);\n  } else if (\n    options.columns === undefined ||\n    options.columns === null ||\n    options.columns === false\n  ) {\n    options.columns = false;\n  } else {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_COLUMNS\",\n      [\n        \"Invalid option columns:\",\n        \"expect an array, a function or true,\",\n        `got ${JSON.stringify(options.columns)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `group_columns_by_name`\n  if (\n    options.group_columns_by_name === undefined ||\n    options.group_columns_by_name === null ||\n    options.group_columns_by_name === false\n  ) {\n    options.group_columns_by_name = false;\n  } else if (options.group_columns_by_name !== true) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_GROUP_COLUMNS_BY_NAME\",\n      [\n        \"Invalid option group_columns_by_name:\",\n        \"expect an boolean,\",\n        `got ${JSON.stringify(options.group_columns_by_name)}`,\n      ],\n      options,\n    );\n  } else if (options.columns === false) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_GROUP_COLUMNS_BY_NAME\",\n      [\n        \"Invalid option group_columns_by_name:\",\n        \"the `columns` mode must be activated.\",\n      ],\n      options,\n    );\n  }\n  // Normalize option `comment`\n  if (\n    options.comment === undefined ||\n    options.comment === null ||\n    options.comment === false ||\n    options.comment === \"\"\n  ) {\n    options.comment = null;\n  } else {\n    if (typeof options.comment === \"string\") {\n      options.comment = Buffer.from(options.comment, options.encoding);\n    }\n    if (!Buffer.isBuffer(options.comment)) {\n      throw new CsvError(\n        \"CSV_INVALID_OPTION_COMMENT\",\n        [\n          \"Invalid option comment:\",\n          \"comment must be a buffer or a string,\",\n          `got ${JSON.stringify(options.comment)}`,\n        ],\n        options,\n      );\n    }\n  }\n  // Normalize option `comment_no_infix`\n  if (\n    options.comment_no_infix === undefined ||\n    options.comment_no_infix === null ||\n    options.comment_no_infix === false\n  ) {\n    options.comment_no_infix = false;\n  } else if (options.comment_no_infix !== true) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_COMMENT\",\n      [\n        \"Invalid option comment_no_infix:\",\n        \"value must be a boolean,\",\n        `got ${JSON.stringify(options.comment_no_infix)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `delimiter`\n  const delimiter_json = JSON.stringify(options.delimiter);\n  if (!Array.isArray(options.delimiter))\n    options.delimiter = [options.delimiter];\n  if (options.delimiter.length === 0) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_DELIMITER\",\n      [\n        \"Invalid option delimiter:\",\n        \"delimiter must be a non empty string or buffer or array of string|buffer,\",\n        `got ${delimiter_json}`,\n      ],\n      options,\n    );\n  }\n  options.delimiter = options.delimiter.map(function (delimiter) {\n    if (delimiter === undefined || delimiter === null || delimiter === false) {\n      return Buffer.from(\",\", options.encoding);\n    }\n    if (typeof delimiter === \"string\") {\n      delimiter = Buffer.from(delimiter, options.encoding);\n    }\n    if (!Buffer.isBuffer(delimiter) || delimiter.length === 0) {\n      throw new CsvError(\n        \"CSV_INVALID_OPTION_DELIMITER\",\n        [\n          \"Invalid option delimiter:\",\n          \"delimiter must be a non empty string or buffer or array of string|buffer,\",\n          `got ${delimiter_json}`,\n        ],\n        options,\n      );\n    }\n    return delimiter;\n  });\n  // Normalize option `escape`\n  if (options.escape === undefined || options.escape === true) {\n    options.escape = Buffer.from('\"', options.encoding);\n  } else if (typeof options.escape === \"string\") {\n    options.escape = Buffer.from(options.escape, options.encoding);\n  } else if (options.escape === null || options.escape === false) {\n    options.escape = null;\n  }\n  if (options.escape !== null) {\n    if (!Buffer.isBuffer(options.escape)) {\n      throw new Error(\n        `Invalid Option: escape must be a buffer, a string or a boolean, got ${JSON.stringify(options.escape)}`,\n      );\n    }\n  }\n  // Normalize option `from`\n  if (options.from === undefined || options.from === null) {\n    options.from = 1;\n  } else {\n    if (typeof options.from === \"string\" && /\\d+/.test(options.from)) {\n      options.from = parseInt(options.from);\n    }\n    if (Number.isInteger(options.from)) {\n      if (options.from < 0) {\n        throw new Error(\n          `Invalid Option: from must be a positive integer, got ${JSON.stringify(opts.from)}`,\n        );\n      }\n    } else {\n      throw new Error(\n        `Invalid Option: from must be an integer, got ${JSON.stringify(options.from)}`,\n      );\n    }\n  }\n  // Normalize option `from_line`\n  if (options.from_line === undefined || options.from_line === null) {\n    options.from_line = 1;\n  } else {\n    if (\n      typeof options.from_line === \"string\" &&\n      /\\d+/.test(options.from_line)\n    ) {\n      options.from_line = parseInt(options.from_line);\n    }\n    if (Number.isInteger(options.from_line)) {\n      if (options.from_line <= 0) {\n        throw new Error(\n          `Invalid Option: from_line must be a positive integer greater than 0, got ${JSON.stringify(opts.from_line)}`,\n        );\n      }\n    } else {\n      throw new Error(\n        `Invalid Option: from_line must be an integer, got ${JSON.stringify(opts.from_line)}`,\n      );\n    }\n  }\n  // Normalize options `ignore_last_delimiters`\n  if (\n    options.ignore_last_delimiters === undefined ||\n    options.ignore_last_delimiters === null\n  ) {\n    options.ignore_last_delimiters = false;\n  } else if (typeof options.ignore_last_delimiters === \"number\") {\n    options.ignore_last_delimiters = Math.floor(options.ignore_last_delimiters);\n    if (options.ignore_last_delimiters === 0) {\n      options.ignore_last_delimiters = false;\n    }\n  } else if (typeof options.ignore_last_delimiters !== \"boolean\") {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_IGNORE_LAST_DELIMITERS\",\n      [\n        \"Invalid option `ignore_last_delimiters`:\",\n        \"the value must be a boolean value or an integer,\",\n        `got ${JSON.stringify(options.ignore_last_delimiters)}`,\n      ],\n      options,\n    );\n  }\n  if (options.ignore_last_delimiters === true && options.columns === false) {\n    throw new CsvError(\n      \"CSV_IGNORE_LAST_DELIMITERS_REQUIRES_COLUMNS\",\n      [\n        \"The option `ignore_last_delimiters`\",\n        \"requires the activation of the `columns` option\",\n      ],\n      options,\n    );\n  }\n  // Normalize option `info`\n  if (\n    options.info === undefined ||\n    options.info === null ||\n    options.info === false\n  ) {\n    options.info = false;\n  } else if (options.info !== true) {\n    throw new Error(\n      `Invalid Option: info must be true, got ${JSON.stringify(options.info)}`,\n    );\n  }\n  // Normalize option `max_record_size`\n  if (\n    options.max_record_size === undefined ||\n    options.max_record_size === null ||\n    options.max_record_size === false\n  ) {\n    options.max_record_size = 0;\n  } else if (\n    Number.isInteger(options.max_record_size) &&\n    options.max_record_size >= 0\n  ) {\n    // Great, nothing to do\n  } else if (\n    typeof options.max_record_size === \"string\" &&\n    /\\d+/.test(options.max_record_size)\n  ) {\n    options.max_record_size = parseInt(options.max_record_size);\n  } else {\n    throw new Error(\n      `Invalid Option: max_record_size must be a positive integer, got ${JSON.stringify(options.max_record_size)}`,\n    );\n  }\n  // Normalize option `objname`\n  if (\n    options.objname === undefined ||\n    options.objname === null ||\n    options.objname === false\n  ) {\n    options.objname = undefined;\n  } else if (Buffer.isBuffer(options.objname)) {\n    if (options.objname.length === 0) {\n      throw new Error(`Invalid Option: objname must be a non empty buffer`);\n    }\n    if (options.encoding === null) {\n      // Don't call `toString`, leave objname as a buffer\n    } else {\n      options.objname = options.objname.toString(options.encoding);\n    }\n  } else if (typeof options.objname === \"string\") {\n    if (options.objname.length === 0) {\n      throw new Error(`Invalid Option: objname must be a non empty string`);\n    }\n    // Great, nothing to do\n  } else if (typeof options.objname === \"number\") {\n    // if(options.objname.length === 0){\n    //   throw new Error(`Invalid Option: objname must be a non empty string`);\n    // }\n    // Great, nothing to do\n  } else {\n    throw new Error(\n      `Invalid Option: objname must be a string or a buffer, got ${options.objname}`,\n    );\n  }\n  if (options.objname !== undefined) {\n    if (typeof options.objname === \"number\") {\n      if (options.columns !== false) {\n        throw Error(\n          \"Invalid Option: objname index cannot be combined with columns or be defined as a field\",\n        );\n      }\n    } else {\n      // A string or a buffer\n      if (options.columns === false) {\n        throw Error(\n          \"Invalid Option: objname field must be combined with columns or be defined as an index\",\n        );\n      }\n    }\n  }\n  // Normalize option `on_record`\n  if (options.on_record === undefined || options.on_record === null) {\n    options.on_record = undefined;\n  } else if (typeof options.on_record !== \"function\") {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_ON_RECORD\",\n      [\n        \"Invalid option `on_record`:\",\n        \"expect a function,\",\n        `got ${JSON.stringify(options.on_record)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `on_skip`\n  // options.on_skip ??= (err, chunk) => {\n  //   this.emit('skip', err, chunk);\n  // };\n  if (\n    options.on_skip !== undefined &&\n    options.on_skip !== null &&\n    typeof options.on_skip !== \"function\"\n  ) {\n    throw new Error(\n      `Invalid Option: on_skip must be a function, got ${JSON.stringify(options.on_skip)}`,\n    );\n  }\n  // Normalize option `quote`\n  if (\n    options.quote === null ||\n    options.quote === false ||\n    options.quote === \"\"\n  ) {\n    options.quote = null;\n  } else {\n    if (options.quote === undefined || options.quote === true) {\n      options.quote = Buffer.from('\"', options.encoding);\n    } else if (typeof options.quote === \"string\") {\n      options.quote = Buffer.from(options.quote, options.encoding);\n    }\n    if (!Buffer.isBuffer(options.quote)) {\n      throw new Error(\n        `Invalid Option: quote must be a buffer or a string, got ${JSON.stringify(options.quote)}`,\n      );\n    }\n  }\n  // Normalize option `raw`\n  if (\n    options.raw === undefined ||\n    options.raw === null ||\n    options.raw === false\n  ) {\n    options.raw = false;\n  } else if (options.raw !== true) {\n    throw new Error(\n      `Invalid Option: raw must be true, got ${JSON.stringify(options.raw)}`,\n    );\n  }\n  // Normalize option `record_delimiter`\n  if (options.record_delimiter === undefined) {\n    options.record_delimiter = [];\n  } else if (\n    typeof options.record_delimiter === \"string\" ||\n    Buffer.isBuffer(options.record_delimiter)\n  ) {\n    if (options.record_delimiter.length === 0) {\n      throw new CsvError(\n        \"CSV_INVALID_OPTION_RECORD_DELIMITER\",\n        [\n          \"Invalid option `record_delimiter`:\",\n          \"value must be a non empty string or buffer,\",\n          `got ${JSON.stringify(options.record_delimiter)}`,\n        ],\n        options,\n      );\n    }\n    options.record_delimiter = [options.record_delimiter];\n  } else if (!Array.isArray(options.record_delimiter)) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_RECORD_DELIMITER\",\n      [\n        \"Invalid option `record_delimiter`:\",\n        \"value must be a string, a buffer or array of string|buffer,\",\n        `got ${JSON.stringify(options.record_delimiter)}`,\n      ],\n      options,\n    );\n  }\n  options.record_delimiter = options.record_delimiter.map(function (rd, i) {\n    if (typeof rd !== \"string\" && !Buffer.isBuffer(rd)) {\n      throw new CsvError(\n        \"CSV_INVALID_OPTION_RECORD_DELIMITER\",\n        [\n          \"Invalid option `record_delimiter`:\",\n          \"value must be a string, a buffer or array of string|buffer\",\n          `at index ${i},`,\n          `got ${JSON.stringify(rd)}`,\n        ],\n        options,\n      );\n    } else if (rd.length === 0) {\n      throw new CsvError(\n        \"CSV_INVALID_OPTION_RECORD_DELIMITER\",\n        [\n          \"Invalid option `record_delimiter`:\",\n          \"value must be a non empty string or buffer\",\n          `at index ${i},`,\n          `got ${JSON.stringify(rd)}`,\n        ],\n        options,\n      );\n    }\n    if (typeof rd === \"string\") {\n      rd = Buffer.from(rd, options.encoding);\n    }\n    return rd;\n  });\n  // Normalize option `relax_column_count`\n  if (typeof options.relax_column_count === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.relax_column_count === undefined ||\n    options.relax_column_count === null\n  ) {\n    options.relax_column_count = false;\n  } else {\n    throw new Error(\n      `Invalid Option: relax_column_count must be a boolean, got ${JSON.stringify(options.relax_column_count)}`,\n    );\n  }\n  if (typeof options.relax_column_count_less === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.relax_column_count_less === undefined ||\n    options.relax_column_count_less === null\n  ) {\n    options.relax_column_count_less = false;\n  } else {\n    throw new Error(\n      `Invalid Option: relax_column_count_less must be a boolean, got ${JSON.stringify(options.relax_column_count_less)}`,\n    );\n  }\n  if (typeof options.relax_column_count_more === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.relax_column_count_more === undefined ||\n    options.relax_column_count_more === null\n  ) {\n    options.relax_column_count_more = false;\n  } else {\n    throw new Error(\n      `Invalid Option: relax_column_count_more must be a boolean, got ${JSON.stringify(options.relax_column_count_more)}`,\n    );\n  }\n  // Normalize option `relax_quotes`\n  if (typeof options.relax_quotes === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.relax_quotes === undefined ||\n    options.relax_quotes === null\n  ) {\n    options.relax_quotes = false;\n  } else {\n    throw new Error(\n      `Invalid Option: relax_quotes must be a boolean, got ${JSON.stringify(options.relax_quotes)}`,\n    );\n  }\n  // Normalize option `skip_empty_lines`\n  if (typeof options.skip_empty_lines === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.skip_empty_lines === undefined ||\n    options.skip_empty_lines === null\n  ) {\n    options.skip_empty_lines = false;\n  } else {\n    throw new Error(\n      `Invalid Option: skip_empty_lines must be a boolean, got ${JSON.stringify(options.skip_empty_lines)}`,\n    );\n  }\n  // Normalize option `skip_records_with_empty_values`\n  if (typeof options.skip_records_with_empty_values === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.skip_records_with_empty_values === undefined ||\n    options.skip_records_with_empty_values === null\n  ) {\n    options.skip_records_with_empty_values = false;\n  } else {\n    throw new Error(\n      `Invalid Option: skip_records_with_empty_values must be a boolean, got ${JSON.stringify(options.skip_records_with_empty_values)}`,\n    );\n  }\n  // Normalize option `skip_records_with_error`\n  if (typeof options.skip_records_with_error === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.skip_records_with_error === undefined ||\n    options.skip_records_with_error === null\n  ) {\n    options.skip_records_with_error = false;\n  } else {\n    throw new Error(\n      `Invalid Option: skip_records_with_error must be a boolean, got ${JSON.stringify(options.skip_records_with_error)}`,\n    );\n  }\n  // Normalize option `rtrim`\n  if (\n    options.rtrim === undefined ||\n    options.rtrim === null ||\n    options.rtrim === false\n  ) {\n    options.rtrim = false;\n  } else if (options.rtrim !== true) {\n    throw new Error(\n      `Invalid Option: rtrim must be a boolean, got ${JSON.stringify(options.rtrim)}`,\n    );\n  }\n  // Normalize option `ltrim`\n  if (\n    options.ltrim === undefined ||\n    options.ltrim === null ||\n    options.ltrim === false\n  ) {\n    options.ltrim = false;\n  } else if (options.ltrim !== true) {\n    throw new Error(\n      `Invalid Option: ltrim must be a boolean, got ${JSON.stringify(options.ltrim)}`,\n    );\n  }\n  // Normalize option `trim`\n  if (\n    options.trim === undefined ||\n    options.trim === null ||\n    options.trim === false\n  ) {\n    options.trim = false;\n  } else if (options.trim !== true) {\n    throw new Error(\n      `Invalid Option: trim must be a boolean, got ${JSON.stringify(options.trim)}`,\n    );\n  }\n  // Normalize options `trim`, `ltrim` and `rtrim`\n  if (options.trim === true && opts.ltrim !== false) {\n    options.ltrim = true;\n  } else if (options.ltrim !== true) {\n    options.ltrim = false;\n  }\n  if (options.trim === true && opts.rtrim !== false) {\n    options.rtrim = true;\n  } else if (options.rtrim !== true) {\n    options.rtrim = false;\n  }\n  // Normalize option `to`\n  if (options.to === undefined || options.to === null) {\n    options.to = -1;\n  } else {\n    if (typeof options.to === \"string\" && /\\d+/.test(options.to)) {\n      options.to = parseInt(options.to);\n    }\n    if (Number.isInteger(options.to)) {\n      if (options.to <= 0) {\n        throw new Error(\n          `Invalid Option: to must be a positive integer greater than 0, got ${JSON.stringify(opts.to)}`,\n        );\n      }\n    } else {\n      throw new Error(\n        `Invalid Option: to must be an integer, got ${JSON.stringify(opts.to)}`,\n      );\n    }\n  }\n  // Normalize option `to_line`\n  if (options.to_line === undefined || options.to_line === null) {\n    options.to_line = -1;\n  } else {\n    if (typeof options.to_line === \"string\" && /\\d+/.test(options.to_line)) {\n      options.to_line = parseInt(options.to_line);\n    }\n    if (Number.isInteger(options.to_line)) {\n      if (options.to_line <= 0) {\n        throw new Error(\n          `Invalid Option: to_line must be a positive integer greater than 0, got ${JSON.stringify(opts.to_line)}`,\n        );\n      }\n    } else {\n      throw new Error(\n        `Invalid Option: to_line must be an integer, got ${JSON.stringify(opts.to_line)}`,\n      );\n    }\n  }\n  return options;\n};\n\nexport { normalize_options };\n", "import { normalize_columns_array } from \"./normalize_columns_array.js\";\nimport { init_state } from \"./init_state.js\";\nimport { normalize_options } from \"./normalize_options.js\";\nimport { CsvError } from \"./CsvError.js\";\n\nconst isRecordEmpty = function (record) {\n  return record.every(\n    (field) =>\n      field == null || (field.toString && field.toString().trim() === \"\"),\n  );\n};\n\nconst cr = 13; // `\\r`, carriage return, 0x0D in hexadécimal, 13 in decimal\nconst nl = 10; // `\\n`, newline, 0x0A in hexadecimal, 10 in decimal\n\nconst boms = {\n  // Note, the following are equals:\n  // Buffer.from(\"\\ufeff\")\n  // Buffer.from([239, 187, 191])\n  // Buffer.from('EFBBBF', 'hex')\n  utf8: Buffer.from([239, 187, 191]),\n  // Note, the following are equals:\n  // Buffer.from \"\\ufeff\", 'utf16le\n  // Buffer.from([255, 254])\n  utf16le: Buffer.from([255, 254]),\n};\n\nconst transform = function (original_options = {}) {\n  const info = {\n    bytes: 0,\n    comment_lines: 0,\n    empty_lines: 0,\n    invalid_field_length: 0,\n    lines: 1,\n    records: 0,\n  };\n  const options = normalize_options(original_options);\n  return {\n    info: info,\n    original_options: original_options,\n    options: options,\n    state: init_state(options),\n    __needMoreData: function (i, bufLen, end) {\n      if (end) return false;\n      const { encoding, escape, quote } = this.options;\n      const { quoting, needMoreDataSize, recordDelimiterMaxLength } =\n        this.state;\n      const numOfCharLeft = bufLen - i - 1;\n      const requiredLength = Math.max(\n        needMoreDataSize,\n        // Skip if the remaining buffer smaller than record delimiter\n        // If \"record_delimiter\" is yet to be discovered:\n        // 1. It is equals to `[]` and \"recordDelimiterMaxLength\" equals `0`\n        // 2. We set the length to windows line ending in the current encoding\n        // Note, that encoding is known from user or bom discovery at that point\n        // recordDelimiterMaxLength,\n        recordDelimiterMaxLength === 0\n          ? Buffer.from(\"\\r\\n\", encoding).length\n          : recordDelimiterMaxLength,\n        // Skip if remaining buffer can be an escaped quote\n        quoting ? (escape === null ? 0 : escape.length) + quote.length : 0,\n        // Skip if remaining buffer can be record delimiter following the closing quote\n        quoting ? quote.length + recordDelimiterMaxLength : 0,\n      );\n      return numOfCharLeft < requiredLength;\n    },\n    // Central parser implementation\n    parse: function (nextBuf, end, push, close) {\n      const {\n        bom,\n        comment_no_infix,\n        encoding,\n        from_line,\n        ltrim,\n        max_record_size,\n        raw,\n        relax_quotes,\n        rtrim,\n        skip_empty_lines,\n        to,\n        to_line,\n      } = this.options;\n      let { comment, escape, quote, record_delimiter } = this.options;\n      const { bomSkipped, previousBuf, rawBuffer, escapeIsQuote } = this.state;\n      let buf;\n      if (previousBuf === undefined) {\n        if (nextBuf === undefined) {\n          // Handle empty string\n          close();\n          return;\n        } else {\n          buf = nextBuf;\n        }\n      } else if (previousBuf !== undefined && nextBuf === undefined) {\n        buf = previousBuf;\n      } else {\n        buf = Buffer.concat([previousBuf, nextBuf]);\n      }\n      // Handle UTF BOM\n      if (bomSkipped === false) {\n        if (bom === false) {\n          this.state.bomSkipped = true;\n        } else if (buf.length < 3) {\n          // No enough data\n          if (end === false) {\n            // Wait for more data\n            this.state.previousBuf = buf;\n            return;\n          }\n        } else {\n          for (const encoding in boms) {\n            if (boms[encoding].compare(buf, 0, boms[encoding].length) === 0) {\n              // Skip BOM\n              const bomLength = boms[encoding].length;\n              this.state.bufBytesStart += bomLength;\n              buf = buf.slice(bomLength);\n              // Renormalize original options with the new encoding\n              this.options = normalize_options({\n                ...this.original_options,\n                encoding: encoding,\n              });\n              // Options will re-evaluate the Buffer with the new encoding\n              ({ comment, escape, quote } = this.options);\n              break;\n            }\n          }\n          this.state.bomSkipped = true;\n        }\n      }\n      const bufLen = buf.length;\n      let pos;\n      for (pos = 0; pos < bufLen; pos++) {\n        // Ensure we get enough space to look ahead\n        // There should be a way to move this out of the loop\n        if (this.__needMoreData(pos, bufLen, end)) {\n          break;\n        }\n        if (this.state.wasRowDelimiter === true) {\n          this.info.lines++;\n          this.state.wasRowDelimiter = false;\n        }\n        if (to_line !== -1 && this.info.lines > to_line) {\n          this.state.stop = true;\n          close();\n          return;\n        }\n        // Auto discovery of record_delimiter, unix, mac and windows supported\n        if (this.state.quoting === false && record_delimiter.length === 0) {\n          const record_delimiterCount = this.__autoDiscoverRecordDelimiter(\n            buf,\n            pos,\n          );\n          if (record_delimiterCount) {\n            record_delimiter = this.options.record_delimiter;\n          }\n        }\n        const chr = buf[pos];\n        if (raw === true) {\n          rawBuffer.append(chr);\n        }\n        if (\n          (chr === cr || chr === nl) &&\n          this.state.wasRowDelimiter === false\n        ) {\n          this.state.wasRowDelimiter = true;\n        }\n        // Previous char was a valid escape char\n        // treat the current char as a regular char\n        if (this.state.escaping === true) {\n          this.state.escaping = false;\n        } else {\n          // Escape is only active inside quoted fields\n          // We are quoting, the char is an escape chr and there is a chr to escape\n          // if(escape !== null && this.state.quoting === true && chr === escape && pos + 1 < bufLen){\n          if (\n            escape !== null &&\n            this.state.quoting === true &&\n            this.__isEscape(buf, pos, chr) &&\n            pos + escape.length < bufLen\n          ) {\n            if (escapeIsQuote) {\n              if (this.__isQuote(buf, pos + escape.length)) {\n                this.state.escaping = true;\n                pos += escape.length - 1;\n                continue;\n              }\n            } else {\n              this.state.escaping = true;\n              pos += escape.length - 1;\n              continue;\n            }\n          }\n          // Not currently escaping and chr is a quote\n          // TODO: need to compare bytes instead of single char\n          if (this.state.commenting === false && this.__isQuote(buf, pos)) {\n            if (this.state.quoting === true) {\n              const nextChr = buf[pos + quote.length];\n              const isNextChrTrimable =\n                rtrim && this.__isCharTrimable(buf, pos + quote.length);\n              const isNextChrComment =\n                comment !== null &&\n                this.__compareBytes(comment, buf, pos + quote.length, nextChr);\n              const isNextChrDelimiter = this.__isDelimiter(\n                buf,\n                pos + quote.length,\n                nextChr,\n              );\n              const isNextChrRecordDelimiter =\n                record_delimiter.length === 0\n                  ? this.__autoDiscoverRecordDelimiter(buf, pos + quote.length)\n                  : this.__isRecordDelimiter(nextChr, buf, pos + quote.length);\n              // Escape a quote\n              // Treat next char as a regular character\n              if (\n                escape !== null &&\n                this.__isEscape(buf, pos, chr) &&\n                this.__isQuote(buf, pos + escape.length)\n              ) {\n                pos += escape.length - 1;\n              } else if (\n                !nextChr ||\n                isNextChrDelimiter ||\n                isNextChrRecordDelimiter ||\n                isNextChrComment ||\n                isNextChrTrimable\n              ) {\n                this.state.quoting = false;\n                this.state.wasQuoting = true;\n                pos += quote.length - 1;\n                continue;\n              } else if (relax_quotes === false) {\n                const err = this.__error(\n                  new CsvError(\n                    \"CSV_INVALID_CLOSING_QUOTE\",\n                    [\n                      \"Invalid Closing Quote:\",\n                      `got \"${String.fromCharCode(nextChr)}\"`,\n                      `at line ${this.info.lines}`,\n                      \"instead of delimiter, record delimiter, trimable character\",\n                      \"(if activated) or comment\",\n                    ],\n                    this.options,\n                    this.__infoField(),\n                  ),\n                );\n                if (err !== undefined) return err;\n              } else {\n                this.state.quoting = false;\n                this.state.wasQuoting = true;\n                this.state.field.prepend(quote);\n                pos += quote.length - 1;\n              }\n            } else {\n              if (this.state.field.length !== 0) {\n                // In relax_quotes mode, treat opening quote preceded by chrs as regular\n                if (relax_quotes === false) {\n                  const info = this.__infoField();\n                  const bom = Object.keys(boms)\n                    .map((b) =>\n                      boms[b].equals(this.state.field.toString()) ? b : false,\n                    )\n                    .filter(Boolean)[0];\n                  const err = this.__error(\n                    new CsvError(\n                      \"INVALID_OPENING_QUOTE\",\n                      [\n                        \"Invalid Opening Quote:\",\n                        `a quote is found on field ${JSON.stringify(info.column)} at line ${info.lines}, value is ${JSON.stringify(this.state.field.toString(encoding))}`,\n                        bom ? `(${bom} bom)` : undefined,\n                      ],\n                      this.options,\n                      info,\n                      {\n                        field: this.state.field,\n                      },\n                    ),\n                  );\n                  if (err !== undefined) return err;\n                }\n              } else {\n                this.state.quoting = true;\n                pos += quote.length - 1;\n                continue;\n              }\n            }\n          }\n          if (this.state.quoting === false) {\n            const recordDelimiterLength = this.__isRecordDelimiter(\n              chr,\n              buf,\n              pos,\n            );\n            if (recordDelimiterLength !== 0) {\n              // Do not emit comments which take a full line\n              const skipCommentLine =\n                this.state.commenting &&\n                this.state.wasQuoting === false &&\n                this.state.record.length === 0 &&\n                this.state.field.length === 0;\n              if (skipCommentLine) {\n                this.info.comment_lines++;\n                // Skip full comment line\n              } else {\n                // Activate records emition if above from_line\n                if (\n                  this.state.enabled === false &&\n                  this.info.lines +\n                    (this.state.wasRowDelimiter === true ? 1 : 0) >=\n                    from_line\n                ) {\n                  this.state.enabled = true;\n                  this.__resetField();\n                  this.__resetRecord();\n                  pos += recordDelimiterLength - 1;\n                  continue;\n                }\n                // Skip if line is empty and skip_empty_lines activated\n                if (\n                  skip_empty_lines === true &&\n                  this.state.wasQuoting === false &&\n                  this.state.record.length === 0 &&\n                  this.state.field.length === 0\n                ) {\n                  this.info.empty_lines++;\n                  pos += recordDelimiterLength - 1;\n                  continue;\n                }\n                this.info.bytes = this.state.bufBytesStart + pos;\n                const errField = this.__onField();\n                if (errField !== undefined) return errField;\n                this.info.bytes =\n                  this.state.bufBytesStart + pos + recordDelimiterLength;\n                const errRecord = this.__onRecord(push);\n                if (errRecord !== undefined) return errRecord;\n                if (to !== -1 && this.info.records >= to) {\n                  this.state.stop = true;\n                  close();\n                  return;\n                }\n              }\n              this.state.commenting = false;\n              pos += recordDelimiterLength - 1;\n              continue;\n            }\n            if (this.state.commenting) {\n              continue;\n            }\n            if (\n              comment !== null &&\n              (comment_no_infix === false ||\n                (this.state.record.length === 0 &&\n                  this.state.field.length === 0))\n            ) {\n              const commentCount = this.__compareBytes(comment, buf, pos, chr);\n              if (commentCount !== 0) {\n                this.state.commenting = true;\n                continue;\n              }\n            }\n            const delimiterLength = this.__isDelimiter(buf, pos, chr);\n            if (delimiterLength !== 0) {\n              this.info.bytes = this.state.bufBytesStart + pos;\n              const errField = this.__onField();\n              if (errField !== undefined) return errField;\n              pos += delimiterLength - 1;\n              continue;\n            }\n          }\n        }\n        if (this.state.commenting === false) {\n          if (\n            max_record_size !== 0 &&\n            this.state.record_length + this.state.field.length > max_record_size\n          ) {\n            return this.__error(\n              new CsvError(\n                \"CSV_MAX_RECORD_SIZE\",\n                [\n                  \"Max Record Size:\",\n                  \"record exceed the maximum number of tolerated bytes\",\n                  `of ${max_record_size}`,\n                  `at line ${this.info.lines}`,\n                ],\n                this.options,\n                this.__infoField(),\n              ),\n            );\n          }\n        }\n        const lappend =\n          ltrim === false ||\n          this.state.quoting === true ||\n          this.state.field.length !== 0 ||\n          !this.__isCharTrimable(buf, pos);\n        // rtrim in non quoting is handle in __onField\n        const rappend = rtrim === false || this.state.wasQuoting === false;\n        if (lappend === true && rappend === true) {\n          this.state.field.append(chr);\n        } else if (rtrim === true && !this.__isCharTrimable(buf, pos)) {\n          return this.__error(\n            new CsvError(\n              \"CSV_NON_TRIMABLE_CHAR_AFTER_CLOSING_QUOTE\",\n              [\n                \"Invalid Closing Quote:\",\n                \"found non trimable byte after quote\",\n                `at line ${this.info.lines}`,\n              ],\n              this.options,\n              this.__infoField(),\n            ),\n          );\n        } else {\n          if (lappend === false) {\n            pos += this.__isCharTrimable(buf, pos) - 1;\n          }\n          continue;\n        }\n      }\n      if (end === true) {\n        // Ensure we are not ending in a quoting state\n        if (this.state.quoting === true) {\n          const err = this.__error(\n            new CsvError(\n              \"CSV_QUOTE_NOT_CLOSED\",\n              [\n                \"Quote Not Closed:\",\n                `the parsing is finished with an opening quote at line ${this.info.lines}`,\n              ],\n              this.options,\n              this.__infoField(),\n            ),\n          );\n          if (err !== undefined) return err;\n        } else {\n          // Skip last line if it has no characters\n          if (\n            this.state.wasQuoting === true ||\n            this.state.record.length !== 0 ||\n            this.state.field.length !== 0\n          ) {\n            this.info.bytes = this.state.bufBytesStart + pos;\n            const errField = this.__onField();\n            if (errField !== undefined) return errField;\n            const errRecord = this.__onRecord(push);\n            if (errRecord !== undefined) return errRecord;\n          } else if (this.state.wasRowDelimiter === true) {\n            this.info.empty_lines++;\n          } else if (this.state.commenting === true) {\n            this.info.comment_lines++;\n          }\n        }\n      } else {\n        this.state.bufBytesStart += pos;\n        this.state.previousBuf = buf.slice(pos);\n      }\n      if (this.state.wasRowDelimiter === true) {\n        this.info.lines++;\n        this.state.wasRowDelimiter = false;\n      }\n    },\n    __onRecord: function (push) {\n      const {\n        columns,\n        group_columns_by_name,\n        encoding,\n        info,\n        from,\n        relax_column_count,\n        relax_column_count_less,\n        relax_column_count_more,\n        raw,\n        skip_records_with_empty_values,\n      } = this.options;\n      const { enabled, record } = this.state;\n      if (enabled === false) {\n        return this.__resetRecord();\n      }\n      // Convert the first line into column names\n      const recordLength = record.length;\n      if (columns === true) {\n        if (skip_records_with_empty_values === true && isRecordEmpty(record)) {\n          this.__resetRecord();\n          return;\n        }\n        return this.__firstLineToColumns(record);\n      }\n      if (columns === false && this.info.records === 0) {\n        this.state.expectedRecordLength = recordLength;\n      }\n      if (recordLength !== this.state.expectedRecordLength) {\n        const err =\n          columns === false\n            ? new CsvError(\n                \"CSV_RECORD_INCONSISTENT_FIELDS_LENGTH\",\n                [\n                  \"Invalid Record Length:\",\n                  `expect ${this.state.expectedRecordLength},`,\n                  `got ${recordLength} on line ${this.info.lines}`,\n                ],\n                this.options,\n                this.__infoField(),\n                {\n                  record: record,\n                },\n              )\n            : new CsvError(\n                \"CSV_RECORD_INCONSISTENT_COLUMNS\",\n                [\n                  \"Invalid Record Length:\",\n                  `columns length is ${columns.length},`, // rename columns\n                  `got ${recordLength} on line ${this.info.lines}`,\n                ],\n                this.options,\n                this.__infoField(),\n                {\n                  record: record,\n                },\n              );\n        if (\n          relax_column_count === true ||\n          (relax_column_count_less === true &&\n            recordLength < this.state.expectedRecordLength) ||\n          (relax_column_count_more === true &&\n            recordLength > this.state.expectedRecordLength)\n        ) {\n          this.info.invalid_field_length++;\n          this.state.error = err;\n          // Error is undefined with skip_records_with_error\n        } else {\n          const finalErr = this.__error(err);\n          if (finalErr) return finalErr;\n        }\n      }\n      if (skip_records_with_empty_values === true && isRecordEmpty(record)) {\n        this.__resetRecord();\n        return;\n      }\n      if (this.state.recordHasError === true) {\n        this.__resetRecord();\n        this.state.recordHasError = false;\n        return;\n      }\n      this.info.records++;\n      if (from === 1 || this.info.records >= from) {\n        const { objname } = this.options;\n        // With columns, records are object\n        if (columns !== false) {\n          const obj = {};\n          // Transform record array to an object\n          for (let i = 0, l = record.length; i < l; i++) {\n            if (columns[i] === undefined || columns[i].disabled) continue;\n            // Turn duplicate columns into an array\n            if (\n              group_columns_by_name === true &&\n              obj[columns[i].name] !== undefined\n            ) {\n              if (Array.isArray(obj[columns[i].name])) {\n                obj[columns[i].name] = obj[columns[i].name].concat(record[i]);\n              } else {\n                obj[columns[i].name] = [obj[columns[i].name], record[i]];\n              }\n            } else {\n              obj[columns[i].name] = record[i];\n            }\n          }\n          // Without objname (default)\n          if (raw === true || info === true) {\n            const extRecord = Object.assign(\n              { record: obj },\n              raw === true\n                ? { raw: this.state.rawBuffer.toString(encoding) }\n                : {},\n              info === true ? { info: this.__infoRecord() } : {},\n            );\n            const err = this.__push(\n              objname === undefined ? extRecord : [obj[objname], extRecord],\n              push,\n            );\n            if (err) {\n              return err;\n            }\n          } else {\n            const err = this.__push(\n              objname === undefined ? obj : [obj[objname], obj],\n              push,\n            );\n            if (err) {\n              return err;\n            }\n          }\n          // Without columns, records are array\n        } else {\n          if (raw === true || info === true) {\n            const extRecord = Object.assign(\n              { record: record },\n              raw === true\n                ? { raw: this.state.rawBuffer.toString(encoding) }\n                : {},\n              info === true ? { info: this.__infoRecord() } : {},\n            );\n            const err = this.__push(\n              objname === undefined ? extRecord : [record[objname], extRecord],\n              push,\n            );\n            if (err) {\n              return err;\n            }\n          } else {\n            const err = this.__push(\n              objname === undefined ? record : [record[objname], record],\n              push,\n            );\n            if (err) {\n              return err;\n            }\n          }\n        }\n      }\n      this.__resetRecord();\n    },\n    __firstLineToColumns: function (record) {\n      const { firstLineToHeaders } = this.state;\n      try {\n        const headers =\n          firstLineToHeaders === undefined\n            ? record\n            : firstLineToHeaders.call(null, record);\n        if (!Array.isArray(headers)) {\n          return this.__error(\n            new CsvError(\n              \"CSV_INVALID_COLUMN_MAPPING\",\n              [\n                \"Invalid Column Mapping:\",\n                \"expect an array from column function,\",\n                `got ${JSON.stringify(headers)}`,\n              ],\n              this.options,\n              this.__infoField(),\n              {\n                headers: headers,\n              },\n            ),\n          );\n        }\n        const normalizedHeaders = normalize_columns_array(headers);\n        this.state.expectedRecordLength = normalizedHeaders.length;\n        this.options.columns = normalizedHeaders;\n        this.__resetRecord();\n        return;\n      } catch (err) {\n        return err;\n      }\n    },\n    __resetRecord: function () {\n      if (this.options.raw === true) {\n        this.state.rawBuffer.reset();\n      }\n      this.state.error = undefined;\n      this.state.record = [];\n      this.state.record_length = 0;\n    },\n    __onField: function () {\n      const { cast, encoding, rtrim, max_record_size } = this.options;\n      const { enabled, wasQuoting } = this.state;\n      // Short circuit for the from_line options\n      if (enabled === false) {\n        return this.__resetField();\n      }\n      let field = this.state.field.toString(encoding);\n      if (rtrim === true && wasQuoting === false) {\n        field = field.trimRight();\n      }\n      if (cast === true) {\n        const [err, f] = this.__cast(field);\n        if (err !== undefined) return err;\n        field = f;\n      }\n      this.state.record.push(field);\n      // Increment record length if record size must not exceed a limit\n      if (max_record_size !== 0 && typeof field === \"string\") {\n        this.state.record_length += field.length;\n      }\n      this.__resetField();\n    },\n    __resetField: function () {\n      this.state.field.reset();\n      this.state.wasQuoting = false;\n    },\n    __push: function (record, push) {\n      const { on_record } = this.options;\n      if (on_record !== undefined) {\n        const info = this.__infoRecord();\n        try {\n          record = on_record.call(null, record, info);\n        } catch (err) {\n          return err;\n        }\n        if (record === undefined || record === null) {\n          return;\n        }\n      }\n      push(record);\n    },\n    // Return a tuple with the error and the casted value\n    __cast: function (field) {\n      const { columns, relax_column_count } = this.options;\n      const isColumns = Array.isArray(columns);\n      // Dont loose time calling cast\n      // because the final record is an object\n      // and this field can't be associated to a key present in columns\n      if (\n        isColumns === true &&\n        relax_column_count &&\n        this.options.columns.length <= this.state.record.length\n      ) {\n        return [undefined, undefined];\n      }\n      if (this.state.castField !== null) {\n        try {\n          const info = this.__infoField();\n          return [undefined, this.state.castField.call(null, field, info)];\n        } catch (err) {\n          return [err];\n        }\n      }\n      if (this.__isFloat(field)) {\n        return [undefined, parseFloat(field)];\n      } else if (this.options.cast_date !== false) {\n        const info = this.__infoField();\n        return [undefined, this.options.cast_date.call(null, field, info)];\n      }\n      return [undefined, field];\n    },\n    // Helper to test if a character is a space or a line delimiter\n    __isCharTrimable: function (buf, pos) {\n      const isTrim = (buf, pos) => {\n        const { timchars } = this.state;\n        loop1: for (let i = 0; i < timchars.length; i++) {\n          const timchar = timchars[i];\n          for (let j = 0; j < timchar.length; j++) {\n            if (timchar[j] !== buf[pos + j]) continue loop1;\n          }\n          return timchar.length;\n        }\n        return 0;\n      };\n      return isTrim(buf, pos);\n    },\n    // Keep it in case we implement the `cast_int` option\n    // __isInt(value){\n    //   // return Number.isInteger(parseInt(value))\n    //   // return !isNaN( parseInt( obj ) );\n    //   return /^(\\-|\\+)?[1-9][0-9]*$/.test(value)\n    // }\n    __isFloat: function (value) {\n      return value - parseFloat(value) + 1 >= 0; // Borrowed from jquery\n    },\n    __compareBytes: function (sourceBuf, targetBuf, targetPos, firstByte) {\n      if (sourceBuf[0] !== firstByte) return 0;\n      const sourceLength = sourceBuf.length;\n      for (let i = 1; i < sourceLength; i++) {\n        if (sourceBuf[i] !== targetBuf[targetPos + i]) return 0;\n      }\n      return sourceLength;\n    },\n    __isDelimiter: function (buf, pos, chr) {\n      const { delimiter, ignore_last_delimiters } = this.options;\n      if (\n        ignore_last_delimiters === true &&\n        this.state.record.length === this.options.columns.length - 1\n      ) {\n        return 0;\n      } else if (\n        ignore_last_delimiters !== false &&\n        typeof ignore_last_delimiters === \"number\" &&\n        this.state.record.length === ignore_last_delimiters - 1\n      ) {\n        return 0;\n      }\n      loop1: for (let i = 0; i < delimiter.length; i++) {\n        const del = delimiter[i];\n        if (del[0] === chr) {\n          for (let j = 1; j < del.length; j++) {\n            if (del[j] !== buf[pos + j]) continue loop1;\n          }\n          return del.length;\n        }\n      }\n      return 0;\n    },\n    __isRecordDelimiter: function (chr, buf, pos) {\n      const { record_delimiter } = this.options;\n      const recordDelimiterLength = record_delimiter.length;\n      loop1: for (let i = 0; i < recordDelimiterLength; i++) {\n        const rd = record_delimiter[i];\n        const rdLength = rd.length;\n        if (rd[0] !== chr) {\n          continue;\n        }\n        for (let j = 1; j < rdLength; j++) {\n          if (rd[j] !== buf[pos + j]) {\n            continue loop1;\n          }\n        }\n        return rd.length;\n      }\n      return 0;\n    },\n    __isEscape: function (buf, pos, chr) {\n      const { escape } = this.options;\n      if (escape === null) return false;\n      const l = escape.length;\n      if (escape[0] === chr) {\n        for (let i = 0; i < l; i++) {\n          if (escape[i] !== buf[pos + i]) {\n            return false;\n          }\n        }\n        return true;\n      }\n      return false;\n    },\n    __isQuote: function (buf, pos) {\n      const { quote } = this.options;\n      if (quote === null) return false;\n      const l = quote.length;\n      for (let i = 0; i < l; i++) {\n        if (quote[i] !== buf[pos + i]) {\n          return false;\n        }\n      }\n      return true;\n    },\n    __autoDiscoverRecordDelimiter: function (buf, pos) {\n      const { encoding } = this.options;\n      // Note, we don't need to cache this information in state,\n      // It is only called on the first line until we find out a suitable\n      // record delimiter.\n      const rds = [\n        // Important, the windows line ending must be before mac os 9\n        Buffer.from(\"\\r\\n\", encoding),\n        Buffer.from(\"\\n\", encoding),\n        Buffer.from(\"\\r\", encoding),\n      ];\n      loop: for (let i = 0; i < rds.length; i++) {\n        const l = rds[i].length;\n        for (let j = 0; j < l; j++) {\n          if (rds[i][j] !== buf[pos + j]) {\n            continue loop;\n          }\n        }\n        this.options.record_delimiter.push(rds[i]);\n        this.state.recordDelimiterMaxLength = rds[i].length;\n        return rds[i].length;\n      }\n      return 0;\n    },\n    __error: function (msg) {\n      const { encoding, raw, skip_records_with_error } = this.options;\n      const err = typeof msg === \"string\" ? new Error(msg) : msg;\n      if (skip_records_with_error) {\n        this.state.recordHasError = true;\n        if (this.options.on_skip !== undefined) {\n          this.options.on_skip(\n            err,\n            raw ? this.state.rawBuffer.toString(encoding) : undefined,\n          );\n        }\n        // this.emit('skip', err, raw ? this.state.rawBuffer.toString(encoding) : undefined);\n        return undefined;\n      } else {\n        return err;\n      }\n    },\n    __infoDataSet: function () {\n      return {\n        ...this.info,\n        columns: this.options.columns,\n      };\n    },\n    __infoRecord: function () {\n      const { columns, raw, encoding } = this.options;\n      return {\n        ...this.__infoDataSet(),\n        error: this.state.error,\n        header: columns === true,\n        index: this.state.record.length,\n        raw: raw ? this.state.rawBuffer.toString(encoding) : undefined,\n      };\n    },\n    __infoField: function () {\n      const { columns } = this.options;\n      const isColumns = Array.isArray(columns);\n      return {\n        ...this.__infoRecord(),\n        column:\n          isColumns === true\n            ? columns.length > this.state.record.length\n              ? columns[this.state.record.length].name\n              : null\n            : this.state.record.length,\n        quoting: this.state.wasQuoting,\n      };\n    },\n  };\n};\n\nexport { transform, CsvError };\n", "import { CsvError, transform } from \"./api/index.js\";\n\nconst parse = function (data, opts = {}) {\n  if (typeof data === \"string\") {\n    data = Buffer.from(data);\n  }\n  const records = opts && opts.objname ? {} : [];\n  const parser = transform(opts);\n  const push = (record) => {\n    if (parser.options.objname === undefined) records.push(record);\n    else {\n      records[record[0]] = record[1];\n    }\n  };\n  const close = () => {};\n  const err1 = parser.parse(data, false, push, close);\n  if (err1 !== undefined) throw err1;\n  const err2 = parser.parse(undefined, true, push, close);\n  if (err2 !== undefined) throw err2;\n  return records;\n};\n\n// export default parse\nexport { parse };\nexport { CsvError };\n"], "mappings": ";;;AAAA,IAAM,WAAN,MAAM,kBAAiB,MAAM;AAAA,EAC3B,YAAY,MAAM,SAAS,YAAY,UAAU;AAC/C,QAAI,MAAM,QAAQ,OAAO,EAAG,WAAU,QAAQ,KAAK,GAAG,EAAE,KAAK;AAC7D,UAAM,OAAO;AACb,QAAI,MAAM,sBAAsB,QAAW;AACzC,YAAM,kBAAkB,MAAM,SAAQ;AAAA,IACxC;AACA,SAAK,OAAO;AACZ,eAAW,WAAW,UAAU;AAC9B,iBAAW,OAAO,SAAS;AACzB,cAAM,QAAQ,QAAQ,GAAG;AACzB,aAAK,GAAG,IAAI,OAAO,SAAS,KAAK,IAC7B,MAAM,SAAS,QAAQ,QAAQ,IAC/B,SAAS,OACP,QACA,KAAK,MAAM,KAAK,UAAU,KAAK,CAAC;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACF;;;ACnBA,IAAM,YAAY,SAAU,KAAK;AAC/B,SAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,CAAC,MAAM,QAAQ,GAAG;AACtE;;;ACCA,IAAM,0BAA0B,SAAU,SAAS;AACjD,QAAM,oBAAoB,CAAC;AAC3B,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK;AAC9C,UAAM,SAAS,QAAQ,CAAC;AACxB,QAAI,WAAW,UAAa,WAAW,QAAQ,WAAW,OAAO;AAC/D,wBAAkB,CAAC,IAAI,EAAE,UAAU,KAAK;AAAA,IAC1C,WAAW,OAAO,WAAW,UAAU;AACrC,wBAAkB,CAAC,IAAI,EAAE,MAAM,OAAO;AAAA,IACxC,WAAW,UAAU,MAAM,GAAG;AAC5B,UAAI,OAAO,OAAO,SAAS,UAAU;AACnC,cAAM,IAAI,SAAS,mCAAmC;AAAA,UACpD;AAAA,UACA,2CAA2C,CAAC;AAAA,UAC5C;AAAA,QACF,CAAC;AAAA,MACH;AACA,wBAAkB,CAAC,IAAI;AAAA,IACzB,OAAO;AACL,YAAM,IAAI,SAAS,iCAAiC;AAAA,QAClD;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,MAAM,CAAC,gBAAgB,CAAC;AAAA,MAChD,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AACT;;;AC7BA,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,OAAO,KAAK;AACtB,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,MAAM,OAAO,YAAY,IAAI;AAAA,EACpC;AAAA,EACA,QAAQ,KAAK;AACX,QAAI,OAAO,SAAS,GAAG,GAAG;AACxB,YAAM,SAAS,KAAK,SAAS,IAAI;AACjC,UAAI,UAAU,KAAK,MAAM;AACvB,aAAK,OAAO;AACZ,YAAI,UAAU,KAAK,MAAM;AACvB,gBAAM,MAAM,sBAAsB;AAAA,QACpC;AAAA,MACF;AACA,YAAM,MAAM,KAAK;AACjB,WAAK,MAAM,OAAO,YAAY,KAAK,IAAI;AACvC,UAAI,KAAK,KAAK,KAAK,CAAC;AACpB,UAAI,KAAK,KAAK,KAAK,IAAI,MAAM;AAC7B,WAAK,UAAU,IAAI;AAAA,IACrB,OAAO;AACL,YAAM,SAAS,KAAK;AACpB,UAAI,WAAW,KAAK,MAAM;AACxB,aAAK,OAAO;AAAA,MACd;AACA,YAAM,MAAM,KAAK,MAAM;AACvB,WAAK,IAAI,CAAC,IAAI;AACd,UAAI,KAAK,KAAK,KAAK,GAAG,GAAG,MAAM;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO,KAAK;AACV,UAAM,SAAS,KAAK;AACpB,QAAI,WAAW,KAAK,MAAM;AACxB,WAAK,OAAO;AAAA,IACd;AACA,SAAK,IAAI,MAAM,IAAI;AAAA,EACrB;AAAA,EACA,QAAQ;AACN,WAAO,OAAO,KAAK,KAAK,IAAI,MAAM,GAAG,KAAK,MAAM,CAAC;AAAA,EACnD;AAAA,EACA,SAAS;AACP,UAAM,SAAS,KAAK;AACpB,SAAK,OAAO,KAAK,OAAO;AACxB,UAAM,MAAM,OAAO,YAAY,KAAK,IAAI;AACxC,SAAK,IAAI,KAAK,KAAK,GAAG,GAAG,MAAM;AAC/B,SAAK,MAAM;AAAA,EACb;AAAA,EACA,SAAS,UAAU;AACjB,QAAI,UAAU;AACZ,aAAO,KAAK,IAAI,MAAM,GAAG,KAAK,MAAM,EAAE,SAAS,QAAQ;AAAA,IACzD,OAAO;AACL,aAAO,WAAW,UAAU,MAAM,KAAK,KAAK,IAAI,MAAM,GAAG,KAAK,MAAM,CAAC;AAAA,IACvE;AAAA,EACF;AAAA,EACA,SAAS;AACP,WAAO,KAAK,SAAS,MAAM;AAAA,EAC7B;AAAA,EACA,QAAQ;AACN,SAAK,SAAS;AAAA,EAChB;AACF;AAEA,IAAO,2BAAQ;;;ACxDf,IAAM,KAAK;AACX,IAAM,KAAK;AACX,IAAM,KAAK;AACX,IAAM,QAAQ;AACd,IAAM,MAAM;AAEZ,IAAM,aAAa,SAAU,SAAS;AACpC,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,WAAW,QAAQ;AAAA,IACnB,YAAY;AAAA;AAAA,IAEZ,OAAO;AAAA,IACP,SAAS,QAAQ,cAAc;AAAA,IAC/B,UAAU;AAAA,IACV,eACE,OAAO,SAAS,QAAQ,MAAM,KAC9B,OAAO,SAAS,QAAQ,KAAK,KAC7B,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,KAAK,MAAM;AAAA;AAAA,IAEpD,sBAAsB,MAAM,QAAQ,QAAQ,OAAO,IAC/C,QAAQ,QAAQ,SAChB;AAAA,IACJ,OAAO,IAAI,yBAAiB,EAAE;AAAA,IAC9B,oBAAoB,QAAQ;AAAA,IAC5B,kBAAkB,KAAK;AAAA;AAAA,MAErB,QAAQ,YAAY,OAAO,QAAQ,QAAQ,SAAS;AAAA,MAEpD,GAAG,QAAQ,UAAU,IAAI,CAAC,cAAc,UAAU,MAAM;AAAA;AAAA,MAExD,QAAQ,UAAU,OAAO,QAAQ,MAAM,SAAS;AAAA,IAClD;AAAA,IACA,aAAa;AAAA,IACb,SAAS;AAAA,IACT,MAAM;AAAA,IACN,WAAW,IAAI,yBAAiB,GAAG;AAAA,IACnC,QAAQ,CAAC;AAAA,IACT,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,0BACE,QAAQ,iBAAiB,WAAW,IAChC,IACA,KAAK,IAAI,GAAG,QAAQ,iBAAiB,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;AAAA,IAC/D,WAAW;AAAA,MACT,OAAO,KAAK,KAAK,QAAQ,QAAQ,EAAE,CAAC;AAAA,MACpC,OAAO,KAAK,KAAM,QAAQ,QAAQ,EAAE,CAAC;AAAA,IACvC;AAAA,IACA,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,UAAU;AAAA,MACR,OAAO,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG,MAAM,EAAE,SAAS,GAAG,QAAQ,QAAQ;AAAA,MAClE,OAAO,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG,MAAM,EAAE,SAAS,GAAG,QAAQ,QAAQ;AAAA,MAClE,OAAO,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG,MAAM,EAAE,SAAS,GAAG,QAAQ,QAAQ;AAAA,MAClE,OAAO,KAAK,OAAO,KAAK,CAAC,KAAK,GAAG,MAAM,EAAE,SAAS,GAAG,QAAQ,QAAQ;AAAA,MACrE,OAAO,KAAK,OAAO,KAAK,CAAC,GAAG,GAAG,MAAM,EAAE,SAAS,GAAG,QAAQ,QAAQ;AAAA,IACrE;AAAA,EACF;AACF;;;ACjEA,IAAM,aAAa,SAAU,KAAK;AAChC,SAAO,IAAI,QAAQ,YAAY,SAAU,GAAG,OAAO;AACjD,WAAO,MAAM,MAAM,YAAY;AAAA,EACjC,CAAC;AACH;;;ACAA,IAAM,oBAAoB,SAAU,MAAM;AACxC,QAAM,UAAU,CAAC;AAEjB,aAAW,OAAO,MAAM;AACtB,YAAQ,WAAW,GAAG,CAAC,IAAI,KAAK,GAAG;AAAA,EACrC;AAIA,MAAI,QAAQ,aAAa,UAAa,QAAQ,aAAa,MAAM;AAC/D,YAAQ,WAAW;AAAA,EACrB,WAAW,QAAQ,aAAa,QAAQ,QAAQ,aAAa,OAAO;AAClE,YAAQ,WAAW;AAAA,EACrB,WACE,OAAO,QAAQ,aAAa,YAC5B,QAAQ,aAAa,MACrB;AACA,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,QAAQ,CAAC;AAAA,MACzC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,QAAQ,UAChB,QAAQ,QAAQ,QAChB,QAAQ,QAAQ,OAChB;AACA,YAAQ,MAAM;AAAA,EAChB,WAAW,QAAQ,QAAQ,MAAM;AAC/B,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,GAAG,CAAC;AAAA,MACpC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,UAAQ,gBAAgB;AACxB,MACE,QAAQ,SAAS,UACjB,QAAQ,SAAS,QACjB,QAAQ,SAAS,SACjB,QAAQ,SAAS,IACjB;AACA,YAAQ,OAAO;AAAA,EACjB,WAAW,OAAO,QAAQ,SAAS,YAAY;AAC7C,YAAQ,gBAAgB,QAAQ;AAChC,YAAQ,OAAO;AAAA,EACjB,WAAW,QAAQ,SAAS,MAAM;AAChC,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,IAAI,CAAC;AAAA,MACrC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,cAAc,UACtB,QAAQ,cAAc,QACtB,QAAQ,cAAc,SACtB,QAAQ,cAAc,IACtB;AACA,YAAQ,YAAY;AAAA,EACtB,WAAW,QAAQ,cAAc,MAAM;AACrC,YAAQ,YAAY,SAAU,OAAO;AACnC,YAAM,OAAO,KAAK,MAAM,KAAK;AAC7B,aAAO,CAAC,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAAA,IACzC;AAAA,EACF,WAAW,OAAO,QAAQ,cAAc,YAAY;AAClD,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,SAAS,CAAC;AAAA,MAC1C;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,UAAQ,4BAA4B;AACpC,MAAI,QAAQ,YAAY,MAAM;AAE5B,YAAQ,4BAA4B;AAAA,EACtC,WAAW,OAAO,QAAQ,YAAY,YAAY;AAChD,YAAQ,4BAA4B,QAAQ;AAC5C,YAAQ,UAAU;AAAA,EACpB,WAAW,MAAM,QAAQ,QAAQ,OAAO,GAAG;AACzC,YAAQ,UAAU,wBAAwB,QAAQ,OAAO;AAAA,EAC3D,WACE,QAAQ,YAAY,UACpB,QAAQ,YAAY,QACpB,QAAQ,YAAY,OACpB;AACA,YAAQ,UAAU;AAAA,EACpB,OAAO;AACL,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,OAAO,CAAC;AAAA,MACxC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,0BAA0B,UAClC,QAAQ,0BAA0B,QAClC,QAAQ,0BAA0B,OAClC;AACA,YAAQ,wBAAwB;AAAA,EAClC,WAAW,QAAQ,0BAA0B,MAAM;AACjD,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,qBAAqB,CAAC;AAAA,MACtD;AAAA,MACA;AAAA,IACF;AAAA,EACF,WAAW,QAAQ,YAAY,OAAO;AACpC,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,YAAY,UACpB,QAAQ,YAAY,QACpB,QAAQ,YAAY,SACpB,QAAQ,YAAY,IACpB;AACA,YAAQ,UAAU;AAAA,EACpB,OAAO;AACL,QAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,cAAQ,UAAU,OAAO,KAAK,QAAQ,SAAS,QAAQ,QAAQ;AAAA,IACjE;AACA,QAAI,CAAC,OAAO,SAAS,QAAQ,OAAO,GAAG;AACrC,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,OAAO,KAAK,UAAU,QAAQ,OAAO,CAAC;AAAA,QACxC;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,qBAAqB,UAC7B,QAAQ,qBAAqB,QAC7B,QAAQ,qBAAqB,OAC7B;AACA,YAAQ,mBAAmB;AAAA,EAC7B,WAAW,QAAQ,qBAAqB,MAAM;AAC5C,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,gBAAgB,CAAC;AAAA,MACjD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,iBAAiB,KAAK,UAAU,QAAQ,SAAS;AACvD,MAAI,CAAC,MAAM,QAAQ,QAAQ,SAAS;AAClC,YAAQ,YAAY,CAAC,QAAQ,SAAS;AACxC,MAAI,QAAQ,UAAU,WAAW,GAAG;AAClC,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,cAAc;AAAA,MACvB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,UAAQ,YAAY,QAAQ,UAAU,IAAI,SAAU,WAAW;AAC7D,QAAI,cAAc,UAAa,cAAc,QAAQ,cAAc,OAAO;AACxE,aAAO,OAAO,KAAK,KAAK,QAAQ,QAAQ;AAAA,IAC1C;AACA,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,OAAO,KAAK,WAAW,QAAQ,QAAQ;AAAA,IACrD;AACA,QAAI,CAAC,OAAO,SAAS,SAAS,KAAK,UAAU,WAAW,GAAG;AACzD,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,OAAO,cAAc;AAAA,QACvB;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AAED,MAAI,QAAQ,WAAW,UAAa,QAAQ,WAAW,MAAM;AAC3D,YAAQ,SAAS,OAAO,KAAK,KAAK,QAAQ,QAAQ;AAAA,EACpD,WAAW,OAAO,QAAQ,WAAW,UAAU;AAC7C,YAAQ,SAAS,OAAO,KAAK,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,EAC/D,WAAW,QAAQ,WAAW,QAAQ,QAAQ,WAAW,OAAO;AAC9D,YAAQ,SAAS;AAAA,EACnB;AACA,MAAI,QAAQ,WAAW,MAAM;AAC3B,QAAI,CAAC,OAAO,SAAS,QAAQ,MAAM,GAAG;AACpC,YAAM,IAAI;AAAA,QACR,uEAAuE,KAAK,UAAU,QAAQ,MAAM,CAAC;AAAA,MACvG;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ,SAAS,UAAa,QAAQ,SAAS,MAAM;AACvD,YAAQ,OAAO;AAAA,EACjB,OAAO;AACL,QAAI,OAAO,QAAQ,SAAS,YAAY,MAAM,KAAK,QAAQ,IAAI,GAAG;AAChE,cAAQ,OAAO,SAAS,QAAQ,IAAI;AAAA,IACtC;AACA,QAAI,OAAO,UAAU,QAAQ,IAAI,GAAG;AAClC,UAAI,QAAQ,OAAO,GAAG;AACpB,cAAM,IAAI;AAAA,UACR,wDAAwD,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,QACnF;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,IAAI;AAAA,QACR,gDAAgD,KAAK,UAAU,QAAQ,IAAI,CAAC;AAAA,MAC9E;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ,cAAc,UAAa,QAAQ,cAAc,MAAM;AACjE,YAAQ,YAAY;AAAA,EACtB,OAAO;AACL,QACE,OAAO,QAAQ,cAAc,YAC7B,MAAM,KAAK,QAAQ,SAAS,GAC5B;AACA,cAAQ,YAAY,SAAS,QAAQ,SAAS;AAAA,IAChD;AACA,QAAI,OAAO,UAAU,QAAQ,SAAS,GAAG;AACvC,UAAI,QAAQ,aAAa,GAAG;AAC1B,cAAM,IAAI;AAAA,UACR,4EAA4E,KAAK,UAAU,KAAK,SAAS,CAAC;AAAA,QAC5G;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,IAAI;AAAA,QACR,qDAAqD,KAAK,UAAU,KAAK,SAAS,CAAC;AAAA,MACrF;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,2BAA2B,UACnC,QAAQ,2BAA2B,MACnC;AACA,YAAQ,yBAAyB;AAAA,EACnC,WAAW,OAAO,QAAQ,2BAA2B,UAAU;AAC7D,YAAQ,yBAAyB,KAAK,MAAM,QAAQ,sBAAsB;AAC1E,QAAI,QAAQ,2BAA2B,GAAG;AACxC,cAAQ,yBAAyB;AAAA,IACnC;AAAA,EACF,WAAW,OAAO,QAAQ,2BAA2B,WAAW;AAC9D,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,sBAAsB,CAAC;AAAA,MACvD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQ,2BAA2B,QAAQ,QAAQ,YAAY,OAAO;AACxE,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,SAAS,UACjB,QAAQ,SAAS,QACjB,QAAQ,SAAS,OACjB;AACA,YAAQ,OAAO;AAAA,EACjB,WAAW,QAAQ,SAAS,MAAM;AAChC,UAAM,IAAI;AAAA,MACR,0CAA0C,KAAK,UAAU,QAAQ,IAAI,CAAC;AAAA,IACxE;AAAA,EACF;AAEA,MACE,QAAQ,oBAAoB,UAC5B,QAAQ,oBAAoB,QAC5B,QAAQ,oBAAoB,OAC5B;AACA,YAAQ,kBAAkB;AAAA,EAC5B,WACE,OAAO,UAAU,QAAQ,eAAe,KACxC,QAAQ,mBAAmB,GAC3B;AAAA,EAEF,WACE,OAAO,QAAQ,oBAAoB,YACnC,MAAM,KAAK,QAAQ,eAAe,GAClC;AACA,YAAQ,kBAAkB,SAAS,QAAQ,eAAe;AAAA,EAC5D,OAAO;AACL,UAAM,IAAI;AAAA,MACR,mEAAmE,KAAK,UAAU,QAAQ,eAAe,CAAC;AAAA,IAC5G;AAAA,EACF;AAEA,MACE,QAAQ,YAAY,UACpB,QAAQ,YAAY,QACpB,QAAQ,YAAY,OACpB;AACA,YAAQ,UAAU;AAAA,EACpB,WAAW,OAAO,SAAS,QAAQ,OAAO,GAAG;AAC3C,QAAI,QAAQ,QAAQ,WAAW,GAAG;AAChC,YAAM,IAAI,MAAM,oDAAoD;AAAA,IACtE;AACA,QAAI,QAAQ,aAAa,MAAM;AAAA,IAE/B,OAAO;AACL,cAAQ,UAAU,QAAQ,QAAQ,SAAS,QAAQ,QAAQ;AAAA,IAC7D;AAAA,EACF,WAAW,OAAO,QAAQ,YAAY,UAAU;AAC9C,QAAI,QAAQ,QAAQ,WAAW,GAAG;AAChC,YAAM,IAAI,MAAM,oDAAoD;AAAA,IACtE;AAAA,EAEF,WAAW,OAAO,QAAQ,YAAY,UAAU;AAAA,EAKhD,OAAO;AACL,UAAM,IAAI;AAAA,MACR,6DAA6D,QAAQ,OAAO;AAAA,IAC9E;AAAA,EACF;AACA,MAAI,QAAQ,YAAY,QAAW;AACjC,QAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,UAAI,QAAQ,YAAY,OAAO;AAC7B,cAAM;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AAEL,UAAI,QAAQ,YAAY,OAAO;AAC7B,cAAM;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ,cAAc,UAAa,QAAQ,cAAc,MAAM;AACjE,YAAQ,YAAY;AAAA,EACtB,WAAW,OAAO,QAAQ,cAAc,YAAY;AAClD,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,SAAS,CAAC;AAAA,MAC1C;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAKA,MACE,QAAQ,YAAY,UACpB,QAAQ,YAAY,QACpB,OAAO,QAAQ,YAAY,YAC3B;AACA,UAAM,IAAI;AAAA,MACR,mDAAmD,KAAK,UAAU,QAAQ,OAAO,CAAC;AAAA,IACpF;AAAA,EACF;AAEA,MACE,QAAQ,UAAU,QAClB,QAAQ,UAAU,SAClB,QAAQ,UAAU,IAClB;AACA,YAAQ,QAAQ;AAAA,EAClB,OAAO;AACL,QAAI,QAAQ,UAAU,UAAa,QAAQ,UAAU,MAAM;AACzD,cAAQ,QAAQ,OAAO,KAAK,KAAK,QAAQ,QAAQ;AAAA,IACnD,WAAW,OAAO,QAAQ,UAAU,UAAU;AAC5C,cAAQ,QAAQ,OAAO,KAAK,QAAQ,OAAO,QAAQ,QAAQ;AAAA,IAC7D;AACA,QAAI,CAAC,OAAO,SAAS,QAAQ,KAAK,GAAG;AACnC,YAAM,IAAI;AAAA,QACR,2DAA2D,KAAK,UAAU,QAAQ,KAAK,CAAC;AAAA,MAC1F;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,QAAQ,UAChB,QAAQ,QAAQ,QAChB,QAAQ,QAAQ,OAChB;AACA,YAAQ,MAAM;AAAA,EAChB,WAAW,QAAQ,QAAQ,MAAM;AAC/B,UAAM,IAAI;AAAA,MACR,yCAAyC,KAAK,UAAU,QAAQ,GAAG,CAAC;AAAA,IACtE;AAAA,EACF;AAEA,MAAI,QAAQ,qBAAqB,QAAW;AAC1C,YAAQ,mBAAmB,CAAC;AAAA,EAC9B,WACE,OAAO,QAAQ,qBAAqB,YACpC,OAAO,SAAS,QAAQ,gBAAgB,GACxC;AACA,QAAI,QAAQ,iBAAiB,WAAW,GAAG;AACzC,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,OAAO,KAAK,UAAU,QAAQ,gBAAgB,CAAC;AAAA,QACjD;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,YAAQ,mBAAmB,CAAC,QAAQ,gBAAgB;AAAA,EACtD,WAAW,CAAC,MAAM,QAAQ,QAAQ,gBAAgB,GAAG;AACnD,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,gBAAgB,CAAC;AAAA,MACjD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,UAAQ,mBAAmB,QAAQ,iBAAiB,IAAI,SAAU,IAAI,GAAG;AACvE,QAAI,OAAO,OAAO,YAAY,CAAC,OAAO,SAAS,EAAE,GAAG;AAClD,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,YAAY,CAAC;AAAA,UACb,OAAO,KAAK,UAAU,EAAE,CAAC;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AAAA,IACF,WAAW,GAAG,WAAW,GAAG;AAC1B,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,YAAY,CAAC;AAAA,UACb,OAAO,KAAK,UAAU,EAAE,CAAC;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,OAAO,UAAU;AAC1B,WAAK,OAAO,KAAK,IAAI,QAAQ,QAAQ;AAAA,IACvC;AACA,WAAO;AAAA,EACT,CAAC;AAED,MAAI,OAAO,QAAQ,uBAAuB,WAAW;AAAA,EAErD,WACE,QAAQ,uBAAuB,UAC/B,QAAQ,uBAAuB,MAC/B;AACA,YAAQ,qBAAqB;AAAA,EAC/B,OAAO;AACL,UAAM,IAAI;AAAA,MACR,6DAA6D,KAAK,UAAU,QAAQ,kBAAkB,CAAC;AAAA,IACzG;AAAA,EACF;AACA,MAAI,OAAO,QAAQ,4BAA4B,WAAW;AAAA,EAE1D,WACE,QAAQ,4BAA4B,UACpC,QAAQ,4BAA4B,MACpC;AACA,YAAQ,0BAA0B;AAAA,EACpC,OAAO;AACL,UAAM,IAAI;AAAA,MACR,kEAAkE,KAAK,UAAU,QAAQ,uBAAuB,CAAC;AAAA,IACnH;AAAA,EACF;AACA,MAAI,OAAO,QAAQ,4BAA4B,WAAW;AAAA,EAE1D,WACE,QAAQ,4BAA4B,UACpC,QAAQ,4BAA4B,MACpC;AACA,YAAQ,0BAA0B;AAAA,EACpC,OAAO;AACL,UAAM,IAAI;AAAA,MACR,kEAAkE,KAAK,UAAU,QAAQ,uBAAuB,CAAC;AAAA,IACnH;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,iBAAiB,WAAW;AAAA,EAE/C,WACE,QAAQ,iBAAiB,UACzB,QAAQ,iBAAiB,MACzB;AACA,YAAQ,eAAe;AAAA,EACzB,OAAO;AACL,UAAM,IAAI;AAAA,MACR,uDAAuD,KAAK,UAAU,QAAQ,YAAY,CAAC;AAAA,IAC7F;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,qBAAqB,WAAW;AAAA,EAEnD,WACE,QAAQ,qBAAqB,UAC7B,QAAQ,qBAAqB,MAC7B;AACA,YAAQ,mBAAmB;AAAA,EAC7B,OAAO;AACL,UAAM,IAAI;AAAA,MACR,2DAA2D,KAAK,UAAU,QAAQ,gBAAgB,CAAC;AAAA,IACrG;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,mCAAmC,WAAW;AAAA,EAEjE,WACE,QAAQ,mCAAmC,UAC3C,QAAQ,mCAAmC,MAC3C;AACA,YAAQ,iCAAiC;AAAA,EAC3C,OAAO;AACL,UAAM,IAAI;AAAA,MACR,yEAAyE,KAAK,UAAU,QAAQ,8BAA8B,CAAC;AAAA,IACjI;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,4BAA4B,WAAW;AAAA,EAE1D,WACE,QAAQ,4BAA4B,UACpC,QAAQ,4BAA4B,MACpC;AACA,YAAQ,0BAA0B;AAAA,EACpC,OAAO;AACL,UAAM,IAAI;AAAA,MACR,kEAAkE,KAAK,UAAU,QAAQ,uBAAuB,CAAC;AAAA,IACnH;AAAA,EACF;AAEA,MACE,QAAQ,UAAU,UAClB,QAAQ,UAAU,QAClB,QAAQ,UAAU,OAClB;AACA,YAAQ,QAAQ;AAAA,EAClB,WAAW,QAAQ,UAAU,MAAM;AACjC,UAAM,IAAI;AAAA,MACR,gDAAgD,KAAK,UAAU,QAAQ,KAAK,CAAC;AAAA,IAC/E;AAAA,EACF;AAEA,MACE,QAAQ,UAAU,UAClB,QAAQ,UAAU,QAClB,QAAQ,UAAU,OAClB;AACA,YAAQ,QAAQ;AAAA,EAClB,WAAW,QAAQ,UAAU,MAAM;AACjC,UAAM,IAAI;AAAA,MACR,gDAAgD,KAAK,UAAU,QAAQ,KAAK,CAAC;AAAA,IAC/E;AAAA,EACF;AAEA,MACE,QAAQ,SAAS,UACjB,QAAQ,SAAS,QACjB,QAAQ,SAAS,OACjB;AACA,YAAQ,OAAO;AAAA,EACjB,WAAW,QAAQ,SAAS,MAAM;AAChC,UAAM,IAAI;AAAA,MACR,+CAA+C,KAAK,UAAU,QAAQ,IAAI,CAAC;AAAA,IAC7E;AAAA,EACF;AAEA,MAAI,QAAQ,SAAS,QAAQ,KAAK,UAAU,OAAO;AACjD,YAAQ,QAAQ;AAAA,EAClB,WAAW,QAAQ,UAAU,MAAM;AACjC,YAAQ,QAAQ;AAAA,EAClB;AACA,MAAI,QAAQ,SAAS,QAAQ,KAAK,UAAU,OAAO;AACjD,YAAQ,QAAQ;AAAA,EAClB,WAAW,QAAQ,UAAU,MAAM;AACjC,YAAQ,QAAQ;AAAA,EAClB;AAEA,MAAI,QAAQ,OAAO,UAAa,QAAQ,OAAO,MAAM;AACnD,YAAQ,KAAK;AAAA,EACf,OAAO;AACL,QAAI,OAAO,QAAQ,OAAO,YAAY,MAAM,KAAK,QAAQ,EAAE,GAAG;AAC5D,cAAQ,KAAK,SAAS,QAAQ,EAAE;AAAA,IAClC;AACA,QAAI,OAAO,UAAU,QAAQ,EAAE,GAAG;AAChC,UAAI,QAAQ,MAAM,GAAG;AACnB,cAAM,IAAI;AAAA,UACR,qEAAqE,KAAK,UAAU,KAAK,EAAE,CAAC;AAAA,QAC9F;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,IAAI;AAAA,QACR,8CAA8C,KAAK,UAAU,KAAK,EAAE,CAAC;AAAA,MACvE;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ,YAAY,UAAa,QAAQ,YAAY,MAAM;AAC7D,YAAQ,UAAU;AAAA,EACpB,OAAO;AACL,QAAI,OAAO,QAAQ,YAAY,YAAY,MAAM,KAAK,QAAQ,OAAO,GAAG;AACtE,cAAQ,UAAU,SAAS,QAAQ,OAAO;AAAA,IAC5C;AACA,QAAI,OAAO,UAAU,QAAQ,OAAO,GAAG;AACrC,UAAI,QAAQ,WAAW,GAAG;AACxB,cAAM,IAAI;AAAA,UACR,0EAA0E,KAAK,UAAU,KAAK,OAAO,CAAC;AAAA,QACxG;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,IAAI;AAAA,QACR,mDAAmD,KAAK,UAAU,KAAK,OAAO,CAAC;AAAA,MACjF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;AC3qBA,IAAM,gBAAgB,SAAU,QAAQ;AACtC,SAAO,OAAO;AAAA,IACZ,CAAC,UACC,SAAS,QAAS,MAAM,YAAY,MAAM,SAAS,EAAE,KAAK,MAAM;AAAA,EACpE;AACF;AAEA,IAAMA,MAAK;AACX,IAAMC,MAAK;AAEX,IAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,MAAM,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA,EAIjC,SAAS,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC;AACjC;AAEA,IAAM,YAAY,SAAU,mBAAmB,CAAC,GAAG;AACjD,QAAM,OAAO;AAAA,IACX,OAAO;AAAA,IACP,eAAe;AAAA,IACf,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AACA,QAAM,UAAU,kBAAkB,gBAAgB;AAClD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,WAAW,OAAO;AAAA,IACzB,gBAAgB,SAAU,GAAG,QAAQ,KAAK;AACxC,UAAI,IAAK,QAAO;AAChB,YAAM,EAAE,UAAU,QAAQ,MAAM,IAAI,KAAK;AACzC,YAAM,EAAE,SAAS,kBAAkB,yBAAyB,IAC1D,KAAK;AACP,YAAM,gBAAgB,SAAS,IAAI;AACnC,YAAM,iBAAiB,KAAK;AAAA,QAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,6BAA6B,IACzB,OAAO,KAAK,QAAQ,QAAQ,EAAE,SAC9B;AAAA;AAAA,QAEJ,WAAW,WAAW,OAAO,IAAI,OAAO,UAAU,MAAM,SAAS;AAAA;AAAA,QAEjE,UAAU,MAAM,SAAS,2BAA2B;AAAA,MACtD;AACA,aAAO,gBAAgB;AAAA,IACzB;AAAA;AAAA,IAEA,OAAO,SAAU,SAAS,KAAK,MAAM,OAAO;AAC1C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,UAAI,EAAE,SAAS,QAAQ,OAAO,iBAAiB,IAAI,KAAK;AACxD,YAAM,EAAE,YAAY,aAAa,WAAW,cAAc,IAAI,KAAK;AACnE,UAAI;AACJ,UAAI,gBAAgB,QAAW;AAC7B,YAAI,YAAY,QAAW;AAEzB,gBAAM;AACN;AAAA,QACF,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF,WAAW,gBAAgB,UAAa,YAAY,QAAW;AAC7D,cAAM;AAAA,MACR,OAAO;AACL,cAAM,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC;AAAA,MAC5C;AAEA,UAAI,eAAe,OAAO;AACxB,YAAI,QAAQ,OAAO;AACjB,eAAK,MAAM,aAAa;AAAA,QAC1B,WAAW,IAAI,SAAS,GAAG;AAEzB,cAAI,QAAQ,OAAO;AAEjB,iBAAK,MAAM,cAAc;AACzB;AAAA,UACF;AAAA,QACF,OAAO;AACL,qBAAWC,aAAY,MAAM;AAC3B,gBAAI,KAAKA,SAAQ,EAAE,QAAQ,KAAK,GAAG,KAAKA,SAAQ,EAAE,MAAM,MAAM,GAAG;AAE/D,oBAAM,YAAY,KAAKA,SAAQ,EAAE;AACjC,mBAAK,MAAM,iBAAiB;AAC5B,oBAAM,IAAI,MAAM,SAAS;AAEzB,mBAAK,UAAU,kBAAkB;AAAA,gBAC/B,GAAG,KAAK;AAAA,gBACR,UAAUA;AAAA,cACZ,CAAC;AAED,eAAC,EAAE,SAAS,QAAQ,MAAM,IAAI,KAAK;AACnC;AAAA,YACF;AAAA,UACF;AACA,eAAK,MAAM,aAAa;AAAA,QAC1B;AAAA,MACF;AACA,YAAM,SAAS,IAAI;AACnB,UAAI;AACJ,WAAK,MAAM,GAAG,MAAM,QAAQ,OAAO;AAGjC,YAAI,KAAK,eAAe,KAAK,QAAQ,GAAG,GAAG;AACzC;AAAA,QACF;AACA,YAAI,KAAK,MAAM,oBAAoB,MAAM;AACvC,eAAK,KAAK;AACV,eAAK,MAAM,kBAAkB;AAAA,QAC/B;AACA,YAAI,YAAY,MAAM,KAAK,KAAK,QAAQ,SAAS;AAC/C,eAAK,MAAM,OAAO;AAClB,gBAAM;AACN;AAAA,QACF;AAEA,YAAI,KAAK,MAAM,YAAY,SAAS,iBAAiB,WAAW,GAAG;AACjE,gBAAM,wBAAwB,KAAK;AAAA,YACjC;AAAA,YACA;AAAA,UACF;AACA,cAAI,uBAAuB;AACzB,+BAAmB,KAAK,QAAQ;AAAA,UAClC;AAAA,QACF;AACA,cAAM,MAAM,IAAI,GAAG;AACnB,YAAI,QAAQ,MAAM;AAChB,oBAAU,OAAO,GAAG;AAAA,QACtB;AACA,aACG,QAAQF,OAAM,QAAQC,QACvB,KAAK,MAAM,oBAAoB,OAC/B;AACA,eAAK,MAAM,kBAAkB;AAAA,QAC/B;AAGA,YAAI,KAAK,MAAM,aAAa,MAAM;AAChC,eAAK,MAAM,WAAW;AAAA,QACxB,OAAO;AAIL,cACE,WAAW,QACX,KAAK,MAAM,YAAY,QACvB,KAAK,WAAW,KAAK,KAAK,GAAG,KAC7B,MAAM,OAAO,SAAS,QACtB;AACA,gBAAI,eAAe;AACjB,kBAAI,KAAK,UAAU,KAAK,MAAM,OAAO,MAAM,GAAG;AAC5C,qBAAK,MAAM,WAAW;AACtB,uBAAO,OAAO,SAAS;AACvB;AAAA,cACF;AAAA,YACF,OAAO;AACL,mBAAK,MAAM,WAAW;AACtB,qBAAO,OAAO,SAAS;AACvB;AAAA,YACF;AAAA,UACF;AAGA,cAAI,KAAK,MAAM,eAAe,SAAS,KAAK,UAAU,KAAK,GAAG,GAAG;AAC/D,gBAAI,KAAK,MAAM,YAAY,MAAM;AAC/B,oBAAM,UAAU,IAAI,MAAM,MAAM,MAAM;AACtC,oBAAM,oBACJ,SAAS,KAAK,iBAAiB,KAAK,MAAM,MAAM,MAAM;AACxD,oBAAM,mBACJ,YAAY,QACZ,KAAK,eAAe,SAAS,KAAK,MAAM,MAAM,QAAQ,OAAO;AAC/D,oBAAM,qBAAqB,KAAK;AAAA,gBAC9B;AAAA,gBACA,MAAM,MAAM;AAAA,gBACZ;AAAA,cACF;AACA,oBAAM,2BACJ,iBAAiB,WAAW,IACxB,KAAK,8BAA8B,KAAK,MAAM,MAAM,MAAM,IAC1D,KAAK,oBAAoB,SAAS,KAAK,MAAM,MAAM,MAAM;AAG/D,kBACE,WAAW,QACX,KAAK,WAAW,KAAK,KAAK,GAAG,KAC7B,KAAK,UAAU,KAAK,MAAM,OAAO,MAAM,GACvC;AACA,uBAAO,OAAO,SAAS;AAAA,cACzB,WACE,CAAC,WACD,sBACA,4BACA,oBACA,mBACA;AACA,qBAAK,MAAM,UAAU;AACrB,qBAAK,MAAM,aAAa;AACxB,uBAAO,MAAM,SAAS;AACtB;AAAA,cACF,WAAW,iBAAiB,OAAO;AACjC,sBAAM,MAAM,KAAK;AAAA,kBACf,IAAI;AAAA,oBACF;AAAA,oBACA;AAAA,sBACE;AAAA,sBACA,QAAQ,OAAO,aAAa,OAAO,CAAC;AAAA,sBACpC,WAAW,KAAK,KAAK,KAAK;AAAA,sBAC1B;AAAA,sBACA;AAAA,oBACF;AAAA,oBACA,KAAK;AAAA,oBACL,KAAK,YAAY;AAAA,kBACnB;AAAA,gBACF;AACA,oBAAI,QAAQ,OAAW,QAAO;AAAA,cAChC,OAAO;AACL,qBAAK,MAAM,UAAU;AACrB,qBAAK,MAAM,aAAa;AACxB,qBAAK,MAAM,MAAM,QAAQ,KAAK;AAC9B,uBAAO,MAAM,SAAS;AAAA,cACxB;AAAA,YACF,OAAO;AACL,kBAAI,KAAK,MAAM,MAAM,WAAW,GAAG;AAEjC,oBAAI,iBAAiB,OAAO;AAC1B,wBAAME,QAAO,KAAK,YAAY;AAC9B,wBAAMC,OAAM,OAAO,KAAK,IAAI,EACzB;AAAA,oBAAI,CAAC,MACJ,KAAK,CAAC,EAAE,OAAO,KAAK,MAAM,MAAM,SAAS,CAAC,IAAI,IAAI;AAAA,kBACpD,EACC,OAAO,OAAO,EAAE,CAAC;AACpB,wBAAM,MAAM,KAAK;AAAA,oBACf,IAAI;AAAA,sBACF;AAAA,sBACA;AAAA,wBACE;AAAA,wBACA,6BAA6B,KAAK,UAAUD,MAAK,MAAM,CAAC,YAAYA,MAAK,KAAK,cAAc,KAAK,UAAU,KAAK,MAAM,MAAM,SAAS,QAAQ,CAAC,CAAC;AAAA,wBAC/IC,OAAM,IAAIA,IAAG,UAAU;AAAA,sBACzB;AAAA,sBACA,KAAK;AAAA,sBACLD;AAAA,sBACA;AAAA,wBACE,OAAO,KAAK,MAAM;AAAA,sBACpB;AAAA,oBACF;AAAA,kBACF;AACA,sBAAI,QAAQ,OAAW,QAAO;AAAA,gBAChC;AAAA,cACF,OAAO;AACL,qBAAK,MAAM,UAAU;AACrB,uBAAO,MAAM,SAAS;AACtB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,KAAK,MAAM,YAAY,OAAO;AAChC,kBAAM,wBAAwB,KAAK;AAAA,cACjC;AAAA,cACA;AAAA,cACA;AAAA,YACF;AACA,gBAAI,0BAA0B,GAAG;AAE/B,oBAAM,kBACJ,KAAK,MAAM,cACX,KAAK,MAAM,eAAe,SAC1B,KAAK,MAAM,OAAO,WAAW,KAC7B,KAAK,MAAM,MAAM,WAAW;AAC9B,kBAAI,iBAAiB;AACnB,qBAAK,KAAK;AAAA,cAEZ,OAAO;AAEL,oBACE,KAAK,MAAM,YAAY,SACvB,KAAK,KAAK,SACP,KAAK,MAAM,oBAAoB,OAAO,IAAI,MAC3C,WACF;AACA,uBAAK,MAAM,UAAU;AACrB,uBAAK,aAAa;AAClB,uBAAK,cAAc;AACnB,yBAAO,wBAAwB;AAC/B;AAAA,gBACF;AAEA,oBACE,qBAAqB,QACrB,KAAK,MAAM,eAAe,SAC1B,KAAK,MAAM,OAAO,WAAW,KAC7B,KAAK,MAAM,MAAM,WAAW,GAC5B;AACA,uBAAK,KAAK;AACV,yBAAO,wBAAwB;AAC/B;AAAA,gBACF;AACA,qBAAK,KAAK,QAAQ,KAAK,MAAM,gBAAgB;AAC7C,sBAAM,WAAW,KAAK,UAAU;AAChC,oBAAI,aAAa,OAAW,QAAO;AACnC,qBAAK,KAAK,QACR,KAAK,MAAM,gBAAgB,MAAM;AACnC,sBAAM,YAAY,KAAK,WAAW,IAAI;AACtC,oBAAI,cAAc,OAAW,QAAO;AACpC,oBAAI,OAAO,MAAM,KAAK,KAAK,WAAW,IAAI;AACxC,uBAAK,MAAM,OAAO;AAClB,wBAAM;AACN;AAAA,gBACF;AAAA,cACF;AACA,mBAAK,MAAM,aAAa;AACxB,qBAAO,wBAAwB;AAC/B;AAAA,YACF;AACA,gBAAI,KAAK,MAAM,YAAY;AACzB;AAAA,YACF;AACA,gBACE,YAAY,SACX,qBAAqB,SACnB,KAAK,MAAM,OAAO,WAAW,KAC5B,KAAK,MAAM,MAAM,WAAW,IAChC;AACA,oBAAM,eAAe,KAAK,eAAe,SAAS,KAAK,KAAK,GAAG;AAC/D,kBAAI,iBAAiB,GAAG;AACtB,qBAAK,MAAM,aAAa;AACxB;AAAA,cACF;AAAA,YACF;AACA,kBAAM,kBAAkB,KAAK,cAAc,KAAK,KAAK,GAAG;AACxD,gBAAI,oBAAoB,GAAG;AACzB,mBAAK,KAAK,QAAQ,KAAK,MAAM,gBAAgB;AAC7C,oBAAM,WAAW,KAAK,UAAU;AAChC,kBAAI,aAAa,OAAW,QAAO;AACnC,qBAAO,kBAAkB;AACzB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,KAAK,MAAM,eAAe,OAAO;AACnC,cACE,oBAAoB,KACpB,KAAK,MAAM,gBAAgB,KAAK,MAAM,MAAM,SAAS,iBACrD;AACA,mBAAO,KAAK;AAAA,cACV,IAAI;AAAA,gBACF;AAAA,gBACA;AAAA,kBACE;AAAA,kBACA;AAAA,kBACA,MAAM,eAAe;AAAA,kBACrB,WAAW,KAAK,KAAK,KAAK;AAAA,gBAC5B;AAAA,gBACA,KAAK;AAAA,gBACL,KAAK,YAAY;AAAA,cACnB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,cAAM,UACJ,UAAU,SACV,KAAK,MAAM,YAAY,QACvB,KAAK,MAAM,MAAM,WAAW,KAC5B,CAAC,KAAK,iBAAiB,KAAK,GAAG;AAEjC,cAAM,UAAU,UAAU,SAAS,KAAK,MAAM,eAAe;AAC7D,YAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,eAAK,MAAM,MAAM,OAAO,GAAG;AAAA,QAC7B,WAAW,UAAU,QAAQ,CAAC,KAAK,iBAAiB,KAAK,GAAG,GAAG;AAC7D,iBAAO,KAAK;AAAA,YACV,IAAI;AAAA,cACF;AAAA,cACA;AAAA,gBACE;AAAA,gBACA;AAAA,gBACA,WAAW,KAAK,KAAK,KAAK;AAAA,cAC5B;AAAA,cACA,KAAK;AAAA,cACL,KAAK,YAAY;AAAA,YACnB;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAI,YAAY,OAAO;AACrB,mBAAO,KAAK,iBAAiB,KAAK,GAAG,IAAI;AAAA,UAC3C;AACA;AAAA,QACF;AAAA,MACF;AACA,UAAI,QAAQ,MAAM;AAEhB,YAAI,KAAK,MAAM,YAAY,MAAM;AAC/B,gBAAM,MAAM,KAAK;AAAA,YACf,IAAI;AAAA,cACF;AAAA,cACA;AAAA,gBACE;AAAA,gBACA,yDAAyD,KAAK,KAAK,KAAK;AAAA,cAC1E;AAAA,cACA,KAAK;AAAA,cACL,KAAK,YAAY;AAAA,YACnB;AAAA,UACF;AACA,cAAI,QAAQ,OAAW,QAAO;AAAA,QAChC,OAAO;AAEL,cACE,KAAK,MAAM,eAAe,QAC1B,KAAK,MAAM,OAAO,WAAW,KAC7B,KAAK,MAAM,MAAM,WAAW,GAC5B;AACA,iBAAK,KAAK,QAAQ,KAAK,MAAM,gBAAgB;AAC7C,kBAAM,WAAW,KAAK,UAAU;AAChC,gBAAI,aAAa,OAAW,QAAO;AACnC,kBAAM,YAAY,KAAK,WAAW,IAAI;AACtC,gBAAI,cAAc,OAAW,QAAO;AAAA,UACtC,WAAW,KAAK,MAAM,oBAAoB,MAAM;AAC9C,iBAAK,KAAK;AAAA,UACZ,WAAW,KAAK,MAAM,eAAe,MAAM;AACzC,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF;AAAA,MACF,OAAO;AACL,aAAK,MAAM,iBAAiB;AAC5B,aAAK,MAAM,cAAc,IAAI,MAAM,GAAG;AAAA,MACxC;AACA,UAAI,KAAK,MAAM,oBAAoB,MAAM;AACvC,aAAK,KAAK;AACV,aAAK,MAAM,kBAAkB;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,YAAY,SAAU,MAAM;AAC1B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA,MAAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,YAAM,EAAE,SAAS,OAAO,IAAI,KAAK;AACjC,UAAI,YAAY,OAAO;AACrB,eAAO,KAAK,cAAc;AAAA,MAC5B;AAEA,YAAM,eAAe,OAAO;AAC5B,UAAI,YAAY,MAAM;AACpB,YAAI,mCAAmC,QAAQ,cAAc,MAAM,GAAG;AACpE,eAAK,cAAc;AACnB;AAAA,QACF;AACA,eAAO,KAAK,qBAAqB,MAAM;AAAA,MACzC;AACA,UAAI,YAAY,SAAS,KAAK,KAAK,YAAY,GAAG;AAChD,aAAK,MAAM,uBAAuB;AAAA,MACpC;AACA,UAAI,iBAAiB,KAAK,MAAM,sBAAsB;AACpD,cAAM,MACJ,YAAY,QACR,IAAI;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA,UAAU,KAAK,MAAM,oBAAoB;AAAA,YACzC,OAAO,YAAY,YAAY,KAAK,KAAK,KAAK;AAAA,UAChD;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY;AAAA,UACjB;AAAA,YACE;AAAA,UACF;AAAA,QACF,IACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA,qBAAqB,QAAQ,MAAM;AAAA;AAAA,YACnC,OAAO,YAAY,YAAY,KAAK,KAAK,KAAK;AAAA,UAChD;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY;AAAA,UACjB;AAAA,YACE;AAAA,UACF;AAAA,QACF;AACN,YACE,uBAAuB,QACtB,4BAA4B,QAC3B,eAAe,KAAK,MAAM,wBAC3B,4BAA4B,QAC3B,eAAe,KAAK,MAAM,sBAC5B;AACA,eAAK,KAAK;AACV,eAAK,MAAM,QAAQ;AAAA,QAErB,OAAO;AACL,gBAAM,WAAW,KAAK,QAAQ,GAAG;AACjC,cAAI,SAAU,QAAO;AAAA,QACvB;AAAA,MACF;AACA,UAAI,mCAAmC,QAAQ,cAAc,MAAM,GAAG;AACpE,aAAK,cAAc;AACnB;AAAA,MACF;AACA,UAAI,KAAK,MAAM,mBAAmB,MAAM;AACtC,aAAK,cAAc;AACnB,aAAK,MAAM,iBAAiB;AAC5B;AAAA,MACF;AACA,WAAK,KAAK;AACV,UAAI,SAAS,KAAK,KAAK,KAAK,WAAW,MAAM;AAC3C,cAAM,EAAE,QAAQ,IAAI,KAAK;AAEzB,YAAI,YAAY,OAAO;AACrB,gBAAM,MAAM,CAAC;AAEb,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,gBAAI,QAAQ,CAAC,MAAM,UAAa,QAAQ,CAAC,EAAE,SAAU;AAErD,gBACE,0BAA0B,QAC1B,IAAI,QAAQ,CAAC,EAAE,IAAI,MAAM,QACzB;AACA,kBAAI,MAAM,QAAQ,IAAI,QAAQ,CAAC,EAAE,IAAI,CAAC,GAAG;AACvC,oBAAI,QAAQ,CAAC,EAAE,IAAI,IAAI,IAAI,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,OAAO,CAAC,CAAC;AAAA,cAC9D,OAAO;AACL,oBAAI,QAAQ,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,QAAQ,CAAC,EAAE,IAAI,GAAG,OAAO,CAAC,CAAC;AAAA,cACzD;AAAA,YACF,OAAO;AACL,kBAAI,QAAQ,CAAC,EAAE,IAAI,IAAI,OAAO,CAAC;AAAA,YACjC;AAAA,UACF;AAEA,cAAI,QAAQ,QAAQA,UAAS,MAAM;AACjC,kBAAM,YAAY,OAAO;AAAA,cACvB,EAAE,QAAQ,IAAI;AAAA,cACd,QAAQ,OACJ,EAAE,KAAK,KAAK,MAAM,UAAU,SAAS,QAAQ,EAAE,IAC/C,CAAC;AAAA,cACLA,UAAS,OAAO,EAAE,MAAM,KAAK,aAAa,EAAE,IAAI,CAAC;AAAA,YACnD;AACA,kBAAM,MAAM,KAAK;AAAA,cACf,YAAY,SAAY,YAAY,CAAC,IAAI,OAAO,GAAG,SAAS;AAAA,cAC5D;AAAA,YACF;AACA,gBAAI,KAAK;AACP,qBAAO;AAAA,YACT;AAAA,UACF,OAAO;AACL,kBAAM,MAAM,KAAK;AAAA,cACf,YAAY,SAAY,MAAM,CAAC,IAAI,OAAO,GAAG,GAAG;AAAA,cAChD;AAAA,YACF;AACA,gBAAI,KAAK;AACP,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QAEF,OAAO;AACL,cAAI,QAAQ,QAAQA,UAAS,MAAM;AACjC,kBAAM,YAAY,OAAO;AAAA,cACvB,EAAE,OAAe;AAAA,cACjB,QAAQ,OACJ,EAAE,KAAK,KAAK,MAAM,UAAU,SAAS,QAAQ,EAAE,IAC/C,CAAC;AAAA,cACLA,UAAS,OAAO,EAAE,MAAM,KAAK,aAAa,EAAE,IAAI,CAAC;AAAA,YACnD;AACA,kBAAM,MAAM,KAAK;AAAA,cACf,YAAY,SAAY,YAAY,CAAC,OAAO,OAAO,GAAG,SAAS;AAAA,cAC/D;AAAA,YACF;AACA,gBAAI,KAAK;AACP,qBAAO;AAAA,YACT;AAAA,UACF,OAAO;AACL,kBAAM,MAAM,KAAK;AAAA,cACf,YAAY,SAAY,SAAS,CAAC,OAAO,OAAO,GAAG,MAAM;AAAA,cACzD;AAAA,YACF;AACA,gBAAI,KAAK;AACP,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,WAAK,cAAc;AAAA,IACrB;AAAA,IACA,sBAAsB,SAAU,QAAQ;AACtC,YAAM,EAAE,mBAAmB,IAAI,KAAK;AACpC,UAAI;AACF,cAAM,UACJ,uBAAuB,SACnB,SACA,mBAAmB,KAAK,MAAM,MAAM;AAC1C,YAAI,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC3B,iBAAO,KAAK;AAAA,YACV,IAAI;AAAA,cACF;AAAA,cACA;AAAA,gBACE;AAAA,gBACA;AAAA,gBACA,OAAO,KAAK,UAAU,OAAO,CAAC;AAAA,cAChC;AAAA,cACA,KAAK;AAAA,cACL,KAAK,YAAY;AAAA,cACjB;AAAA,gBACE;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,cAAM,oBAAoB,wBAAwB,OAAO;AACzD,aAAK,MAAM,uBAAuB,kBAAkB;AACpD,aAAK,QAAQ,UAAU;AACvB,aAAK,cAAc;AACnB;AAAA,MACF,SAAS,KAAK;AACZ,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,eAAe,WAAY;AACzB,UAAI,KAAK,QAAQ,QAAQ,MAAM;AAC7B,aAAK,MAAM,UAAU,MAAM;AAAA,MAC7B;AACA,WAAK,MAAM,QAAQ;AACnB,WAAK,MAAM,SAAS,CAAC;AACrB,WAAK,MAAM,gBAAgB;AAAA,IAC7B;AAAA,IACA,WAAW,WAAY;AACrB,YAAM,EAAE,MAAM,UAAU,OAAO,gBAAgB,IAAI,KAAK;AACxD,YAAM,EAAE,SAAS,WAAW,IAAI,KAAK;AAErC,UAAI,YAAY,OAAO;AACrB,eAAO,KAAK,aAAa;AAAA,MAC3B;AACA,UAAI,QAAQ,KAAK,MAAM,MAAM,SAAS,QAAQ;AAC9C,UAAI,UAAU,QAAQ,eAAe,OAAO;AAC1C,gBAAQ,MAAM,UAAU;AAAA,MAC1B;AACA,UAAI,SAAS,MAAM;AACjB,cAAM,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,KAAK;AAClC,YAAI,QAAQ,OAAW,QAAO;AAC9B,gBAAQ;AAAA,MACV;AACA,WAAK,MAAM,OAAO,KAAK,KAAK;AAE5B,UAAI,oBAAoB,KAAK,OAAO,UAAU,UAAU;AACtD,aAAK,MAAM,iBAAiB,MAAM;AAAA,MACpC;AACA,WAAK,aAAa;AAAA,IACpB;AAAA,IACA,cAAc,WAAY;AACxB,WAAK,MAAM,MAAM,MAAM;AACvB,WAAK,MAAM,aAAa;AAAA,IAC1B;AAAA,IACA,QAAQ,SAAU,QAAQ,MAAM;AAC9B,YAAM,EAAE,UAAU,IAAI,KAAK;AAC3B,UAAI,cAAc,QAAW;AAC3B,cAAMA,QAAO,KAAK,aAAa;AAC/B,YAAI;AACF,mBAAS,UAAU,KAAK,MAAM,QAAQA,KAAI;AAAA,QAC5C,SAAS,KAAK;AACZ,iBAAO;AAAA,QACT;AACA,YAAI,WAAW,UAAa,WAAW,MAAM;AAC3C;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM;AAAA,IACb;AAAA;AAAA,IAEA,QAAQ,SAAU,OAAO;AACvB,YAAM,EAAE,SAAS,mBAAmB,IAAI,KAAK;AAC7C,YAAM,YAAY,MAAM,QAAQ,OAAO;AAIvC,UACE,cAAc,QACd,sBACA,KAAK,QAAQ,QAAQ,UAAU,KAAK,MAAM,OAAO,QACjD;AACA,eAAO,CAAC,QAAW,MAAS;AAAA,MAC9B;AACA,UAAI,KAAK,MAAM,cAAc,MAAM;AACjC,YAAI;AACF,gBAAMA,QAAO,KAAK,YAAY;AAC9B,iBAAO,CAAC,QAAW,KAAK,MAAM,UAAU,KAAK,MAAM,OAAOA,KAAI,CAAC;AAAA,QACjE,SAAS,KAAK;AACZ,iBAAO,CAAC,GAAG;AAAA,QACb;AAAA,MACF;AACA,UAAI,KAAK,UAAU,KAAK,GAAG;AACzB,eAAO,CAAC,QAAW,WAAW,KAAK,CAAC;AAAA,MACtC,WAAW,KAAK,QAAQ,cAAc,OAAO;AAC3C,cAAMA,QAAO,KAAK,YAAY;AAC9B,eAAO,CAAC,QAAW,KAAK,QAAQ,UAAU,KAAK,MAAM,OAAOA,KAAI,CAAC;AAAA,MACnE;AACA,aAAO,CAAC,QAAW,KAAK;AAAA,IAC1B;AAAA;AAAA,IAEA,kBAAkB,SAAU,KAAK,KAAK;AACpC,YAAM,SAAS,CAACE,MAAKC,SAAQ;AAC3B,cAAM,EAAE,SAAS,IAAI,KAAK;AAC1B,cAAO,UAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AAC/C,gBAAM,UAAU,SAAS,CAAC;AAC1B,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAI,QAAQ,CAAC,MAAMD,KAAIC,OAAM,CAAC,EAAG,UAAS;AAAA,UAC5C;AACA,iBAAO,QAAQ;AAAA,QACjB;AACA,eAAO;AAAA,MACT;AACA,aAAO,OAAO,KAAK,GAAG;AAAA,IACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,WAAW,SAAU,OAAO;AAC1B,aAAO,QAAQ,WAAW,KAAK,IAAI,KAAK;AAAA,IAC1C;AAAA,IACA,gBAAgB,SAAU,WAAW,WAAW,WAAW,WAAW;AACpE,UAAI,UAAU,CAAC,MAAM,UAAW,QAAO;AACvC,YAAM,eAAe,UAAU;AAC/B,eAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACrC,YAAI,UAAU,CAAC,MAAM,UAAU,YAAY,CAAC,EAAG,QAAO;AAAA,MACxD;AACA,aAAO;AAAA,IACT;AAAA,IACA,eAAe,SAAU,KAAK,KAAK,KAAK;AACtC,YAAM,EAAE,WAAW,uBAAuB,IAAI,KAAK;AACnD,UACE,2BAA2B,QAC3B,KAAK,MAAM,OAAO,WAAW,KAAK,QAAQ,QAAQ,SAAS,GAC3D;AACA,eAAO;AAAA,MACT,WACE,2BAA2B,SAC3B,OAAO,2BAA2B,YAClC,KAAK,MAAM,OAAO,WAAW,yBAAyB,GACtD;AACA,eAAO;AAAA,MACT;AACA,YAAO,UAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAChD,cAAM,MAAM,UAAU,CAAC;AACvB,YAAI,IAAI,CAAC,MAAM,KAAK;AAClB,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,gBAAI,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,EAAG,UAAS;AAAA,UACxC;AACA,iBAAO,IAAI;AAAA,QACb;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,IACA,qBAAqB,SAAU,KAAK,KAAK,KAAK;AAC5C,YAAM,EAAE,iBAAiB,IAAI,KAAK;AAClC,YAAM,wBAAwB,iBAAiB;AAC/C,YAAO,UAAS,IAAI,GAAG,IAAI,uBAAuB,KAAK;AACrD,cAAM,KAAK,iBAAiB,CAAC;AAC7B,cAAM,WAAW,GAAG;AACpB,YAAI,GAAG,CAAC,MAAM,KAAK;AACjB;AAAA,QACF;AACA,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,cAAI,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,GAAG;AAC1B,qBAAS;AAAA,UACX;AAAA,QACF;AACA,eAAO,GAAG;AAAA,MACZ;AACA,aAAO;AAAA,IACT;AAAA,IACA,YAAY,SAAU,KAAK,KAAK,KAAK;AACnC,YAAM,EAAE,OAAO,IAAI,KAAK;AACxB,UAAI,WAAW,KAAM,QAAO;AAC5B,YAAM,IAAI,OAAO;AACjB,UAAI,OAAO,CAAC,MAAM,KAAK;AACrB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,GAAG;AAC9B,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,IACA,WAAW,SAAU,KAAK,KAAK;AAC7B,YAAM,EAAE,MAAM,IAAI,KAAK;AACvB,UAAI,UAAU,KAAM,QAAO;AAC3B,YAAM,IAAI,MAAM;AAChB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,GAAG;AAC7B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,IACA,+BAA+B,SAAU,KAAK,KAAK;AACjD,YAAM,EAAE,SAAS,IAAI,KAAK;AAI1B,YAAM,MAAM;AAAA;AAAA,QAEV,OAAO,KAAK,QAAQ,QAAQ;AAAA,QAC5B,OAAO,KAAK,MAAM,QAAQ;AAAA,QAC1B,OAAO,KAAK,MAAM,QAAQ;AAAA,MAC5B;AACA,WAAM,UAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACzC,cAAM,IAAI,IAAI,CAAC,EAAE;AACjB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,IAAI,CAAC,EAAE,CAAC,MAAM,IAAI,MAAM,CAAC,GAAG;AAC9B,qBAAS;AAAA,UACX;AAAA,QACF;AACA,aAAK,QAAQ,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACzC,aAAK,MAAM,2BAA2B,IAAI,CAAC,EAAE;AAC7C,eAAO,IAAI,CAAC,EAAE;AAAA,MAChB;AACA,aAAO;AAAA,IACT;AAAA,IACA,SAAS,SAAU,KAAK;AACtB,YAAM,EAAE,UAAU,KAAK,wBAAwB,IAAI,KAAK;AACxD,YAAM,MAAM,OAAO,QAAQ,WAAW,IAAI,MAAM,GAAG,IAAI;AACvD,UAAI,yBAAyB;AAC3B,aAAK,MAAM,iBAAiB;AAC5B,YAAI,KAAK,QAAQ,YAAY,QAAW;AACtC,eAAK,QAAQ;AAAA,YACX;AAAA,YACA,MAAM,KAAK,MAAM,UAAU,SAAS,QAAQ,IAAI;AAAA,UAClD;AAAA,QACF;AAEA,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,eAAe,WAAY;AACzB,aAAO;AAAA,QACL,GAAG,KAAK;AAAA,QACR,SAAS,KAAK,QAAQ;AAAA,MACxB;AAAA,IACF;AAAA,IACA,cAAc,WAAY;AACxB,YAAM,EAAE,SAAS,KAAK,SAAS,IAAI,KAAK;AACxC,aAAO;AAAA,QACL,GAAG,KAAK,cAAc;AAAA,QACtB,OAAO,KAAK,MAAM;AAAA,QAClB,QAAQ,YAAY;AAAA,QACpB,OAAO,KAAK,MAAM,OAAO;AAAA,QACzB,KAAK,MAAM,KAAK,MAAM,UAAU,SAAS,QAAQ,IAAI;AAAA,MACvD;AAAA,IACF;AAAA,IACA,aAAa,WAAY;AACvB,YAAM,EAAE,QAAQ,IAAI,KAAK;AACzB,YAAM,YAAY,MAAM,QAAQ,OAAO;AACvC,aAAO;AAAA,QACL,GAAG,KAAK,aAAa;AAAA,QACrB,QACE,cAAc,OACV,QAAQ,SAAS,KAAK,MAAM,OAAO,SACjC,QAAQ,KAAK,MAAM,OAAO,MAAM,EAAE,OAClC,OACF,KAAK,MAAM,OAAO;AAAA,QACxB,SAAS,KAAK,MAAM;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACF;;;ACv4BA,IAAM,QAAQ,SAAU,MAAM,OAAO,CAAC,GAAG;AACvC,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,OAAO,KAAK,IAAI;AAAA,EACzB;AACA,QAAM,UAAU,QAAQ,KAAK,UAAU,CAAC,IAAI,CAAC;AAC7C,QAAM,SAAS,UAAU,IAAI;AAC7B,QAAM,OAAO,CAAC,WAAW;AACvB,QAAI,OAAO,QAAQ,YAAY,OAAW,SAAQ,KAAK,MAAM;AAAA,SACxD;AACH,cAAQ,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,QAAM,QAAQ,MAAM;AAAA,EAAC;AACrB,QAAM,OAAO,OAAO,MAAM,MAAM,OAAO,MAAM,KAAK;AAClD,MAAI,SAAS,OAAW,OAAM;AAC9B,QAAM,OAAO,OAAO,MAAM,QAAW,MAAM,MAAM,KAAK;AACtD,MAAI,SAAS,OAAW,OAAM;AAC9B,SAAO;AACT;", "names": ["cr", "nl", "encoding", "info", "bom", "buf", "pos"]}