{"version": 3, "sources": ["../../embla-carousel/src/components/utils.ts", "../../embla-carousel/src/components/Alignment.ts", "../../embla-carousel/src/components/EventStore.ts", "../../embla-carousel/src/components/Animations.ts", "../../embla-carousel/src/components/Axis.ts", "../../embla-carousel/src/components/Limit.ts", "../../embla-carousel/src/components/Counter.ts", "../../embla-carousel/src/components/DragHandler.ts", "../../embla-carousel/src/components/DragTracker.ts", "../../embla-carousel/src/components/NodeRects.ts", "../../embla-carousel/src/components/PercentOfView.ts", "../../embla-carousel/src/components/ResizeHandler.ts", "../../embla-carousel/src/components/ScrollBody.ts", "../../embla-carousel/src/components/ScrollBounds.ts", "../../embla-carousel/src/components/ScrollContain.ts", "../../embla-carousel/src/components/ScrollLimit.ts", "../../embla-carousel/src/components/ScrollLooper.ts", "../../embla-carousel/src/components/ScrollProgress.ts", "../../embla-carousel/src/components/ScrollSnaps.ts", "../../embla-carousel/src/components/SlideRegistry.ts", "../../embla-carousel/src/components/ScrollTarget.ts", "../../embla-carousel/src/components/ScrollTo.ts", "../../embla-carousel/src/components/SlideFocus.ts", "../../embla-carousel/src/components/Vector1d.ts", "../../embla-carousel/src/components/Translate.ts", "../../embla-carousel/src/components/SlideLooper.ts", "../../embla-carousel/src/components/SlidesHandler.ts", "../../embla-carousel/src/components/SlidesInView.ts", "../../embla-carousel/src/components/SlideSizes.ts", "../../embla-carousel/src/components/SlidesToScroll.ts", "../../embla-carousel/src/components/Engine.ts", "../../embla-carousel/src/components/EventHandler.ts", "../../embla-carousel/src/components/Options.ts", "../../embla-carousel/src/components/OptionsHandler.ts", "../../embla-carousel/src/components/PluginsHandler.ts", "../../embla-carousel/src/components/EmblaCarousel.ts"], "sourcesContent": ["import { PointerEventType } from './DragTracker'\n\nexport type WindowType = Window & typeof globalThis\n\nexport function isNumber(subject: unknown): subject is number {\n  return typeof subject === 'number'\n}\n\nexport function isString(subject: unknown): subject is string {\n  return typeof subject === 'string'\n}\n\nexport function isBoolean(subject: unknown): subject is boolean {\n  return typeof subject === 'boolean'\n}\n\nexport function isObject(subject: unknown): subject is Record<string, unknown> {\n  return Object.prototype.toString.call(subject) === '[object Object]'\n}\n\nexport function mathAbs(n: number): number {\n  return Math.abs(n)\n}\n\nexport function mathSign(n: number): number {\n  return Math.sign(n)\n}\n\nexport function deltaAbs(valueB: number, valueA: number): number {\n  return mathAbs(valueB - valueA)\n}\n\nexport function factorAbs(valueB: number, valueA: number): number {\n  if (valueB === 0 || valueA === 0) return 0\n  if (mathAbs(valueB) <= mathAbs(valueA)) return 0\n  const diff = deltaAbs(mathAbs(valueB), mathAbs(valueA))\n  return mathAbs(diff / valueB)\n}\n\nexport function roundToTwoDecimals(num: number): number {\n  return Math.round(num * 100) / 100\n}\n\nexport function arrayKeys<Type>(array: Type[]): number[] {\n  return objectKeys(array).map(Number)\n}\n\nexport function arrayLast<Type>(array: Type[]): Type {\n  return array[arrayLastIndex(array)]\n}\n\nexport function arrayLastIndex<Type>(array: Type[]): number {\n  return Math.max(0, array.length - 1)\n}\n\nexport function arrayIsLastIndex<Type>(array: Type[], index: number): boolean {\n  return index === arrayLastIndex(array)\n}\n\nexport function arrayFromNumber(n: number, startAt: number = 0): number[] {\n  return Array.from(Array(n), (_, i) => startAt + i)\n}\n\nexport function objectKeys<Type extends object>(object: Type): string[] {\n  return Object.keys(object)\n}\n\nexport function objectsMergeDeep(\n  objectA: Record<string, unknown>,\n  objectB: Record<string, unknown>\n): Record<string, unknown> {\n  return [objectA, objectB].reduce((mergedObjects, currentObject) => {\n    objectKeys(currentObject).forEach((key) => {\n      const valueA = mergedObjects[key]\n      const valueB = currentObject[key]\n      const areObjects = isObject(valueA) && isObject(valueB)\n\n      mergedObjects[key] = areObjects\n        ? objectsMergeDeep(valueA, valueB)\n        : valueB\n    })\n    return mergedObjects\n  }, {})\n}\n\nexport function isMouseEvent(\n  evt: PointerEventType,\n  ownerWindow: WindowType\n): evt is MouseEvent {\n  return (\n    typeof ownerWindow.MouseEvent !== 'undefined' &&\n    evt instanceof ownerWindow.MouseEvent\n  )\n}\n", "import { isString } from './utils'\n\nexport type AlignmentOptionType =\n  | 'start'\n  | 'center'\n  | 'end'\n  | ((viewSize: number, snapSize: number, index: number) => number)\n\nexport type AlignmentType = {\n  measure: (n: number, index: number) => number\n}\n\nexport function Alignment(\n  align: AlignmentOptionType,\n  viewSize: number\n): AlignmentType {\n  const predefined = { start, center, end }\n\n  function start(): number {\n    return 0\n  }\n\n  function center(n: number): number {\n    return end(n) / 2\n  }\n\n  function end(n: number): number {\n    return viewSize - n\n  }\n\n  function measure(n: number, index: number): number {\n    if (isString(align)) return predefined[align](n)\n    return align(viewSize, n, index)\n  }\n\n  const self: AlignmentType = {\n    measure\n  }\n  return self\n}\n", "type EventNameType = keyof DocumentEventMap | keyof WindowEventMap\ntype EventHandlerType = (evt: any) => void\ntype EventOptionsType = boolean | AddEventListenerOptions | undefined\ntype EventRemoverType = () => void\n\nexport type EventStoreType = {\n  add: (\n    node: EventTarget,\n    type: EventNameType,\n    handler: EventHandlerType,\n    options?: EventOptionsType\n  ) => EventStoreType\n  clear: () => void\n}\n\nexport function EventStore(): EventStoreType {\n  let listeners: EventRemoverType[] = []\n\n  function add(\n    node: EventTarget,\n    type: EventNameType,\n    handler: EventHandlerType,\n    options: EventOptionsType = { passive: true }\n  ): EventStoreType {\n    let removeListener: EventRemoverType\n\n    if ('addEventListener' in node) {\n      node.addEventListener(type, handler, options)\n      removeListener = () => node.removeEventListener(type, handler, options)\n    } else {\n      const legacyMediaQueryList = <MediaQueryList>node\n      legacyMediaQueryList.addListener(handler)\n      removeListener = () => legacyMediaQueryList.removeListener(handler)\n    }\n\n    listeners.push(removeListener)\n    return self\n  }\n\n  function clear(): void {\n    listeners = listeners.filter((remove) => remove())\n  }\n\n  const self: EventStoreType = {\n    add,\n    clear\n  }\n  return self\n}\n", "import { EngineType } from './Engine'\nimport { EventStore } from './EventStore'\nimport { WindowType } from './utils'\n\nexport type AnimationsUpdateType = (engine: EngineType) => void\nexport type AnimationsRenderType = (engine: EngineType, alpha: number) => void\n\nexport type AnimationsType = {\n  init: () => void\n  destroy: () => void\n  start: () => void\n  stop: () => void\n  update: () => void\n  render: (alpha: number) => void\n}\n\nexport function Animations(\n  ownerDocument: Document,\n  ownerWindow: WindowType,\n  update: () => void,\n  render: (alpha: number) => void\n): AnimationsType {\n  const documentVisibleHandler = EventStore()\n  const fixedTimeStep = 1000 / 60\n\n  let lastTimeStamp: number | null = null\n  let accumulatedTime = 0\n  let animationId = 0\n\n  function init(): void {\n    documentVisibleHandler.add(ownerDocument, 'visibilitychange', () => {\n      if (ownerDocument.hidden) reset()\n    })\n  }\n\n  function destroy(): void {\n    stop()\n    documentVisibleHandler.clear()\n  }\n\n  function animate(timeStamp: DOMHighResTimeStamp): void {\n    if (!animationId) return\n    if (!lastTimeStamp) {\n      lastTimeStamp = timeStamp\n      update()\n      update()\n    }\n\n    const timeElapsed = timeStamp - lastTimeStamp\n    lastTimeStamp = timeStamp\n    accumulatedTime += timeElapsed\n\n    while (accumulatedTime >= fixedTimeStep) {\n      update()\n      accumulatedTime -= fixedTimeStep\n    }\n\n    const alpha = accumulatedTime / fixedTimeStep\n    render(alpha)\n\n    if (animationId) {\n      animationId = ownerWindow.requestAnimationFrame(animate)\n    }\n  }\n\n  function start(): void {\n    if (animationId) return\n    animationId = ownerWindow.requestAnimationFrame(animate)\n  }\n\n  function stop(): void {\n    ownerWindow.cancelAnimationFrame(animationId)\n    lastTimeStamp = null\n    accumulatedTime = 0\n    animationId = 0\n  }\n\n  function reset(): void {\n    lastTimeStamp = null\n    accumulatedTime = 0\n  }\n\n  const self: AnimationsType = {\n    init,\n    destroy,\n    start,\n    stop,\n    update,\n    render\n  }\n  return self\n}\n", "import { NodeRectType } from './NodeRects'\n\nexport type AxisOptionType = 'x' | 'y'\nexport type AxisDirectionOptionType = 'ltr' | 'rtl'\ntype AxisEdgeType = 'top' | 'right' | 'bottom' | 'left'\n\nexport type AxisType = {\n  scroll: AxisOptionType\n  cross: AxisOptionType\n  startEdge: AxisEdgeType\n  endEdge: AxisEdgeType\n  measureSize: (nodeRect: NodeRectType) => number\n  direction: (n: number) => number\n}\n\nexport function Axis(\n  axis: AxisOptionType,\n  contentDirection: AxisDirectionOptionType\n): AxisType {\n  const isRightToLeft = contentDirection === 'rtl'\n  const isVertical = axis === 'y'\n  const scroll = isVertical ? 'y' : 'x'\n  const cross = isVertical ? 'x' : 'y'\n  const sign = !isVertical && isRightToLeft ? -1 : 1\n  const startEdge = getStartEdge()\n  const endEdge = getEndEdge()\n\n  function measureSize(nodeRect: NodeRectType): number {\n    const { height, width } = nodeRect\n    return isVertical ? height : width\n  }\n\n  function getStartEdge(): AxisEdgeType {\n    if (isVertical) return 'top'\n    return isRightToLeft ? 'right' : 'left'\n  }\n\n  function getEndEdge(): AxisEdgeType {\n    if (isVertical) return 'bottom'\n    return isRightToLeft ? 'left' : 'right'\n  }\n\n  function direction(n: number): number {\n    return n * sign\n  }\n\n  const self: AxisType = {\n    scroll,\n    cross,\n    startEdge,\n    endEdge,\n    measureSize,\n    direction\n  }\n  return self\n}\n", "import { mathAbs } from './utils'\n\nexport type LimitType = {\n  min: number\n  max: number\n  length: number\n  constrain: (n: number) => number\n  reachedAny: (n: number) => boolean\n  reachedMax: (n: number) => boolean\n  reachedMin: (n: number) => boolean\n  removeOffset: (n: number) => number\n}\n\nexport function Limit(min: number = 0, max: number = 0): LimitType {\n  const length = mathAbs(min - max)\n\n  function reachedMin(n: number): boolean {\n    return n < min\n  }\n\n  function reachedMax(n: number): boolean {\n    return n > max\n  }\n\n  function reachedAny(n: number): boolean {\n    return reachedMin(n) || reachedMax(n)\n  }\n\n  function constrain(n: number): number {\n    if (!reachedAny(n)) return n\n    return reachedMin(n) ? min : max\n  }\n\n  function removeOffset(n: number): number {\n    if (!length) return n\n    return n - length * Math.ceil((n - max) / length)\n  }\n\n  const self: LimitType = {\n    length,\n    max,\n    min,\n    constrain,\n    reachedAny,\n    reachedMax,\n    reachedMin,\n    removeOffset\n  }\n  return self\n}\n", "import { Limit } from './Limit'\nimport { mathAbs } from './utils'\n\nexport type CounterType = {\n  get: () => number\n  set: (n: number) => CounterType\n  add: (n: number) => CounterType\n  clone: () => CounterType\n}\n\nexport function Counter(\n  max: number,\n  start: number,\n  loop: boolean\n): CounterType {\n  const { constrain } = Limit(0, max)\n  const loopEnd = max + 1\n  let counter = withinLimit(start)\n\n  function withinLimit(n: number): number {\n    return !loop ? constrain(n) : mathAbs((loopEnd + n) % loopEnd)\n  }\n\n  function get(): number {\n    return counter\n  }\n\n  function set(n: number): CounterType {\n    counter = withinLimit(n)\n    return self\n  }\n\n  function add(n: number): CounterType {\n    return clone().set(get() + n)\n  }\n\n  function clone(): CounterType {\n    return Counter(max, get(), loop)\n  }\n\n  const self: CounterType = {\n    get,\n    set,\n    add,\n    clone\n  }\n  return self\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\nimport { AnimationsType } from './Animations'\nimport { CounterType } from './Counter'\nimport { DragTrackerType, PointerEventType } from './DragTracker'\nimport { EventHandlerType } from './EventHandler'\nimport { AxisType } from './Axis'\nimport { EventStore } from './EventStore'\nimport { ScrollBodyType } from './ScrollBody'\nimport { ScrollTargetType } from './ScrollTarget'\nimport { ScrollToType } from './ScrollTo'\nimport { Vector1DType } from './Vector1d'\nimport { PercentOfViewType } from './PercentOfView'\nimport { Limit } from './Limit'\nimport {\n  deltaAbs,\n  factorAbs,\n  isBoolean,\n  isMouseEvent,\n  mathAbs,\n  mathSign,\n  WindowType\n} from './utils'\n\ntype DragHandlerCallbackType = (\n  emblaApi: EmblaCarouselType,\n  evt: PointerEventType\n) => boolean | void\n\nexport type DragHandlerOptionType = boolean | DragHandlerCallbackType\n\nexport type DragHandlerType = {\n  init: (emblaApi: EmblaCarouselType) => void\n  destroy: () => void\n  pointerDown: () => boolean\n}\n\nexport function DragHandler(\n  axis: AxisType,\n  rootNode: HTMLElement,\n  ownerDocument: Document,\n  ownerWindow: WindowType,\n  target: Vector1DType,\n  dragTracker: DragTrackerType,\n  location: Vector1DType,\n  animation: AnimationsType,\n  scrollTo: ScrollToType,\n  scrollBody: ScrollBodyType,\n  scrollTarget: ScrollTargetType,\n  index: CounterType,\n  eventHandler: EventHandlerType,\n  percentOfView: PercentOfViewType,\n  dragFree: boolean,\n  dragThreshold: number,\n  skipSnaps: boolean,\n  baseFriction: number,\n  watchDrag: DragHandlerOptionType\n): DragHandlerType {\n  const { cross: crossAxis, direction } = axis\n  const focusNodes = ['INPUT', 'SELECT', 'TEXTAREA']\n  const nonPassiveEvent = { passive: false }\n  const initEvents = EventStore()\n  const dragEvents = EventStore()\n  const goToNextThreshold = Limit(50, 225).constrain(percentOfView.measure(20))\n  const snapForceBoost = { mouse: 300, touch: 400 }\n  const freeForceBoost = { mouse: 500, touch: 600 }\n  const baseSpeed = dragFree ? 43 : 25\n\n  let isMoving = false\n  let startScroll = 0\n  let startCross = 0\n  let pointerIsDown = false\n  let preventScroll = false\n  let preventClick = false\n  let isMouse = false\n\n  function init(emblaApi: EmblaCarouselType): void {\n    if (!watchDrag) return\n\n    function downIfAllowed(evt: PointerEventType): void {\n      if (isBoolean(watchDrag) || watchDrag(emblaApi, evt)) down(evt)\n    }\n\n    const node = rootNode\n    initEvents\n      .add(node, 'dragstart', (evt) => evt.preventDefault(), nonPassiveEvent)\n      .add(node, 'touchmove', () => undefined, nonPassiveEvent)\n      .add(node, 'touchend', () => undefined)\n      .add(node, 'touchstart', downIfAllowed)\n      .add(node, 'mousedown', downIfAllowed)\n      .add(node, 'touchcancel', up)\n      .add(node, 'contextmenu', up)\n      .add(node, 'click', click, true)\n  }\n\n  function destroy(): void {\n    initEvents.clear()\n    dragEvents.clear()\n  }\n\n  function addDragEvents(): void {\n    const node = isMouse ? ownerDocument : rootNode\n    dragEvents\n      .add(node, 'touchmove', move, nonPassiveEvent)\n      .add(node, 'touchend', up)\n      .add(node, 'mousemove', move, nonPassiveEvent)\n      .add(node, 'mouseup', up)\n  }\n\n  function isFocusNode(node: Element): boolean {\n    const nodeName = node.nodeName || ''\n    return focusNodes.includes(nodeName)\n  }\n\n  function forceBoost(): number {\n    const boost = dragFree ? freeForceBoost : snapForceBoost\n    const type = isMouse ? 'mouse' : 'touch'\n    return boost[type]\n  }\n\n  function allowedForce(force: number, targetChanged: boolean): number {\n    const next = index.add(mathSign(force) * -1)\n    const baseForce = scrollTarget.byDistance(force, !dragFree).distance\n\n    if (dragFree || mathAbs(force) < goToNextThreshold) return baseForce\n    if (skipSnaps && targetChanged) return baseForce * 0.5\n\n    return scrollTarget.byIndex(next.get(), 0).distance\n  }\n\n  function down(evt: PointerEventType): void {\n    const isMouseEvt = isMouseEvent(evt, ownerWindow)\n    isMouse = isMouseEvt\n    preventClick = dragFree && isMouseEvt && !evt.buttons && isMoving\n    isMoving = deltaAbs(target.get(), location.get()) >= 2\n\n    if (isMouseEvt && evt.button !== 0) return\n    if (isFocusNode(evt.target as Element)) return\n\n    pointerIsDown = true\n    dragTracker.pointerDown(evt)\n    scrollBody.useFriction(0).useDuration(0)\n    target.set(location)\n    addDragEvents()\n    startScroll = dragTracker.readPoint(evt)\n    startCross = dragTracker.readPoint(evt, crossAxis)\n    eventHandler.emit('pointerDown')\n  }\n\n  function move(evt: PointerEventType): void {\n    const isTouchEvt = !isMouseEvent(evt, ownerWindow)\n    if (isTouchEvt && evt.touches.length >= 2) return up(evt)\n\n    const lastScroll = dragTracker.readPoint(evt)\n    const lastCross = dragTracker.readPoint(evt, crossAxis)\n    const diffScroll = deltaAbs(lastScroll, startScroll)\n    const diffCross = deltaAbs(lastCross, startCross)\n\n    if (!preventScroll && !isMouse) {\n      if (!evt.cancelable) return up(evt)\n      preventScroll = diffScroll > diffCross\n      if (!preventScroll) return up(evt)\n    }\n    const diff = dragTracker.pointerMove(evt)\n    if (diffScroll > dragThreshold) preventClick = true\n\n    scrollBody.useFriction(0.3).useDuration(0.75)\n    animation.start()\n    target.add(direction(diff))\n    evt.preventDefault()\n  }\n\n  function up(evt: PointerEventType): void {\n    const currentLocation = scrollTarget.byDistance(0, false)\n    const targetChanged = currentLocation.index !== index.get()\n    const rawForce = dragTracker.pointerUp(evt) * forceBoost()\n    const force = allowedForce(direction(rawForce), targetChanged)\n    const forceFactor = factorAbs(rawForce, force)\n    const speed = baseSpeed - 10 * forceFactor\n    const friction = baseFriction + forceFactor / 50\n\n    preventScroll = false\n    pointerIsDown = false\n    dragEvents.clear()\n    scrollBody.useDuration(speed).useFriction(friction)\n    scrollTo.distance(force, !dragFree)\n    isMouse = false\n    eventHandler.emit('pointerUp')\n  }\n\n  function click(evt: MouseEvent): void {\n    if (preventClick) {\n      evt.stopPropagation()\n      evt.preventDefault()\n      preventClick = false\n    }\n  }\n\n  function pointerDown(): boolean {\n    return pointerIsDown\n  }\n\n  const self: DragHandlerType = {\n    init,\n    destroy,\n    pointerDown\n  }\n  return self\n}\n", "import { AxisOptionType, AxisType } from './Axis'\nimport { isMouseEvent, mathAbs, WindowType } from './utils'\n\ntype PointerCoordType = keyof Touch | keyof MouseEvent\nexport type PointerEventType = TouchEvent | MouseEvent\n\nexport type DragTrackerType = {\n  pointerDown: (evt: PointerEventType) => number\n  pointerMove: (evt: PointerEventType) => number\n  pointerUp: (evt: PointerEventType) => number\n  readPoint: (evt: PointerEventType, evtAxis?: AxisOptionType) => number\n}\n\nexport function DragTracker(\n  axis: AxisType,\n  ownerWindow: WindowType\n): DragTrackerType {\n  const logInterval = 170\n\n  let startEvent: PointerEventType\n  let lastEvent: PointerEventType\n\n  function readTime(evt: PointerEventType): number {\n    return evt.timeStamp\n  }\n\n  function readPoint(evt: PointerEventType, evtAxis?: AxisOptionType): number {\n    const property = evtAxis || axis.scroll\n    const coord: PointerCoordType = `client${property === 'x' ? 'X' : 'Y'}`\n    return (isMouseEvent(evt, ownerWindow) ? evt : evt.touches[0])[coord]\n  }\n\n  function pointerDown(evt: PointerEventType): number {\n    startEvent = evt\n    lastEvent = evt\n    return readPoint(evt)\n  }\n\n  function pointerMove(evt: PointerEventType): number {\n    const diff = readPoint(evt) - readPoint(lastEvent)\n    const expired = readTime(evt) - readTime(startEvent) > logInterval\n\n    lastEvent = evt\n    if (expired) startEvent = evt\n    return diff\n  }\n\n  function pointerUp(evt: PointerEventType): number {\n    if (!startEvent || !lastEvent) return 0\n    const diffDrag = readPoint(lastEvent) - readPoint(startEvent)\n    const diffTime = readTime(evt) - readTime(startEvent)\n    const expired = readTime(evt) - readTime(lastEvent) > logInterval\n    const force = diffDrag / diffTime\n    const isFlick = diffTime && !expired && mathAbs(force) > 0.1\n\n    return isFlick ? force : 0\n  }\n\n  const self: DragTrackerType = {\n    pointerDown,\n    pointerMove,\n    pointerUp,\n    readPoint\n  }\n  return self\n}\n", "export type NodeRectType = {\n  top: number\n  right: number\n  bottom: number\n  left: number\n  width: number\n  height: number\n}\n\nexport type NodeRectsType = {\n  measure: (node: HTMLElement) => NodeRectType\n}\n\nexport function NodeRects(): NodeRectsType {\n  function measure(node: HTMLElement): NodeRectType {\n    const { offsetTop, offsetLeft, offsetWidth, offsetHeight } = node\n    const offset: NodeRectType = {\n      top: offsetTop,\n      right: offsetLeft + offsetWidth,\n      bottom: offsetTop + offsetHeight,\n      left: offsetLeft,\n      width: offsetWidth,\n      height: offsetHeight\n    }\n\n    return offset\n  }\n\n  const self: NodeRectsType = {\n    measure\n  }\n  return self\n}\n", "export type PercentOfViewType = {\n  measure: (n: number) => number\n}\n\nexport function PercentOfView(viewSize: number): PercentOfViewType {\n  function measure(n: number): number {\n    return viewSize * (n / 100)\n  }\n\n  const self: PercentOfViewType = {\n    measure\n  }\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { EmblaCarouselType } from './EmblaCarousel'\nimport { EventHandlerType } from './EventHandler'\nimport { NodeRectsType } from './NodeRects'\nimport { isBoolean, mathAbs, WindowType } from './utils'\n\ntype ResizeHandlerCallbackType = (\n  emblaApi: EmblaCarouselType,\n  entries: ResizeObserverEntry[]\n) => boolean | void\n\nexport type ResizeHandlerOptionType = boolean | ResizeHandlerCallbackType\n\nexport type ResizeHandlerType = {\n  init: (emblaApi: EmblaCarouselType) => void\n  destroy: () => void\n}\n\nexport function ResizeHandler(\n  container: HTMLElement,\n  eventHandler: EventHandlerType,\n  ownerWindow: WindowType,\n  slides: HTMLElement[],\n  axis: AxisType,\n  watchResize: ResizeHandlerOptionType,\n  nodeRects: NodeRectsType\n): ResizeHandlerType {\n  const observeNodes = [container].concat(slides)\n  let resizeObserver: ResizeObserver\n  let containerSize: number\n  let slideSizes: number[] = []\n  let destroyed = false\n\n  function readSize(node: HTMLElement): number {\n    return axis.measureSize(nodeRects.measure(node))\n  }\n\n  function init(emblaApi: EmblaCarouselType): void {\n    if (!watchResize) return\n\n    containerSize = readSize(container)\n    slideSizes = slides.map(readSize)\n\n    function defaultCallback(entries: ResizeObserverEntry[]): void {\n      for (const entry of entries) {\n        if (destroyed) return\n\n        const isContainer = entry.target === container\n        const slideIndex = slides.indexOf(<HTMLElement>entry.target)\n        const lastSize = isContainer ? containerSize : slideSizes[slideIndex]\n        const newSize = readSize(isContainer ? container : slides[slideIndex])\n        const diffSize = mathAbs(newSize - lastSize)\n\n        if (diffSize >= 0.5) {\n          emblaApi.reInit()\n          eventHandler.emit('resize')\n\n          break\n        }\n      }\n    }\n\n    resizeObserver = new ResizeObserver((entries) => {\n      if (isBoolean(watchResize) || watchResize(emblaApi, entries)) {\n        defaultCallback(entries)\n      }\n    })\n\n    ownerWindow.requestAnimationFrame(() => {\n      observeNodes.forEach((node) => resizeObserver.observe(node))\n    })\n  }\n\n  function destroy(): void {\n    destroyed = true\n    if (resizeObserver) resizeObserver.disconnect()\n  }\n\n  const self: ResizeHandlerType = {\n    init,\n    destroy\n  }\n  return self\n}\n", "import { mathSign, mathAbs } from './utils'\nimport { Vector1DType } from './Vector1d'\n\nexport type ScrollBodyType = {\n  direction: () => number\n  duration: () => number\n  velocity: () => number\n  seek: () => ScrollBodyType\n  settled: () => boolean\n  useBaseFriction: () => ScrollBodyType\n  useBaseDuration: () => ScrollBodyType\n  useFriction: (n: number) => ScrollBodyType\n  useDuration: (n: number) => ScrollBodyType\n}\n\nexport function ScrollBody(\n  location: Vector1DType,\n  offsetLocation: Vector1DType,\n  previousLocation: Vector1DType,\n  target: Vector1DType,\n  baseDuration: number,\n  baseFriction: number\n): ScrollBodyType {\n  let scrollVelocity = 0\n  let scrollDirection = 0\n  let scrollDuration = baseDuration\n  let scrollFriction = baseFriction\n  let rawLocation = location.get()\n  let rawLocationPrevious = 0\n\n  function seek(): ScrollBodyType {\n    const displacement = target.get() - location.get()\n    const isInstant = !scrollDuration\n    let scrollDistance = 0\n\n    if (isInstant) {\n      scrollVelocity = 0\n      previousLocation.set(target)\n      location.set(target)\n\n      scrollDistance = displacement\n    } else {\n      previousLocation.set(location)\n\n      scrollVelocity += displacement / scrollDuration\n      scrollVelocity *= scrollFriction\n      rawLocation += scrollVelocity\n      location.add(scrollVelocity)\n\n      scrollDistance = rawLocation - rawLocationPrevious\n    }\n\n    scrollDirection = mathSign(scrollDistance)\n    rawLocationPrevious = rawLocation\n    return self\n  }\n\n  function settled(): boolean {\n    const diff = target.get() - offsetLocation.get()\n    return mathAbs(diff) < 0.001\n  }\n\n  function duration(): number {\n    return scrollDuration\n  }\n\n  function direction(): number {\n    return scrollDirection\n  }\n\n  function velocity(): number {\n    return scrollVelocity\n  }\n\n  function useBaseDuration(): ScrollBodyType {\n    return useDuration(baseDuration)\n  }\n\n  function useBaseFriction(): ScrollBodyType {\n    return useFriction(baseFriction)\n  }\n\n  function useDuration(n: number): ScrollBodyType {\n    scrollDuration = n\n    return self\n  }\n\n  function useFriction(n: number): ScrollBodyType {\n    scrollFriction = n\n    return self\n  }\n\n  const self: ScrollBodyType = {\n    direction,\n    duration,\n    velocity,\n    seek,\n    settled,\n    useBaseFriction,\n    useBaseDuration,\n    useFriction,\n    useDuration\n  }\n  return self\n}\n", "import { Limit, LimitType } from './Limit'\nimport { ScrollBodyType } from './ScrollBody'\nimport { Vector1DType } from './Vector1d'\nimport { mathAbs } from './utils'\nimport { PercentOfViewType } from './PercentOfView'\n\nexport type ScrollBoundsType = {\n  shouldConstrain: () => boolean\n  constrain: (pointerDown: boolean) => void\n  toggleActive: (active: boolean) => void\n}\n\nexport function ScrollBounds(\n  limit: LimitType,\n  location: Vector1DType,\n  target: Vector1DType,\n  scrollBody: ScrollBodyType,\n  percentOfView: PercentOfViewType\n): ScrollBoundsType {\n  const pullBackThreshold = percentOfView.measure(10)\n  const edgeOffsetTolerance = percentOfView.measure(50)\n  const frictionLimit = Limit(0.1, 0.99)\n  let disabled = false\n\n  function shouldConstrain(): boolean {\n    if (disabled) return false\n    if (!limit.reachedAny(target.get())) return false\n    if (!limit.reachedAny(location.get())) return false\n    return true\n  }\n\n  function constrain(pointerDown: boolean): void {\n    if (!shouldConstrain()) return\n    const edge = limit.reachedMin(location.get()) ? 'min' : 'max'\n    const diffToEdge = mathAbs(limit[edge] - location.get())\n    const diffToTarget = target.get() - location.get()\n    const friction = frictionLimit.constrain(diffToEdge / edgeOffsetTolerance)\n\n    target.subtract(diffToTarget * friction)\n\n    if (!pointerDown && mathAbs(diffToTarget) < pullBackThreshold) {\n      target.set(limit.constrain(target.get()))\n      scrollBody.useDuration(25).useBaseFriction()\n    }\n  }\n\n  function toggleActive(active: boolean): void {\n    disabled = !active\n  }\n\n  const self: ScrollBoundsType = {\n    shouldConstrain,\n    constrain,\n    toggleActive\n  }\n  return self\n}\n", "import { Limit, LimitType } from './Limit'\nimport { arrayIsLastIndex, arrayLast, deltaAbs } from './utils'\n\nexport type ScrollContainOptionType = false | 'trimSnaps' | 'keepSnaps'\n\nexport type ScrollContainType = {\n  snapsContained: number[]\n  scrollContainLimit: LimitType\n}\n\nexport function ScrollContain(\n  viewSize: number,\n  contentSize: number,\n  snapsAligned: number[],\n  containScroll: ScrollContainOptionType,\n  pixelTolerance: number\n): ScrollContainType {\n  const scrollBounds = Limit(-contentSize + viewSize, 0)\n  const snapsBounded = measureBounded()\n  const scrollContainLimit = findScrollContainLimit()\n  const snapsContained = measureContained()\n\n  function usePixelTolerance(bound: number, snap: number): boolean {\n    return deltaAbs(bound, snap) <= 1\n  }\n\n  function findScrollContainLimit(): LimitType {\n    const startSnap = snapsBounded[0]\n    const endSnap = arrayLast(snapsBounded)\n    const min = snapsBounded.lastIndexOf(startSnap)\n    const max = snapsBounded.indexOf(endSnap) + 1\n    return Limit(min, max)\n  }\n\n  function measureBounded(): number[] {\n    return snapsAligned\n      .map((snapAligned, index) => {\n        const { min, max } = scrollBounds\n        const snap = scrollBounds.constrain(snapAligned)\n        const isFirst = !index\n        const isLast = arrayIsLastIndex(snapsAligned, index)\n        if (isFirst) return max\n        if (isLast) return min\n        if (usePixelTolerance(min, snap)) return min\n        if (usePixelTolerance(max, snap)) return max\n        return snap\n      })\n      .map((scrollBound) => parseFloat(scrollBound.toFixed(3)))\n  }\n\n  function measureContained(): number[] {\n    if (contentSize <= viewSize + pixelTolerance) return [scrollBounds.max]\n    if (containScroll === 'keepSnaps') return snapsBounded\n    const { min, max } = scrollContainLimit\n    return snapsBounded.slice(min, max)\n  }\n\n  const self: ScrollContainType = {\n    snapsContained,\n    scrollContainLimit\n  }\n  return self\n}\n", "import { Limit, LimitType } from './Limit'\nimport { arrayLast } from './utils'\n\nexport type ScrollLimitType = {\n  limit: LimitType\n}\n\nexport function ScrollLimit(\n  contentSize: number,\n  scrollSnaps: number[],\n  loop: boolean\n): ScrollLimitType {\n  const max = scrollSnaps[0]\n  const min = loop ? max - contentSize : arrayLast(scrollSnaps)\n  const limit = Limit(min, max)\n\n  const self: ScrollLimitType = {\n    limit\n  }\n  return self\n}\n", "import { Limit, LimitType } from './Limit'\nimport { Vector1DType } from './Vector1d'\n\nexport type ScrollLooperType = {\n  loop: (direction: number) => void\n}\n\nexport function ScrollLooper(\n  contentSize: number,\n  limit: LimitType,\n  location: Vector1DType,\n  vectors: Vector1DType[]\n): ScrollLooperType {\n  const jointSafety = 0.1\n  const min = limit.min + jointSafety\n  const max = limit.max + jointSafety\n  const { reachedMin, reachedMax } = Limit(min, max)\n\n  function shouldLoop(direction: number): boolean {\n    if (direction === 1) return reachedMax(location.get())\n    if (direction === -1) return reachedMin(location.get())\n    return false\n  }\n\n  function loop(direction: number): void {\n    if (!shouldLoop(direction)) return\n\n    const loopDistance = contentSize * (direction * -1)\n    vectors.forEach((v) => v.add(loopDistance))\n  }\n\n  const self: ScrollLooperType = {\n    loop\n  }\n  return self\n}\n", "import { LimitType } from './Limit'\n\nexport type ScrollProgressType = {\n  get: (n: number) => number\n}\n\nexport function ScrollProgress(limit: LimitType): ScrollProgressType {\n  const { max, length } = limit\n\n  function get(n: number): number {\n    const currentLocation = n - max\n    return length ? currentLocation / -length : 0\n  }\n\n  const self: ScrollProgressType = {\n    get\n  }\n  return self\n}\n", "import { AlignmentType } from './Alignment'\nimport { AxisType } from './Axis'\nimport { NodeRectType } from './NodeRects'\nimport { SlidesToScrollType } from './SlidesToScroll'\nimport { arrayLast, mathAbs } from './utils'\n\nexport type ScrollSnapsType = {\n  snaps: number[]\n  snapsAligned: number[]\n}\n\nexport function ScrollSnaps(\n  axis: AxisType,\n  alignment: AlignmentType,\n  containerRect: NodeRectType,\n  slideRects: NodeRectType[],\n  slidesToScroll: SlidesToScrollType\n): ScrollSnapsType {\n  const { startEdge, endEdge } = axis\n  const { groupSlides } = slidesToScroll\n  const alignments = measureSizes().map(alignment.measure)\n  const snaps = measureUnaligned()\n  const snapsAligned = measureAligned()\n\n  function measureSizes(): number[] {\n    return groupSlides(slideRects)\n      .map((rects) => arrayLast(rects)[endEdge] - rects[0][startEdge])\n      .map(mathAbs)\n  }\n\n  function measureUnaligned(): number[] {\n    return slideRects\n      .map((rect) => containerRect[startEdge] - rect[startEdge])\n      .map((snap) => -mathAbs(snap))\n  }\n\n  function measureAligned(): number[] {\n    return groupSlides(snaps)\n      .map((g) => g[0])\n      .map((snap, index) => snap + alignments[index])\n  }\n\n  const self: ScrollSnapsType = {\n    snaps,\n    snapsAligned\n  }\n  return self\n}\n", "import { LimitType } from './Limit'\nimport { ScrollContainOptionType } from './ScrollContain'\nimport { SlidesToScrollType } from './SlidesToScroll'\nimport {\n  arrayFromNumber,\n  arrayIsLastIndex,\n  arrayLast,\n  arrayLastIndex\n} from './utils'\n\nexport type SlideRegistryType = {\n  slideRegistry: number[][]\n}\n\nexport function SlideRegistry(\n  containSnaps: boolean,\n  containScroll: ScrollContainOptionType,\n  scrollSnaps: number[],\n  scrollContainLimit: LimitType,\n  slidesToScroll: SlidesToScrollType,\n  slideIndexes: number[]\n): SlideRegistryType {\n  const { groupSlides } = slidesToScroll\n  const { min, max } = scrollContainLimit\n  const slideRegistry = createSlideRegistry()\n\n  function createSlideRegistry(): number[][] {\n    const groupedSlideIndexes = groupSlides(slideIndexes)\n    const doNotContain = !containSnaps || containScroll === 'keepSnaps'\n\n    if (scrollSnaps.length === 1) return [slideIndexes]\n    if (doNotContain) return groupedSlideIndexes\n\n    return groupedSlideIndexes.slice(min, max).map((group, index, groups) => {\n      const isFirst = !index\n      const isLast = arrayIsLastIndex(groups, index)\n\n      if (isFirst) {\n        const range = arrayLast(groups[0]) + 1\n        return arrayFromNumber(range)\n      }\n      if (isLast) {\n        const range = arrayLastIndex(slideIndexes) - arrayLast(groups)[0] + 1\n        return arrayFromNumber(range, arrayLast(groups)[0])\n      }\n      return group\n    })\n  }\n\n  const self: SlideRegistryType = {\n    slideRegistry\n  }\n  return self\n}\n", "import { LimitType } from './Limit'\nimport { Vector1DType } from './Vector1d'\nimport { arrayLast, mathAbs, mathSign } from './utils'\n\nexport type TargetType = {\n  distance: number\n  index: number\n}\n\nexport type ScrollTargetType = {\n  byIndex: (target: number, direction: number) => TargetType\n  byDistance: (force: number, snap: boolean) => TargetType\n  shortcut: (target: number, direction: number) => number\n}\n\nexport function ScrollTarget(\n  loop: boolean,\n  scrollSnaps: number[],\n  contentSize: number,\n  limit: LimitType,\n  targetVector: Vector1DType\n): ScrollTargetType {\n  const { reachedAny, removeOffset, constrain } = limit\n\n  function minDistance(distances: number[]): number {\n    return distances.concat().sort((a, b) => mathAbs(a) - mathAbs(b))[0]\n  }\n\n  function findTargetSnap(target: number): TargetType {\n    const distance = loop ? removeOffset(target) : constrain(target)\n    const ascDiffsToSnaps = scrollSnaps\n      .map((snap, index) => ({ diff: shortcut(snap - distance, 0), index }))\n      .sort((d1, d2) => mathAbs(d1.diff) - mathAbs(d2.diff))\n\n    const { index } = ascDiffsToSnaps[0]\n    return { index, distance }\n  }\n\n  function shortcut(target: number, direction: number): number {\n    const targets = [target, target + contentSize, target - contentSize]\n\n    if (!loop) return target\n    if (!direction) return minDistance(targets)\n\n    const matchingTargets = targets.filter((t) => mathSign(t) === direction)\n    if (matchingTargets.length) return minDistance(matchingTargets)\n    return arrayLast(targets) - contentSize\n  }\n\n  function byIndex(index: number, direction: number): TargetType {\n    const diffToSnap = scrollSnaps[index] - targetVector.get()\n    const distance = shortcut(diffToSnap, direction)\n    return { index, distance }\n  }\n\n  function byDistance(distance: number, snap: boolean): TargetType {\n    const target = targetVector.get() + distance\n    const { index, distance: targetSnapDistance } = findTargetSnap(target)\n    const reachedBound = !loop && reachedAny(target)\n\n    if (!snap || reachedBound) return { index, distance }\n\n    const diffToSnap = scrollSnaps[index] - targetSnapDistance\n    const snapDistance = distance + shortcut(diffToSnap, 0)\n\n    return { index, distance: snapDistance }\n  }\n\n  const self: ScrollTargetType = {\n    byDistance,\n    byIndex,\n    shortcut\n  }\n  return self\n}\n", "import { AnimationsType } from './Animations'\nimport { CounterType } from './Counter'\nimport { EventHandlerType } from './EventHandler'\nimport { ScrollBodyType } from './ScrollBody'\nimport { ScrollTargetType, TargetType } from './ScrollTarget'\nimport { Vector1DType } from './Vector1d'\n\nexport type ScrollToType = {\n  distance: (n: number, snap: boolean) => void\n  index: (n: number, direction: number) => void\n}\n\nexport function ScrollTo(\n  animation: AnimationsType,\n  indexCurrent: CounterType,\n  indexPrevious: CounterType,\n  scrollBody: ScrollBodyType,\n  scrollTarget: ScrollTargetType,\n  targetVector: Vector1DType,\n  eventHandler: EventHandlerType\n): ScrollToType {\n  function scrollTo(target: TargetType): void {\n    const distanceDiff = target.distance\n    const indexDiff = target.index !== indexCurrent.get()\n\n    targetVector.add(distanceDiff)\n\n    if (distanceDiff) {\n      if (scrollBody.duration()) {\n        animation.start()\n      } else {\n        animation.update()\n        animation.render(1)\n        animation.update()\n      }\n    }\n\n    if (indexDiff) {\n      indexPrevious.set(indexCurrent.get())\n      indexCurrent.set(target.index)\n      eventHandler.emit('select')\n    }\n  }\n\n  function distance(n: number, snap: boolean): void {\n    const target = scrollTarget.byDistance(n, snap)\n    scrollTo(target)\n  }\n\n  function index(n: number, direction: number): void {\n    const targetIndex = indexCurrent.clone().set(n)\n    const target = scrollTarget.byIndex(targetIndex.get(), direction)\n    scrollTo(target)\n  }\n\n  const self: ScrollToType = {\n    distance,\n    index\n  }\n  return self\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\nimport { EventHandlerType } from './EventHandler'\nimport { EventStoreType } from './EventStore'\nimport { ScrollBodyType } from './ScrollBody'\nimport { ScrollToType } from './ScrollTo'\nimport { SlideRegistryType } from './SlideRegistry'\nimport { isBoolean, isNumber } from './utils'\n\ntype FocusHandlerCallbackType = (\n  emblaApi: EmblaCarouselType,\n  evt: FocusEvent\n) => boolean | void\n\nexport type FocusHandlerOptionType = boolean | FocusHandlerCallbackType\n\nexport type SlideFocusType = {\n  init: (emblaApi: EmblaCarouselType) => void\n}\n\nexport function SlideFocus(\n  root: HTMLElement,\n  slides: HTMLElement[],\n  slideRegistry: SlideRegistryType['slideRegistry'],\n  scrollTo: ScrollToType,\n  scrollBody: ScrollBodyType,\n  eventStore: EventStoreType,\n  eventHandler: EventHandlerType,\n  watchFocus: FocusHandlerOptionType\n): SlideFocusType {\n  const focusListenerOptions = { passive: true, capture: true }\n  let lastTabPressTime = 0\n\n  function init(emblaApi: EmblaCarouselType): void {\n    if (!watchFocus) return\n\n    function defaultCallback(index: number): void {\n      const nowTime = new Date().getTime()\n      const diffTime = nowTime - lastTabPressTime\n\n      if (diffTime > 10) return\n\n      eventHandler.emit('slideFocusStart')\n      root.scrollLeft = 0\n\n      const group = slideRegistry.findIndex((group) => group.includes(index))\n\n      if (!isNumber(group)) return\n\n      scrollBody.useDuration(0)\n      scrollTo.index(group, 0)\n\n      eventHandler.emit('slideFocus')\n    }\n\n    eventStore.add(document, 'keydown', registerTabPress, false)\n\n    slides.forEach((slide, slideIndex) => {\n      eventStore.add(\n        slide,\n        'focus',\n        (evt: FocusEvent) => {\n          if (isBoolean(watchFocus) || watchFocus(emblaApi, evt)) {\n            defaultCallback(slideIndex)\n          }\n        },\n        focusListenerOptions\n      )\n    })\n  }\n\n  function registerTabPress(event: KeyboardEvent): void {\n    if (event.code === 'Tab') lastTabPressTime = new Date().getTime()\n  }\n\n  const self: SlideFocusType = {\n    init\n  }\n  return self\n}\n", "import { isNumber } from './utils'\n\nexport type Vector1DType = {\n  get: () => number\n  set: (n: Vector1DType | number) => void\n  add: (n: Vector1DType | number) => void\n  subtract: (n: Vector1DType | number) => void\n}\n\nexport function Vector1D(initialValue: number): Vector1DType {\n  let value = initialValue\n\n  function get(): number {\n    return value\n  }\n\n  function set(n: Vector1DType | number): void {\n    value = normalizeInput(n)\n  }\n\n  function add(n: Vector1DType | number): void {\n    value += normalizeInput(n)\n  }\n\n  function subtract(n: Vector1DType | number): void {\n    value -= normalizeInput(n)\n  }\n\n  function normalizeInput(n: Vector1DType | number): number {\n    return isNumber(n) ? n : n.get()\n  }\n\n  const self: Vector1DType = {\n    get,\n    set,\n    add,\n    subtract\n  }\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { roundToTwoDecimals } from './utils'\n\nexport type TranslateType = {\n  clear: () => void\n  to: (target: number) => void\n  toggleActive: (active: boolean) => void\n}\n\nexport function Translate(\n  axis: AxisType,\n  container: HTMLElement\n): TranslateType {\n  const translate = axis.scroll === 'x' ? x : y\n  const containerStyle = container.style\n  let previousTarget: number | null = null\n  let disabled = false\n\n  function x(n: number): string {\n    return `translate3d(${n}px,0px,0px)`\n  }\n\n  function y(n: number): string {\n    return `translate3d(0px,${n}px,0px)`\n  }\n\n  function to(target: number): void {\n    if (disabled) return\n\n    const newTarget = roundToTwoDecimals(axis.direction(target))\n    if (newTarget === previousTarget) return\n\n    containerStyle.transform = translate(newTarget)\n    previousTarget = newTarget\n  }\n\n  function toggleActive(active: boolean): void {\n    disabled = !active\n  }\n\n  function clear(): void {\n    if (disabled) return\n    containerStyle.transform = ''\n    if (!container.getAttribute('style')) container.removeAttribute('style')\n  }\n\n  const self: TranslateType = {\n    clear,\n    to,\n    toggleActive\n  }\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { arrayKeys } from './utils'\nimport { Vector1D, Vector1DType } from './Vector1d'\nimport { Translate, TranslateType } from './Translate'\n\ntype SlideBoundType = {\n  start: number\n  end: number\n}\n\ntype LoopPointType = {\n  loopPoint: number\n  index: number\n  translate: TranslateType\n  slideLocation: Vector1DType\n  target: () => number\n}\n\nexport type SlideLooperType = {\n  canLoop: () => boolean\n  clear: () => void\n  loop: () => void\n  loopPoints: LoopPointType[]\n}\n\nexport function SlideLooper(\n  axis: AxisType,\n  viewSize: number,\n  contentSize: number,\n  slideSizes: number[],\n  slideSizesWithGaps: number[],\n  snaps: number[],\n  scrollSnaps: number[],\n  location: Vector1DType,\n  slides: HTMLElement[]\n): SlideLooperType {\n  const roundingSafety = 0.5\n  const ascItems = arrayKeys(slideSizesWithGaps)\n  const descItems = arrayKeys(slideSizesWithGaps).reverse()\n  const loopPoints = startPoints().concat(endPoints())\n\n  function removeSlideSizes(indexes: number[], from: number): number {\n    return indexes.reduce((a: number, i) => {\n      return a - slideSizesWithGaps[i]\n    }, from)\n  }\n\n  function slidesInGap(indexes: number[], gap: number): number[] {\n    return indexes.reduce((a: number[], i) => {\n      const remainingGap = removeSlideSizes(a, gap)\n      return remainingGap > 0 ? a.concat([i]) : a\n    }, [])\n  }\n\n  function findSlideBounds(offset: number): SlideBoundType[] {\n    return snaps.map((snap, index) => ({\n      start: snap - slideSizes[index] + roundingSafety + offset,\n      end: snap + viewSize - roundingSafety + offset\n    }))\n  }\n\n  function findLoopPoints(\n    indexes: number[],\n    offset: number,\n    isEndEdge: boolean\n  ): LoopPointType[] {\n    const slideBounds = findSlideBounds(offset)\n\n    return indexes.map((index) => {\n      const initial = isEndEdge ? 0 : -contentSize\n      const altered = isEndEdge ? contentSize : 0\n      const boundEdge = isEndEdge ? 'end' : 'start'\n      const loopPoint = slideBounds[index][boundEdge]\n\n      return {\n        index,\n        loopPoint,\n        slideLocation: Vector1D(-1),\n        translate: Translate(axis, slides[index]),\n        target: () => (location.get() > loopPoint ? initial : altered)\n      }\n    })\n  }\n\n  function startPoints(): LoopPointType[] {\n    const gap = scrollSnaps[0]\n    const indexes = slidesInGap(descItems, gap)\n    return findLoopPoints(indexes, contentSize, false)\n  }\n\n  function endPoints(): LoopPointType[] {\n    const gap = viewSize - scrollSnaps[0] - 1\n    const indexes = slidesInGap(ascItems, gap)\n    return findLoopPoints(indexes, -contentSize, true)\n  }\n\n  function canLoop(): boolean {\n    return loopPoints.every(({ index }) => {\n      const otherIndexes = ascItems.filter((i) => i !== index)\n      return removeSlideSizes(otherIndexes, viewSize) <= 0.1\n    })\n  }\n\n  function loop(): void {\n    loopPoints.forEach((loopPoint) => {\n      const { target, translate, slideLocation } = loopPoint\n      const shiftLocation = target()\n      if (shiftLocation === slideLocation.get()) return\n      translate.to(shiftLocation)\n      slideLocation.set(shiftLocation)\n    })\n  }\n\n  function clear(): void {\n    loopPoints.forEach((loopPoint) => loopPoint.translate.clear())\n  }\n\n  const self: SlideLooperType = {\n    canLoop,\n    clear,\n    loop,\n    loopPoints\n  }\n  return self\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\nimport { EventHandlerType } from './EventHandler'\nimport { isBoolean } from './utils'\n\ntype SlidesHandlerCallbackType = (\n  emblaApi: EmblaCarouselType,\n  mutations: MutationRecord[]\n) => boolean | void\n\nexport type SlidesHandlerOptionType = boolean | SlidesHandlerCallbackType\n\nexport type SlidesHandlerType = {\n  init: (emblaApi: EmblaCarouselType) => void\n  destroy: () => void\n}\n\nexport function SlidesHandler(\n  container: HTMLElement,\n  eventHandler: EventHandlerType,\n  watchSlides: SlidesHandlerOptionType\n): SlidesHandlerType {\n  let mutationObserver: MutationObserver\n  let destroyed = false\n\n  function init(emblaApi: EmblaCarouselType): void {\n    if (!watchSlides) return\n\n    function defaultCallback(mutations: MutationRecord[]): void {\n      for (const mutation of mutations) {\n        if (mutation.type === 'childList') {\n          emblaApi.reInit()\n          eventHandler.emit('slidesChanged')\n          break\n        }\n      }\n    }\n\n    mutationObserver = new MutationObserver((mutations) => {\n      if (destroyed) return\n      if (isBoolean(watchSlides) || watchSlides(emblaApi, mutations)) {\n        defaultCallback(mutations)\n      }\n    })\n\n    mutationObserver.observe(container, { childList: true })\n  }\n\n  function destroy(): void {\n    if (mutationObserver) mutationObserver.disconnect()\n    destroyed = true\n  }\n\n  const self: SlidesHandlerType = {\n    init,\n    destroy\n  }\n  return self\n}\n", "import { EventHandlerType } from './EventHandler'\nimport { objectKeys } from './utils'\n\ntype IntersectionEntryMapType = {\n  [key: number]: IntersectionObserverEntry\n}\n\nexport type SlidesInViewOptionsType = IntersectionObserverInit['threshold']\n\nexport type SlidesInViewType = {\n  init: () => void\n  destroy: () => void\n  get: (inView?: boolean) => number[]\n}\n\nexport function SlidesInView(\n  container: HTMLElement,\n  slides: HTMLElement[],\n  eventHandler: EventHandlerType,\n  threshold: SlidesInViewOptionsType\n): SlidesInViewType {\n  const intersectionEntryMap: IntersectionEntryMapType = {}\n  let inViewCache: number[] | null = null\n  let notInViewCache: number[] | null = null\n  let intersectionObserver: IntersectionObserver\n  let destroyed = false\n\n  function init(): void {\n    intersectionObserver = new IntersectionObserver(\n      (entries) => {\n        if (destroyed) return\n\n        entries.forEach((entry) => {\n          const index = slides.indexOf(<HTMLElement>entry.target)\n          intersectionEntryMap[index] = entry\n        })\n\n        inViewCache = null\n        notInViewCache = null\n        eventHandler.emit('slidesInView')\n      },\n      {\n        root: container.parentElement,\n        threshold\n      }\n    )\n\n    slides.forEach((slide) => intersectionObserver.observe(slide))\n  }\n\n  function destroy(): void {\n    if (intersectionObserver) intersectionObserver.disconnect()\n    destroyed = true\n  }\n\n  function createInViewList(inView: boolean): number[] {\n    return objectKeys(intersectionEntryMap).reduce(\n      (list: number[], slideIndex) => {\n        const index = parseInt(slideIndex)\n        const { isIntersecting } = intersectionEntryMap[index]\n        const inViewMatch = inView && isIntersecting\n        const notInViewMatch = !inView && !isIntersecting\n\n        if (inViewMatch || notInViewMatch) list.push(index)\n        return list\n      },\n      []\n    )\n  }\n\n  function get(inView: boolean = true): number[] {\n    if (inView && inViewCache) return inViewCache\n    if (!inView && notInViewCache) return notInViewCache\n\n    const slideIndexes = createInViewList(inView)\n\n    if (inView) inViewCache = slideIndexes\n    if (!inView) notInViewCache = slideIndexes\n\n    return slideIndexes\n  }\n\n  const self: SlidesInViewType = {\n    init,\n    destroy,\n    get\n  }\n\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { NodeRectType } from './NodeRects'\nimport { arrayIsLastIndex, arrayLast, mathAbs, WindowType } from './utils'\n\nexport type SlideSizesType = {\n  slideSizes: number[]\n  slideSizesWithGaps: number[]\n  startGap: number\n  endGap: number\n}\n\nexport function SlideSizes(\n  axis: AxisType,\n  containerRect: NodeRectType,\n  slideRects: NodeRectType[],\n  slides: HTMLElement[],\n  readEdgeGap: boolean,\n  ownerWindow: WindowType\n): SlideSizesType {\n  const { measureSize, startEdge, endEdge } = axis\n  const withEdgeGap = slideRects[0] && readEdgeGap\n  const startGap = measureStartGap()\n  const endGap = measureEndGap()\n  const slideSizes = slideRects.map(measureSize)\n  const slideSizesWithGaps = measureWithGaps()\n\n  function measureStartGap(): number {\n    if (!withEdgeGap) return 0\n    const slideRect = slideRects[0]\n    return mathAbs(containerRect[startEdge] - slideRect[startEdge])\n  }\n\n  function measureEndGap(): number {\n    if (!withEdgeGap) return 0\n    const style = ownerWindow.getComputedStyle(arrayLast(slides))\n    return parseFloat(style.getPropertyValue(`margin-${endEdge}`))\n  }\n\n  function measureWithGaps(): number[] {\n    return slideRects\n      .map((rect, index, rects) => {\n        const isFirst = !index\n        const isLast = arrayIsLastIndex(rects, index)\n        if (isFirst) return slideSizes[index] + startGap\n        if (isLast) return slideSizes[index] + endGap\n        return rects[index + 1][startEdge] - rect[startEdge]\n      })\n      .map(mathAbs)\n  }\n\n  const self: SlideSizesType = {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  }\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { NodeRectType } from './NodeRects'\nimport {\n  arrayKeys,\n  arrayLast,\n  arrayLastIndex,\n  isNumber,\n  mathAbs\n} from './utils'\n\nexport type SlidesToScrollOptionType = 'auto' | number\n\nexport type SlidesToScrollType = {\n  groupSlides: <Type>(array: Type[]) => Type[][]\n}\n\nexport function SlidesToScroll(\n  axis: AxisType,\n  viewSize: number,\n  slidesToScroll: SlidesToScrollOptionType,\n  loop: boolean,\n  containerRect: NodeRectType,\n  slideRects: NodeRectType[],\n  startGap: number,\n  endGap: number,\n  pixelTolerance: number\n): SlidesToScrollType {\n  const { startEdge, endEdge, direction } = axis\n  const groupByNumber = isNumber(slidesToScroll)\n\n  function byNumber<Type>(array: Type[], groupSize: number): Type[][] {\n    return arrayKeys(array)\n      .filter((i) => i % groupSize === 0)\n      .map((i) => array.slice(i, i + groupSize))\n  }\n\n  function bySize<Type>(array: Type[]): Type[][] {\n    if (!array.length) return []\n\n    return arrayKeys(array)\n      .reduce((groups: number[], rectB, index) => {\n        const rectA = arrayLast(groups) || 0\n        const isFirst = rectA === 0\n        const isLast = rectB === arrayLastIndex(array)\n\n        const edgeA = containerRect[startEdge] - slideRects[rectA][startEdge]\n        const edgeB = containerRect[startEdge] - slideRects[rectB][endEdge]\n        const gapA = !loop && isFirst ? direction(startGap) : 0\n        const gapB = !loop && isLast ? direction(endGap) : 0\n        const chunkSize = mathAbs(edgeB - gapB - (edgeA + gapA))\n\n        if (index && chunkSize > viewSize + pixelTolerance) groups.push(rectB)\n        if (isLast) groups.push(array.length)\n        return groups\n      }, [])\n      .map((currentSize, index, groups) => {\n        const previousSize = Math.max(groups[index - 1] || 0)\n        return array.slice(previousSize, currentSize)\n      })\n  }\n\n  function groupSlides<Type>(array: Type[]): Type[][] {\n    return groupByNumber ? byNumber(array, slidesToScroll) : bySize(array)\n  }\n\n  const self: SlidesToScrollType = {\n    groupSlides\n  }\n  return self\n}\n", "import { Alignment } from './Alignment'\nimport {\n  Animations,\n  AnimationsType,\n  AnimationsUpdateType,\n  AnimationsRenderType\n} from './Animations'\nimport { Axis, AxisType } from './Axis'\nimport { Counter, CounterType } from './Counter'\nimport { DragHandler, DragHandlerType } from './DragHandler'\nimport { DragTracker } from './DragTracker'\nimport { EventHandlerType } from './EventHandler'\nimport { EventStore, EventStoreType } from './EventStore'\nimport { LimitType } from './Limit'\nimport { NodeRectType, NodeRects } from './NodeRects'\nimport { OptionsType } from './Options'\nimport { PercentOfView, PercentOfViewType } from './PercentOfView'\nimport { ResizeHandler, ResizeHandlerType } from './ResizeHandler'\nimport { ScrollBody, ScrollBodyType } from './ScrollBody'\nimport { ScrollBounds, ScrollBoundsType } from './ScrollBounds'\nimport { ScrollContain } from './ScrollContain'\nimport { ScrollLimit } from './ScrollLimit'\nimport { Sc<PERSON>Looper, ScrollLooperType } from './ScrollLooper'\nimport { ScrollProgress, ScrollProgressType } from './ScrollProgress'\nimport { ScrollSnaps } from './ScrollSnaps'\nimport { SlideRegistry, SlideRegistryType } from './SlideRegistry'\nimport { ScrollTarget, ScrollTargetType } from './ScrollTarget'\nimport { ScrollTo, ScrollToType } from './ScrollTo'\nimport { SlideFocus, SlideFocusType } from './SlideFocus'\nimport { SlideLooper, SlideLooperType } from './SlideLooper'\nimport { SlidesHandler, SlidesHandlerType } from './SlidesHandler'\nimport { SlidesInView, SlidesInViewType } from './SlidesInView'\nimport { SlideSizes } from './SlideSizes'\nimport { SlidesToScroll, SlidesToScrollType } from './SlidesToScroll'\nimport { Translate, TranslateType } from './Translate'\nimport { arrayKeys, arrayLast, arrayLastIndex, WindowType } from './utils'\nimport { Vector1D, Vector1DType } from './Vector1d'\n\nexport type EngineType = {\n  ownerDocument: Document\n  ownerWindow: WindowType\n  eventHandler: EventHandlerType\n  axis: AxisType\n  animation: AnimationsType\n  scrollBounds: ScrollBoundsType\n  scrollLooper: ScrollLooperType\n  scrollProgress: ScrollProgressType\n  index: CounterType\n  indexPrevious: CounterType\n  limit: LimitType\n  location: Vector1DType\n  offsetLocation: Vector1DType\n  previousLocation: Vector1DType\n  options: OptionsType\n  percentOfView: PercentOfViewType\n  scrollBody: ScrollBodyType\n  dragHandler: DragHandlerType\n  eventStore: EventStoreType\n  slideLooper: SlideLooperType\n  slidesInView: SlidesInViewType\n  slidesToScroll: SlidesToScrollType\n  target: Vector1DType\n  translate: TranslateType\n  resizeHandler: ResizeHandlerType\n  slidesHandler: SlidesHandlerType\n  scrollTo: ScrollToType\n  scrollTarget: ScrollTargetType\n  scrollSnapList: number[]\n  scrollSnaps: number[]\n  slideIndexes: number[]\n  slideFocus: SlideFocusType\n  slideRegistry: SlideRegistryType['slideRegistry']\n  containerRect: NodeRectType\n  slideRects: NodeRectType[]\n}\n\nexport function Engine(\n  root: HTMLElement,\n  container: HTMLElement,\n  slides: HTMLElement[],\n  ownerDocument: Document,\n  ownerWindow: WindowType,\n  options: OptionsType,\n  eventHandler: EventHandlerType\n): EngineType {\n  // Options\n  const {\n    align,\n    axis: scrollAxis,\n    direction,\n    startIndex,\n    loop,\n    duration,\n    dragFree,\n    dragThreshold,\n    inViewThreshold,\n    slidesToScroll: groupSlides,\n    skipSnaps,\n    containScroll,\n    watchResize,\n    watchSlides,\n    watchDrag,\n    watchFocus\n  } = options\n\n  // Measurements\n  const pixelTolerance = 2\n  const nodeRects = NodeRects()\n  const containerRect = nodeRects.measure(container)\n  const slideRects = slides.map(nodeRects.measure)\n  const axis = Axis(scrollAxis, direction)\n  const viewSize = axis.measureSize(containerRect)\n  const percentOfView = PercentOfView(viewSize)\n  const alignment = Alignment(align, viewSize)\n  const containSnaps = !loop && !!containScroll\n  const readEdgeGap = loop || !!containScroll\n  const { slideSizes, slideSizesWithGaps, startGap, endGap } = SlideSizes(\n    axis,\n    containerRect,\n    slideRects,\n    slides,\n    readEdgeGap,\n    ownerWindow\n  )\n  const slidesToScroll = SlidesToScroll(\n    axis,\n    viewSize,\n    groupSlides,\n    loop,\n    containerRect,\n    slideRects,\n    startGap,\n    endGap,\n    pixelTolerance\n  )\n  const { snaps, snapsAligned } = ScrollSnaps(\n    axis,\n    alignment,\n    containerRect,\n    slideRects,\n    slidesToScroll\n  )\n  const contentSize = -arrayLast(snaps) + arrayLast(slideSizesWithGaps)\n  const { snapsContained, scrollContainLimit } = ScrollContain(\n    viewSize,\n    contentSize,\n    snapsAligned,\n    containScroll,\n    pixelTolerance\n  )\n  const scrollSnaps = containSnaps ? snapsContained : snapsAligned\n  const { limit } = ScrollLimit(contentSize, scrollSnaps, loop)\n\n  // Indexes\n  const index = Counter(arrayLastIndex(scrollSnaps), startIndex, loop)\n  const indexPrevious = index.clone()\n  const slideIndexes = arrayKeys(slides)\n\n  // Animation\n  const update: AnimationsUpdateType = ({\n    dragHandler,\n    scrollBody,\n    scrollBounds,\n    options: { loop }\n  }) => {\n    if (!loop) scrollBounds.constrain(dragHandler.pointerDown())\n    scrollBody.seek()\n  }\n\n  const render: AnimationsRenderType = (\n    {\n      scrollBody,\n      translate,\n      location,\n      offsetLocation,\n      previousLocation,\n      scrollLooper,\n      slideLooper,\n      dragHandler,\n      animation,\n      eventHandler,\n      scrollBounds,\n      options: { loop }\n    },\n    alpha\n  ) => {\n    const shouldSettle = scrollBody.settled()\n    const withinBounds = !scrollBounds.shouldConstrain()\n    const hasSettled = loop ? shouldSettle : shouldSettle && withinBounds\n    const hasSettledAndIdle = hasSettled && !dragHandler.pointerDown()\n\n    if (hasSettledAndIdle) animation.stop()\n\n    const interpolatedLocation =\n      location.get() * alpha + previousLocation.get() * (1 - alpha)\n\n    offsetLocation.set(interpolatedLocation)\n\n    if (loop) {\n      scrollLooper.loop(scrollBody.direction())\n      slideLooper.loop()\n    }\n\n    translate.to(offsetLocation.get())\n\n    if (hasSettledAndIdle) eventHandler.emit('settle')\n    if (!hasSettled) eventHandler.emit('scroll')\n  }\n\n  const animation = Animations(\n    ownerDocument,\n    ownerWindow,\n    () => update(engine),\n    (alpha: number) => render(engine, alpha)\n  )\n\n  // Shared\n  const friction = 0.68\n  const startLocation = scrollSnaps[index.get()]\n  const location = Vector1D(startLocation)\n  const previousLocation = Vector1D(startLocation)\n  const offsetLocation = Vector1D(startLocation)\n  const target = Vector1D(startLocation)\n  const scrollBody = ScrollBody(\n    location,\n    offsetLocation,\n    previousLocation,\n    target,\n    duration,\n    friction\n  )\n  const scrollTarget = ScrollTarget(\n    loop,\n    scrollSnaps,\n    contentSize,\n    limit,\n    target\n  )\n  const scrollTo = ScrollTo(\n    animation,\n    index,\n    indexPrevious,\n    scrollBody,\n    scrollTarget,\n    target,\n    eventHandler\n  )\n  const scrollProgress = ScrollProgress(limit)\n  const eventStore = EventStore()\n  const slidesInView = SlidesInView(\n    container,\n    slides,\n    eventHandler,\n    inViewThreshold\n  )\n  const { slideRegistry } = SlideRegistry(\n    containSnaps,\n    containScroll,\n    scrollSnaps,\n    scrollContainLimit,\n    slidesToScroll,\n    slideIndexes\n  )\n  const slideFocus = SlideFocus(\n    root,\n    slides,\n    slideRegistry,\n    scrollTo,\n    scrollBody,\n    eventStore,\n    eventHandler,\n    watchFocus\n  )\n\n  // Engine\n  const engine: EngineType = {\n    ownerDocument,\n    ownerWindow,\n    eventHandler,\n    containerRect,\n    slideRects,\n    animation,\n    axis,\n    dragHandler: DragHandler(\n      axis,\n      root,\n      ownerDocument,\n      ownerWindow,\n      target,\n      DragTracker(axis, ownerWindow),\n      location,\n      animation,\n      scrollTo,\n      scrollBody,\n      scrollTarget,\n      index,\n      eventHandler,\n      percentOfView,\n      dragFree,\n      dragThreshold,\n      skipSnaps,\n      friction,\n      watchDrag\n    ),\n    eventStore,\n    percentOfView,\n    index,\n    indexPrevious,\n    limit,\n    location,\n    offsetLocation,\n    previousLocation,\n    options,\n    resizeHandler: ResizeHandler(\n      container,\n      eventHandler,\n      ownerWindow,\n      slides,\n      axis,\n      watchResize,\n      nodeRects\n    ),\n    scrollBody,\n    scrollBounds: ScrollBounds(\n      limit,\n      offsetLocation,\n      target,\n      scrollBody,\n      percentOfView\n    ),\n    scrollLooper: ScrollLooper(contentSize, limit, offsetLocation, [\n      location,\n      offsetLocation,\n      previousLocation,\n      target\n    ]),\n    scrollProgress,\n    scrollSnapList: scrollSnaps.map(scrollProgress.get),\n    scrollSnaps,\n    scrollTarget,\n    scrollTo,\n    slideLooper: SlideLooper(\n      axis,\n      viewSize,\n      contentSize,\n      slideSizes,\n      slideSizesWithGaps,\n      snaps,\n      scrollSnaps,\n      offsetLocation,\n      slides\n    ),\n    slideFocus,\n    slidesHandler: SlidesHandler(container, eventHandler, watchSlides),\n    slidesInView,\n    slideIndexes,\n    slideRegistry,\n    slidesToScroll,\n    target,\n    translate: Translate(axis, container)\n  }\n\n  return engine\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\n\ntype CallbackType = (emblaApi: EmblaCarouselType, evt: EmblaEventType) => void\ntype ListenersType = Partial<{ [key in EmblaEventType]: CallbackType[] }>\n\nexport type EmblaEventType = EmblaEventListType[keyof EmblaEventListType]\n\nexport interface EmblaEventListType {\n  init: 'init'\n  pointerDown: 'pointerDown'\n  pointerUp: 'pointerUp'\n  slidesChanged: 'slidesChanged'\n  slidesInView: 'slidesInView'\n  scroll: 'scroll'\n  select: 'select'\n  settle: 'settle'\n  destroy: 'destroy'\n  reInit: 'reInit'\n  resize: 'resize'\n  slideFocusStart: 'slideFocusStart'\n  slideFocus: 'slideFocus'\n}\n\nexport type EventHandlerType = {\n  init: (emblaApi: EmblaCarouselType) => void\n  emit: (evt: EmblaEventType) => EventHandlerType\n  on: (evt: EmblaEventType, cb: CallbackType) => EventHandlerType\n  off: (evt: EmblaEventType, cb: CallbackType) => EventHandlerType\n  clear: () => void\n}\n\nexport function EventHandler(): EventHandlerType {\n  let listeners: ListenersType = {}\n  let api: EmblaCarouselType\n\n  function init(emblaApi: EmblaCarouselType): void {\n    api = emblaApi\n  }\n\n  function getListeners(evt: EmblaEventType): CallbackType[] {\n    return listeners[evt] || []\n  }\n\n  function emit(evt: EmblaEventType): EventHandlerType {\n    getListeners(evt).forEach((e) => e(api, evt))\n    return self\n  }\n\n  function on(evt: EmblaEventType, cb: CallbackType): EventHandlerType {\n    listeners[evt] = getListeners(evt).concat([cb])\n    return self\n  }\n\n  function off(evt: EmblaEventType, cb: CallbackType): EventHandlerType {\n    listeners[evt] = getListeners(evt).filter((e) => e !== cb)\n    return self\n  }\n\n  function clear(): void {\n    listeners = {}\n  }\n\n  const self: EventHandlerType = {\n    init,\n    emit,\n    off,\n    on,\n    clear\n  }\n  return self\n}\n", "import { AlignmentOptionType } from './Alignment'\nimport { AxisDirectionOptionType, AxisOptionType } from './Axis'\nimport { SlidesToScrollOptionType } from './SlidesToScroll'\nimport { ScrollContainOptionType } from './ScrollContain'\nimport { DragHandlerOptionType } from './DragHandler'\nimport { ResizeHandlerOptionType } from './ResizeHandler'\nimport { SlidesHandlerOptionType } from './SlidesHandler'\nimport { SlidesInViewOptionsType } from './SlidesInView'\nimport { FocusHandlerOptionType } from './SlideFocus'\n\nexport type LooseOptionsType = {\n  [key: string]: unknown\n}\n\nexport type CreateOptionsType<Type extends LooseOptionsType> = Type & {\n  active: boolean\n  breakpoints: {\n    [key: string]: Omit<Partial<CreateOptionsType<Type>>, 'breakpoints'>\n  }\n}\n\nexport type OptionsType = CreateOptionsType<{\n  align: AlignmentOptionType\n  axis: AxisOptionType\n  container: string | HTMLElement | null\n  slides: string | HTMLElement[] | NodeListOf<HTMLElement> | null\n  containScroll: ScrollContainOptionType\n  direction: AxisDirectionOptionType\n  slidesToScroll: SlidesToScrollOptionType\n  dragFree: boolean\n  dragThreshold: number\n  inViewThreshold: SlidesInViewOptionsType\n  loop: boolean\n  skipSnaps: boolean\n  duration: number\n  startIndex: number\n  watchDrag: DragHandlerOptionType\n  watchResize: ResizeHandlerOptionType\n  watchSlides: SlidesHandlerOptionType\n  watchFocus: FocusHandlerOptionType\n}>\n\nexport const defaultOptions: OptionsType = {\n  align: 'center',\n  axis: 'x',\n  container: null,\n  slides: null,\n  containScroll: 'trimSnaps',\n  direction: 'ltr',\n  slidesToScroll: 1,\n  inViewThreshold: 0,\n  breakpoints: {},\n  dragFree: false,\n  dragThreshold: 10,\n  loop: false,\n  skipSnaps: false,\n  duration: 25,\n  startIndex: 0,\n  active: true,\n  watchDrag: true,\n  watchResize: true,\n  watchSlides: true,\n  watchFocus: true\n}\n\nexport type EmblaOptionsType = Partial<OptionsType>\n", "import { LooseOptionsType, CreateOptionsType } from './Options'\nimport { objectKeys, objectsMergeDeep, WindowType } from './utils'\n\ntype OptionsType = Partial<CreateOptionsType<LooseOptionsType>>\n\nexport type OptionsHandlerType = {\n  mergeOptions: <TypeA extends OptionsType, TypeB extends OptionsType>(\n    optionsA: TypeA,\n    optionsB?: TypeB\n  ) => TypeA\n  optionsAtMedia: <Type extends OptionsType>(options: Type) => Type\n  optionsMediaQueries: (optionsList: OptionsType[]) => MediaQueryList[]\n}\n\nexport function OptionsHandler(ownerWindow: WindowType): OptionsHandlerType {\n  function mergeOptions<TypeA extends OptionsType, TypeB extends OptionsType>(\n    optionsA: TypeA,\n    optionsB?: TypeB\n  ): TypeA {\n    return <TypeA>objectsMergeDeep(optionsA, optionsB || {})\n  }\n\n  function optionsAtMedia<Type extends OptionsType>(options: Type): Type {\n    const optionsAtMedia = options.breakpoints || {}\n    const matchedMediaOptions = objectKeys(optionsAtMedia)\n      .filter((media) => ownerWindow.matchMedia(media).matches)\n      .map((media) => optionsAtMedia[media])\n      .reduce((a, mediaOption) => mergeOptions(a, mediaOption), {})\n\n    return mergeOptions(options, matchedMediaOptions)\n  }\n\n  function optionsMediaQueries(optionsList: OptionsType[]): MediaQueryList[] {\n    return optionsList\n      .map((options) => objectKeys(options.breakpoints || {}))\n      .reduce((acc, mediaQueries) => acc.concat(mediaQueries), [])\n      .map(ownerWindow.matchMedia)\n  }\n\n  const self: OptionsHandlerType = {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  }\n  return self\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\nimport { OptionsHandlerType } from './OptionsHandler'\nimport { EmblaPluginsType, EmblaPluginType } from './Plugins'\n\nexport type PluginsHandlerType = {\n  init: (\n    emblaApi: EmblaCarouselType,\n    plugins: EmblaPluginType[]\n  ) => EmblaPluginsType\n  destroy: () => void\n}\n\nexport function PluginsHandler(\n  optionsHandler: OptionsHandlerType\n): PluginsHandlerType {\n  let activePlugins: EmblaPluginType[] = []\n\n  function init(\n    emblaApi: EmblaCarouselType,\n    plugins: EmblaPluginType[]\n  ): EmblaPluginsType {\n    activePlugins = plugins.filter(\n      ({ options }) => optionsHandler.optionsAtMedia(options).active !== false\n    )\n    activePlugins.forEach((plugin) => plugin.init(emblaApi, optionsHandler))\n\n    return plugins.reduce(\n      (map, plugin) => Object.assign(map, { [plugin.name]: plugin }),\n      {}\n    )\n  }\n\n  function destroy(): void {\n    activePlugins = activePlugins.filter((plugin) => plugin.destroy())\n  }\n\n  const self: PluginsHandlerType = {\n    init,\n    destroy\n  }\n  return self\n}\n", "import { Engine, EngineType } from './Engine'\nimport { EventStore } from './EventStore'\nimport { EventHandler, EventHandlerType } from './EventHandler'\nimport { defaultOptions, EmblaOptionsType, OptionsType } from './Options'\nimport { OptionsHandler } from './OptionsHandler'\nimport { PluginsHandler } from './PluginsHandler'\nimport { EmblaPluginsType, EmblaPluginType } from './Plugins'\nimport { isString, WindowType } from './utils'\n\nexport type EmblaCarouselType = {\n  canScrollNext: () => boolean\n  canScrollPrev: () => boolean\n  containerNode: () => HTMLElement\n  internalEngine: () => EngineType\n  destroy: () => void\n  off: EventHandlerType['off']\n  on: EventHandlerType['on']\n  emit: EventHandlerType['emit']\n  plugins: () => EmblaPluginsType\n  previousScrollSnap: () => number\n  reInit: (options?: EmblaOptionsType, plugins?: EmblaPluginType[]) => void\n  rootNode: () => HTMLElement\n  scrollNext: (jump?: boolean) => void\n  scrollPrev: (jump?: boolean) => void\n  scrollProgress: () => number\n  scrollSnapList: () => number[]\n  scrollTo: (index: number, jump?: boolean) => void\n  selectedScrollSnap: () => number\n  slideNodes: () => HTMLElement[]\n  slidesInView: () => number[]\n  slidesNotInView: () => number[]\n}\n\nfunction EmblaCarousel(\n  root: HTMLElement,\n  userOptions?: EmblaOptionsType,\n  userPlugins?: EmblaPluginType[]\n): EmblaCarouselType {\n  const ownerDocument = root.ownerDocument\n  const ownerWindow = <WindowType>ownerDocument.defaultView\n  const optionsHandler = OptionsHandler(ownerWindow)\n  const pluginsHandler = PluginsHandler(optionsHandler)\n  const mediaHandlers = EventStore()\n  const eventHandler = EventHandler()\n  const { mergeOptions, optionsAtMedia, optionsMediaQueries } = optionsHandler\n  const { on, off, emit } = eventHandler\n  const reInit = reActivate\n\n  let destroyed = false\n  let engine: EngineType\n  let optionsBase = mergeOptions(defaultOptions, EmblaCarousel.globalOptions)\n  let options = mergeOptions(optionsBase)\n  let pluginList: EmblaPluginType[] = []\n  let pluginApis: EmblaPluginsType\n\n  let container: HTMLElement\n  let slides: HTMLElement[]\n\n  function storeElements(): void {\n    const { container: userContainer, slides: userSlides } = options\n\n    const customContainer = isString(userContainer)\n      ? root.querySelector(userContainer)\n      : userContainer\n    container = <HTMLElement>(customContainer || root.children[0])\n\n    const customSlides = isString(userSlides)\n      ? container.querySelectorAll(userSlides)\n      : userSlides\n    slides = <HTMLElement[]>[].slice.call(customSlides || container.children)\n  }\n\n  function createEngine(options: OptionsType): EngineType {\n    const engine = Engine(\n      root,\n      container,\n      slides,\n      ownerDocument,\n      ownerWindow,\n      options,\n      eventHandler\n    )\n\n    if (options.loop && !engine.slideLooper.canLoop()) {\n      const optionsWithoutLoop = Object.assign({}, options, { loop: false })\n      return createEngine(optionsWithoutLoop)\n    }\n    return engine\n  }\n\n  function activate(\n    withOptions?: EmblaOptionsType,\n    withPlugins?: EmblaPluginType[]\n  ): void {\n    if (destroyed) return\n\n    optionsBase = mergeOptions(optionsBase, withOptions)\n    options = optionsAtMedia(optionsBase)\n    pluginList = withPlugins || pluginList\n\n    storeElements()\n\n    engine = createEngine(options)\n\n    optionsMediaQueries([\n      optionsBase,\n      ...pluginList.map(({ options }) => options)\n    ]).forEach((query) => mediaHandlers.add(query, 'change', reActivate))\n\n    if (!options.active) return\n\n    engine.translate.to(engine.location.get())\n    engine.animation.init()\n    engine.slidesInView.init()\n    engine.slideFocus.init(self)\n    engine.eventHandler.init(self)\n    engine.resizeHandler.init(self)\n    engine.slidesHandler.init(self)\n\n    if (engine.options.loop) engine.slideLooper.loop()\n    if (container.offsetParent && slides.length) engine.dragHandler.init(self)\n\n    pluginApis = pluginsHandler.init(self, pluginList)\n  }\n\n  function reActivate(\n    withOptions?: EmblaOptionsType,\n    withPlugins?: EmblaPluginType[]\n  ): void {\n    const startIndex = selectedScrollSnap()\n    deActivate()\n    activate(mergeOptions({ startIndex }, withOptions), withPlugins)\n    eventHandler.emit('reInit')\n  }\n\n  function deActivate(): void {\n    engine.dragHandler.destroy()\n    engine.eventStore.clear()\n    engine.translate.clear()\n    engine.slideLooper.clear()\n    engine.resizeHandler.destroy()\n    engine.slidesHandler.destroy()\n    engine.slidesInView.destroy()\n    engine.animation.destroy()\n    pluginsHandler.destroy()\n    mediaHandlers.clear()\n  }\n\n  function destroy(): void {\n    if (destroyed) return\n    destroyed = true\n    mediaHandlers.clear()\n    deActivate()\n    eventHandler.emit('destroy')\n    eventHandler.clear()\n  }\n\n  function scrollTo(index: number, jump?: boolean, direction?: number): void {\n    if (!options.active || destroyed) return\n    engine.scrollBody\n      .useBaseFriction()\n      .useDuration(jump === true ? 0 : options.duration)\n    engine.scrollTo.index(index, direction || 0)\n  }\n\n  function scrollNext(jump?: boolean): void {\n    const next = engine.index.add(1).get()\n    scrollTo(next, jump, -1)\n  }\n\n  function scrollPrev(jump?: boolean): void {\n    const prev = engine.index.add(-1).get()\n    scrollTo(prev, jump, 1)\n  }\n\n  function canScrollNext(): boolean {\n    const next = engine.index.add(1).get()\n    return next !== selectedScrollSnap()\n  }\n\n  function canScrollPrev(): boolean {\n    const prev = engine.index.add(-1).get()\n    return prev !== selectedScrollSnap()\n  }\n\n  function scrollSnapList(): number[] {\n    return engine.scrollSnapList\n  }\n\n  function scrollProgress(): number {\n    return engine.scrollProgress.get(engine.offsetLocation.get())\n  }\n\n  function selectedScrollSnap(): number {\n    return engine.index.get()\n  }\n\n  function previousScrollSnap(): number {\n    return engine.indexPrevious.get()\n  }\n\n  function slidesInView(): number[] {\n    return engine.slidesInView.get()\n  }\n\n  function slidesNotInView(): number[] {\n    return engine.slidesInView.get(false)\n  }\n\n  function plugins(): EmblaPluginsType {\n    return pluginApis\n  }\n\n  function internalEngine(): EngineType {\n    return engine\n  }\n\n  function rootNode(): HTMLElement {\n    return root\n  }\n\n  function containerNode(): HTMLElement {\n    return container\n  }\n\n  function slideNodes(): HTMLElement[] {\n    return slides\n  }\n\n  const self: EmblaCarouselType = {\n    canScrollNext,\n    canScrollPrev,\n    containerNode,\n    internalEngine,\n    destroy,\n    off,\n    on,\n    emit,\n    plugins,\n    previousScrollSnap,\n    reInit,\n    rootNode,\n    scrollNext,\n    scrollPrev,\n    scrollProgress,\n    scrollSnapList,\n    scrollTo,\n    selectedScrollSnap,\n    slideNodes,\n    slidesInView,\n    slidesNotInView\n  }\n\n  activate(userOptions, userPlugins)\n  setTimeout(() => eventHandler.emit('init'), 0)\n  return self\n}\n\ndeclare namespace EmblaCarousel {\n  let globalOptions: EmblaOptionsType | undefined\n}\n\nEmblaCarousel.globalOptions = undefined\n\nexport default EmblaCarousel\n"], "mappings": ";;;AAIM,SAAUA,SAASC,SAAgB;AACvC,SAAO,OAAOA,YAAY;AAC5B;AAEM,SAAUC,SAASD,SAAgB;AACvC,SAAO,OAAOA,YAAY;AAC5B;AAEM,SAAUE,UAAUF,SAAgB;AACxC,SAAO,OAAOA,YAAY;AAC5B;AAEM,SAAUG,SAASH,SAAgB;AACvC,SAAOI,OAAOC,UAAUC,SAASC,KAAKP,OAAO,MAAM;AACrD;AAEM,SAAUQ,QAAQC,GAAS;AAC/B,SAAOC,KAAKC,IAAIF,CAAC;AACnB;AAEM,SAAUG,SAASH,GAAS;AAChC,SAAOC,KAAKG,KAAKJ,CAAC;AACpB;AAEgB,SAAAK,SAASC,QAAgBC,QAAc;AACrD,SAAOR,QAAQO,SAASC,MAAM;AAChC;AAEgB,SAAAC,UAAUF,QAAgBC,QAAc;AACtD,MAAID,WAAW,KAAKC,WAAW,EAAG,QAAO;AACzC,MAAIR,QAAQO,MAAM,KAAKP,QAAQQ,MAAM,EAAG,QAAO;AAC/C,QAAME,OAAOJ,SAASN,QAAQO,MAAM,GAAGP,QAAQQ,MAAM,CAAC;AACtD,SAAOR,QAAQU,OAAOH,MAAM;AAC9B;AAEM,SAAUI,mBAAmBC,KAAW;AAC5C,SAAOV,KAAKW,MAAMD,MAAM,GAAG,IAAI;AACjC;AAEM,SAAUE,UAAgBC,OAAa;AAC3C,SAAOC,WAAWD,KAAK,EAAEE,IAAIC,MAAM;AACrC;AAEM,SAAUC,UAAgBJ,OAAa;AAC3C,SAAOA,MAAMK,eAAeL,KAAK,CAAC;AACpC;AAEM,SAAUK,eAAqBL,OAAa;AAChD,SAAOb,KAAKmB,IAAI,GAAGN,MAAMO,SAAS,CAAC;AACrC;AAEgB,SAAAC,iBAAuBR,OAAeS,OAAa;AACjE,SAAOA,UAAUJ,eAAeL,KAAK;AACvC;SAEgBU,gBAAgBxB,GAAWyB,UAAkB,GAAC;AAC5D,SAAOC,MAAMC,KAAKD,MAAM1B,CAAC,GAAG,CAAC4B,GAAGC,MAAMJ,UAAUI,CAAC;AACnD;AAEM,SAAUd,WAAgCe,QAAY;AAC1D,SAAOnC,OAAOoC,KAAKD,MAAM;AAC3B;AAEgB,SAAAE,iBACdC,SACAC,SAAgC;AAEhC,SAAO,CAACD,SAASC,OAAO,EAAEC,OAAO,CAACC,eAAeC,kBAAiB;AAChEtB,eAAWsB,aAAa,EAAEC,QAASC,SAAO;AACxC,YAAMhC,SAAS6B,cAAcG,GAAG;AAChC,YAAMjC,SAAS+B,cAAcE,GAAG;AAChC,YAAMC,aAAa9C,SAASa,MAAM,KAAKb,SAASY,MAAM;AAEtD8B,oBAAcG,GAAG,IAAIC,aACjBR,iBAAiBzB,QAAQD,MAAM,IAC/BA;IACN,CAAC;AACD,WAAO8B;KACN,CAAA,CAAE;AACP;AAEgB,SAAAK,aACdC,KACAC,aAAuB;AAEvB,SACE,OAAOA,YAAYC,eAAe,eAClCF,eAAeC,YAAYC;AAE/B;ACjFgB,SAAAC,UACdC,OACAC,UAAgB;AAEhB,QAAMC,aAAa;IAAEC;IAAOC;IAAQC;;AAEpC,WAASF,QAAK;AACZ,WAAO;EACT;AAEA,WAASC,OAAOlD,GAAS;AACvB,WAAOmD,IAAInD,CAAC,IAAI;EAClB;AAEA,WAASmD,IAAInD,GAAS;AACpB,WAAO+C,WAAW/C;EACpB;AAEA,WAASoD,QAAQpD,GAAWuB,OAAa;AACvC,QAAI/B,SAASsD,KAAK,EAAG,QAAOE,WAAWF,KAAK,EAAE9C,CAAC;AAC/C,WAAO8C,MAAMC,UAAU/C,GAAGuB,KAAK;EACjC;AAEA,QAAM8B,OAAsB;IAC1BD;;AAEF,SAAOC;AACT;SCxBgBC,aAAU;AACxB,MAAIC,YAAgC,CAAA;AAEpC,WAASC,IACPC,MACAC,MACAC,SACAC,UAA4B;IAAEC,SAAS;EAAM,GAAA;AAE7C,QAAIC;AAEJ,QAAI,sBAAsBL,MAAM;AAC9BA,WAAKM,iBAAiBL,MAAMC,SAASC,OAAO;AAC5CE,uBAAiBA,MAAML,KAAKO,oBAAoBN,MAAMC,SAASC,OAAO;IACxE,OAAO;AACL,YAAMK,uBAAuCR;AAC7CQ,2BAAqBC,YAAYP,OAAO;AACxCG,uBAAiBA,MAAMG,qBAAqBH,eAAeH,OAAO;IACpE;AAEAJ,cAAUY,KAAKL,cAAc;AAC7B,WAAOT;EACT;AAEA,WAASe,QAAK;AACZb,gBAAYA,UAAUc,OAAQC,YAAWA,OAAM,CAAE;EACnD;AAEA,QAAMjB,OAAuB;IAC3BG;IACAY;;AAEF,SAAOf;AACT;AChCM,SAAUkB,WACdC,eACA7B,aACA8B,QACAC,QAA+B;AAE/B,QAAMC,yBAAyBrB,WAAU;AACzC,QAAMsB,gBAAgB,MAAO;AAE7B,MAAIC,gBAA+B;AACnC,MAAIC,kBAAkB;AACtB,MAAIC,cAAc;AAElB,WAASC,OAAI;AACXL,2BAAuBnB,IAAIgB,eAAe,oBAAoB,MAAK;AACjE,UAAIA,cAAcS,OAAQC,OAAK;IACjC,CAAC;EACH;AAEA,WAASC,UAAO;AACdC,SAAI;AACJT,2BAAuBP,MAAK;EAC9B;AAEA,WAASiB,QAAQC,WAA8B;AAC7C,QAAI,CAACP,YAAa;AAClB,QAAI,CAACF,eAAe;AAClBA,sBAAgBS;AAChBb,aAAM;AACNA,aAAM;IACR;AAEA,UAAMc,cAAcD,YAAYT;AAChCA,oBAAgBS;AAChBR,uBAAmBS;AAEnB,WAAOT,mBAAmBF,eAAe;AACvCH,aAAM;AACNK,yBAAmBF;IACrB;AAEA,UAAMY,QAAQV,kBAAkBF;AAChCF,WAAOc,KAAK;AAEZ,QAAIT,aAAa;AACfA,oBAAcpC,YAAY8C,sBAAsBJ,OAAO;IACzD;EACF;AAEA,WAASpC,QAAK;AACZ,QAAI8B,YAAa;AACjBA,kBAAcpC,YAAY8C,sBAAsBJ,OAAO;EACzD;AAEA,WAASD,OAAI;AACXzC,gBAAY+C,qBAAqBX,WAAW;AAC5CF,oBAAgB;AAChBC,sBAAkB;AAClBC,kBAAc;EAChB;AAEA,WAASG,QAAK;AACZL,oBAAgB;AAChBC,sBAAkB;EACpB;AAEA,QAAMzB,OAAuB;IAC3B2B;IACAG;IACAlC;IACAmC;IACAX;IACAC;;AAEF,SAAOrB;AACT;AC5EgB,SAAAsC,KACdC,MACAC,kBAAyC;AAEzC,QAAMC,gBAAgBD,qBAAqB;AAC3C,QAAME,aAAaH,SAAS;AAC5B,QAAMI,SAASD,aAAa,MAAM;AAClC,QAAME,QAAQF,aAAa,MAAM;AACjC,QAAM3F,OAAO,CAAC2F,cAAcD,gBAAgB,KAAK;AACjD,QAAMI,YAAYC,aAAY;AAC9B,QAAMC,UAAUC,WAAU;AAE1B,WAASC,YAAYC,UAAsB;AACzC,UAAM;MAAEC;MAAQC;IAAO,IAAGF;AAC1B,WAAOR,aAAaS,SAASC;EAC/B;AAEA,WAASN,eAAY;AACnB,QAAIJ,WAAY,QAAO;AACvB,WAAOD,gBAAgB,UAAU;EACnC;AAEA,WAASO,aAAU;AACjB,QAAIN,WAAY,QAAO;AACvB,WAAOD,gBAAgB,SAAS;EAClC;AAEA,WAASY,UAAU1G,GAAS;AAC1B,WAAOA,IAAII;EACb;AAEA,QAAMiD,OAAiB;IACrB2C;IACAC;IACAC;IACAE;IACAE;IACAI;;AAEF,SAAOrD;AACT;SC1CgBsD,MAAMC,MAAc,GAAGxF,MAAc,GAAC;AACpD,QAAMC,SAAStB,QAAQ6G,MAAMxF,GAAG;AAEhC,WAASyF,WAAW7G,GAAS;AAC3B,WAAOA,IAAI4G;EACb;AAEA,WAASE,WAAW9G,GAAS;AAC3B,WAAOA,IAAIoB;EACb;AAEA,WAAS2F,WAAW/G,GAAS;AAC3B,WAAO6G,WAAW7G,CAAC,KAAK8G,WAAW9G,CAAC;EACtC;AAEA,WAASgH,UAAUhH,GAAS;AAC1B,QAAI,CAAC+G,WAAW/G,CAAC,EAAG,QAAOA;AAC3B,WAAO6G,WAAW7G,CAAC,IAAI4G,MAAMxF;EAC/B;AAEA,WAAS6F,aAAajH,GAAS;AAC7B,QAAI,CAACqB,OAAQ,QAAOrB;AACpB,WAAOA,IAAIqB,SAASpB,KAAKiH,MAAMlH,IAAIoB,OAAOC,MAAM;EAClD;AAEA,QAAMgC,OAAkB;IACtBhC;IACAD;IACAwF;IACAI;IACAD;IACAD;IACAD;IACAI;;AAEF,SAAO5D;AACT;SCvCgB8D,QACd/F,KACA6B,OACAmE,MAAa;AAEb,QAAM;IAAEJ;EAAS,IAAKL,MAAM,GAAGvF,GAAG;AAClC,QAAMiG,UAAUjG,MAAM;AACtB,MAAIkG,UAAUC,YAAYtE,KAAK;AAE/B,WAASsE,YAAYvH,GAAS;AAC5B,WAAO,CAACoH,OAAOJ,UAAUhH,CAAC,IAAID,SAASsH,UAAUrH,KAAKqH,OAAO;EAC/D;AAEA,WAASG,MAAG;AACV,WAAOF;EACT;AAEA,WAASG,IAAIzH,GAAS;AACpBsH,cAAUC,YAAYvH,CAAC;AACvB,WAAOqD;EACT;AAEA,WAASG,IAAIxD,GAAS;AACpB,WAAO0H,MAAK,EAAGD,IAAID,IAAG,IAAKxH,CAAC;EAC9B;AAEA,WAAS0H,QAAK;AACZ,WAAOP,QAAQ/F,KAAKoG,IAAG,GAAIJ,IAAI;EACjC;AAEA,QAAM/D,OAAoB;IACxBmE;IACAC;IACAjE;IACAkE;;AAEF,SAAOrE;AACT;SCXgBsE,YACd/B,MACAgC,UACApD,eACA7B,aACAkF,QACAC,aACAC,UACAC,WACAC,UACAC,YACAC,cACA5G,OACA6G,cACAC,eACAC,UACAC,eACAC,WACAC,cACAC,WAAgC;AAEhC,QAAM;IAAEzC,OAAO0C;IAAWjC;EAAS,IAAKd;AACxC,QAAMgD,aAAa,CAAC,SAAS,UAAU,UAAU;AACjD,QAAMC,kBAAkB;IAAEhF,SAAS;;AACnC,QAAMiF,aAAaxF,WAAU;AAC7B,QAAMyF,aAAazF,WAAU;AAC7B,QAAM0F,oBAAoBrC,MAAM,IAAI,GAAG,EAAEK,UAAUqB,cAAcjF,QAAQ,EAAE,CAAC;AAC5E,QAAM6F,iBAAiB;IAAEC,OAAO;IAAKC,OAAO;;AAC5C,QAAMC,iBAAiB;IAAEF,OAAO;IAAKC,OAAO;;AAC5C,QAAME,YAAYf,WAAW,KAAK;AAElC,MAAIgB,WAAW;AACf,MAAIC,cAAc;AAClB,MAAIC,aAAa;AACjB,MAAIC,gBAAgB;AACpB,MAAIC,gBAAgB;AACpB,MAAIC,eAAe;AACnB,MAAIC,UAAU;AAEd,WAAS5E,KAAK6E,UAA2B;AACvC,QAAI,CAACnB,UAAW;AAEhB,aAASoB,cAAcpH,KAAqB;AAC1C,UAAIjD,UAAUiJ,SAAS,KAAKA,UAAUmB,UAAUnH,GAAG,EAAGqH,MAAKrH,GAAG;IAChE;AAEA,UAAMe,OAAOmE;AACbkB,eACGtF,IAAIC,MAAM,aAAcf,SAAQA,IAAIsH,eAAc,GAAInB,eAAe,EACrErF,IAAIC,MAAM,aAAa,MAAMwG,QAAWpB,eAAe,EACvDrF,IAAIC,MAAM,YAAY,MAAMwG,MAAS,EACrCzG,IAAIC,MAAM,cAAcqG,aAAa,EACrCtG,IAAIC,MAAM,aAAaqG,aAAa,EACpCtG,IAAIC,MAAM,eAAeyG,EAAE,EAC3B1G,IAAIC,MAAM,eAAeyG,EAAE,EAC3B1G,IAAIC,MAAM,SAAS0G,OAAO,IAAI;EACnC;AAEA,WAAShF,UAAO;AACd2D,eAAW1E,MAAK;AAChB2E,eAAW3E,MAAK;EAClB;AAEA,WAASgG,gBAAa;AACpB,UAAM3G,OAAOmG,UAAUpF,gBAAgBoD;AACvCmB,eACGvF,IAAIC,MAAM,aAAa4G,MAAMxB,eAAe,EAC5CrF,IAAIC,MAAM,YAAYyG,EAAE,EACxB1G,IAAIC,MAAM,aAAa4G,MAAMxB,eAAe,EAC5CrF,IAAIC,MAAM,WAAWyG,EAAE;EAC5B;AAEA,WAASI,YAAY7G,MAAa;AAChC,UAAM8G,WAAW9G,KAAK8G,YAAY;AAClC,WAAO3B,WAAW4B,SAASD,QAAQ;EACrC;AAEA,WAASE,aAAU;AACjB,UAAMC,QAAQpC,WAAWc,iBAAiBH;AAC1C,UAAMvF,OAAOkG,UAAU,UAAU;AACjC,WAAOc,MAAMhH,IAAI;EACnB;AAEA,WAASiH,aAAaC,OAAeC,eAAsB;AACzD,UAAMC,OAAOvJ,MAAMiC,IAAIrD,SAASyK,KAAK,IAAI,EAAE;AAC3C,UAAMG,YAAY5C,aAAa6C,WAAWJ,OAAO,CAACtC,QAAQ,EAAE2C;AAE5D,QAAI3C,YAAYvI,QAAQ6K,KAAK,IAAI5B,kBAAmB,QAAO+B;AAC3D,QAAIvC,aAAaqC,cAAe,QAAOE,YAAY;AAEnD,WAAO5C,aAAa+C,QAAQJ,KAAKtD,IAAG,GAAI,CAAC,EAAEyD;EAC7C;AAEA,WAASlB,KAAKrH,KAAqB;AACjC,UAAMyI,aAAa1I,aAAaC,KAAKC,WAAW;AAChDiH,cAAUuB;AACVxB,mBAAerB,YAAY6C,cAAc,CAACzI,IAAI0I,WAAW9B;AACzDA,eAAWjJ,SAASwH,OAAOL,IAAG,GAAIO,SAASP,IAAG,CAAE,KAAK;AAErD,QAAI2D,cAAczI,IAAI2I,WAAW,EAAG;AACpC,QAAIf,YAAY5H,IAAImF,MAAiB,EAAG;AAExC4B,oBAAgB;AAChB3B,gBAAYwD,YAAY5I,GAAG;AAC3BwF,eAAWqD,YAAY,CAAC,EAAEC,YAAY,CAAC;AACvC3D,WAAOJ,IAAIM,QAAQ;AACnBqC,kBAAa;AACbb,kBAAczB,YAAY2D,UAAU/I,GAAG;AACvC8G,iBAAa1B,YAAY2D,UAAU/I,KAAKiG,SAAS;AACjDP,iBAAasD,KAAK,aAAa;EACjC;AAEA,WAASrB,KAAK3H,KAAqB;AACjC,UAAMiJ,aAAa,CAAClJ,aAAaC,KAAKC,WAAW;AACjD,QAAIgJ,cAAcjJ,IAAIkJ,QAAQvK,UAAU,EAAG,QAAO6I,GAAGxH,GAAG;AAExD,UAAMmJ,aAAa/D,YAAY2D,UAAU/I,GAAG;AAC5C,UAAMoJ,YAAYhE,YAAY2D,UAAU/I,KAAKiG,SAAS;AACtD,UAAMoD,aAAa1L,SAASwL,YAAYtC,WAAW;AACnD,UAAMyC,YAAY3L,SAASyL,WAAWtC,UAAU;AAEhD,QAAI,CAACE,iBAAiB,CAACE,SAAS;AAC9B,UAAI,CAAClH,IAAIuJ,WAAY,QAAO/B,GAAGxH,GAAG;AAClCgH,sBAAgBqC,aAAaC;AAC7B,UAAI,CAACtC,cAAe,QAAOQ,GAAGxH,GAAG;IACnC;AACA,UAAMjC,OAAOqH,YAAYoE,YAAYxJ,GAAG;AACxC,QAAIqJ,aAAaxD,cAAeoB,gBAAe;AAE/CzB,eAAWqD,YAAY,GAAG,EAAEC,YAAY,IAAI;AAC5CxD,cAAU/E,MAAK;AACf4E,WAAOrE,IAAIkD,UAAUjG,IAAI,CAAC;AAC1BiC,QAAIsH,eAAc;EACpB;AAEA,WAASE,GAAGxH,KAAqB;AAC/B,UAAMyJ,kBAAkBhE,aAAa6C,WAAW,GAAG,KAAK;AACxD,UAAMH,gBAAgBsB,gBAAgB5K,UAAUA,MAAMiG,IAAG;AACzD,UAAM4E,WAAWtE,YAAYuE,UAAU3J,GAAG,IAAI+H,WAAU;AACxD,UAAMG,QAAQD,aAAajE,UAAU0F,QAAQ,GAAGvB,aAAa;AAC7D,UAAMyB,cAAc9L,UAAU4L,UAAUxB,KAAK;AAC7C,UAAM2B,QAAQlD,YAAY,KAAKiD;AAC/B,UAAME,WAAW/D,eAAe6D,cAAc;AAE9C5C,oBAAgB;AAChBD,oBAAgB;AAChBV,eAAW3E,MAAK;AAChB8D,eAAWsD,YAAYe,KAAK,EAAEhB,YAAYiB,QAAQ;AAClDvE,aAASgD,SAASL,OAAO,CAACtC,QAAQ;AAClCsB,cAAU;AACVxB,iBAAasD,KAAK,WAAW;EAC/B;AAEA,WAASvB,MAAMzH,KAAe;AAC5B,QAAIiH,cAAc;AAChBjH,UAAI+J,gBAAe;AACnB/J,UAAIsH,eAAc;AAClBL,qBAAe;IACjB;EACF;AAEA,WAAS2B,cAAW;AAClB,WAAO7B;EACT;AAEA,QAAMpG,OAAwB;IAC5B2B;IACAG;IACAmG;;AAEF,SAAOjI;AACT;AClMgB,SAAAqJ,YACd9G,MACAjD,aAAuB;AAEvB,QAAMgK,cAAc;AAEpB,MAAIC;AACJ,MAAIC;AAEJ,WAASC,SAASpK,KAAqB;AACrC,WAAOA,IAAI4C;EACb;AAEA,WAASmG,UAAU/I,KAAuBqK,SAAwB;AAChE,UAAMC,WAAWD,WAAWnH,KAAKI;AACjC,UAAMiH,QAA0B,SAASD,aAAa,MAAM,MAAM,GAAG;AACrE,YAAQvK,aAAaC,KAAKC,WAAW,IAAID,MAAMA,IAAIkJ,QAAQ,CAAC,GAAGqB,KAAK;EACtE;AAEA,WAAS3B,YAAY5I,KAAqB;AACxCkK,iBAAalK;AACbmK,gBAAYnK;AACZ,WAAO+I,UAAU/I,GAAG;EACtB;AAEA,WAASwJ,YAAYxJ,KAAqB;AACxC,UAAMjC,OAAOgL,UAAU/I,GAAG,IAAI+I,UAAUoB,SAAS;AACjD,UAAMK,UAAUJ,SAASpK,GAAG,IAAIoK,SAASF,UAAU,IAAID;AAEvDE,gBAAYnK;AACZ,QAAIwK,QAASN,cAAalK;AAC1B,WAAOjC;EACT;AAEA,WAAS4L,UAAU3J,KAAqB;AACtC,QAAI,CAACkK,cAAc,CAACC,UAAW,QAAO;AACtC,UAAMM,WAAW1B,UAAUoB,SAAS,IAAIpB,UAAUmB,UAAU;AAC5D,UAAMQ,WAAWN,SAASpK,GAAG,IAAIoK,SAASF,UAAU;AACpD,UAAMM,UAAUJ,SAASpK,GAAG,IAAIoK,SAASD,SAAS,IAAIF;AACtD,UAAM/B,QAAQuC,WAAWC;AACzB,UAAMC,UAAUD,YAAY,CAACF,WAAWnN,QAAQ6K,KAAK,IAAI;AAEzD,WAAOyC,UAAUzC,QAAQ;EAC3B;AAEA,QAAMvH,OAAwB;IAC5BiI;IACAY;IACAG;IACAZ;;AAEF,SAAOpI;AACT;SCpDgBiK,YAAS;AACvB,WAASlK,QAAQK,MAAiB;AAChC,UAAM;MAAE8J;MAAWC;MAAYC;MAAaC;IAAY,IAAKjK;AAC7D,UAAMkK,SAAuB;MAC3BC,KAAKL;MACLM,OAAOL,aAAaC;MACpBK,QAAQP,YAAYG;MACpBK,MAAMP;MACN/G,OAAOgH;MACPjH,QAAQkH;;AAGV,WAAOC;EACT;AAEA,QAAMtK,OAAsB;IAC1BD;;AAEF,SAAOC;AACT;AC5BM,SAAU2K,cAAcjL,UAAgB;AAC5C,WAASK,QAAQpD,GAAS;AACxB,WAAO+C,YAAY/C,IAAI;EACzB;AAEA,QAAMqD,OAA0B;IAC9BD;;AAEF,SAAOC;AACT;ACKgB,SAAA4K,cACdC,WACA9F,cACAzF,aACAwL,QACAvI,MACAwI,aACAC,WAAwB;AAExB,QAAMC,eAAe,CAACJ,SAAS,EAAEK,OAAOJ,MAAM;AAC9C,MAAIK;AACJ,MAAIC;AACJ,MAAIC,aAAuB,CAAA;AAC3B,MAAIC,YAAY;AAEhB,WAASC,SAASnL,MAAiB;AACjC,WAAOmC,KAAKU,YAAY+H,UAAUjL,QAAQK,IAAI,CAAC;EACjD;AAEA,WAASuB,KAAK6E,UAA2B;AACvC,QAAI,CAACuE,YAAa;AAElBK,oBAAgBG,SAASV,SAAS;AAClCQ,iBAAaP,OAAOnN,IAAI4N,QAAQ;AAEhC,aAASC,gBAAgBC,SAA8B;AACrD,iBAAWC,SAASD,SAAS;AAC3B,YAAIH,UAAW;AAEf,cAAMK,cAAcD,MAAMlH,WAAWqG;AACrC,cAAMe,aAAad,OAAOe,QAAqBH,MAAMlH,MAAM;AAC3D,cAAMsH,WAAWH,cAAcP,gBAAgBC,WAAWO,UAAU;AACpE,cAAMG,UAAUR,SAASI,cAAcd,YAAYC,OAAOc,UAAU,CAAC;AACrE,cAAMI,WAAWtP,QAAQqP,UAAUD,QAAQ;AAE3C,YAAIE,YAAY,KAAK;AACnBxF,mBAASyF,OAAM;AACflH,uBAAasD,KAAK,QAAQ;AAE1B;QACF;MACF;IACF;AAEA8C,qBAAiB,IAAIe,eAAgBT,aAAW;AAC9C,UAAIrP,UAAU2O,WAAW,KAAKA,YAAYvE,UAAUiF,OAAO,GAAG;AAC5DD,wBAAgBC,OAAO;MACzB;IACF,CAAC;AAEDnM,gBAAY8C,sBAAsB,MAAK;AACrC6I,mBAAahM,QAASmB,UAAS+K,eAAegB,QAAQ/L,IAAI,CAAC;IAC7D,CAAC;EACH;AAEA,WAAS0B,UAAO;AACdwJ,gBAAY;AACZ,QAAIH,eAAgBA,gBAAeiB,WAAU;EAC/C;AAEA,QAAMpM,OAA0B;IAC9B2B;IACAG;;AAEF,SAAO9B;AACT;ACpEgB,SAAAqM,WACd3H,UACA4H,gBACAC,kBACA/H,QACAgI,cACApH,cAAoB;AAEpB,MAAIqH,iBAAiB;AACrB,MAAIC,kBAAkB;AACtB,MAAIC,iBAAiBH;AACrB,MAAII,iBAAiBxH;AACrB,MAAIyH,cAAcnI,SAASP,IAAG;AAC9B,MAAI2I,sBAAsB;AAE1B,WAASC,OAAI;AACX,UAAMC,eAAexI,OAAOL,IAAG,IAAKO,SAASP,IAAG;AAChD,UAAM8I,YAAY,CAACN;AACnB,QAAIO,iBAAiB;AAErB,QAAID,WAAW;AACbR,uBAAiB;AACjBF,uBAAiBnI,IAAII,MAAM;AAC3BE,eAASN,IAAII,MAAM;AAEnB0I,uBAAiBF;IACnB,OAAO;AACLT,uBAAiBnI,IAAIM,QAAQ;AAE7B+H,wBAAkBO,eAAeL;AACjCF,wBAAkBG;AAClBC,qBAAeJ;AACf/H,eAASvE,IAAIsM,cAAc;AAE3BS,uBAAiBL,cAAcC;IACjC;AAEAJ,sBAAkB5P,SAASoQ,cAAc;AACzCJ,0BAAsBD;AACtB,WAAO7M;EACT;AAEA,WAASmN,UAAO;AACd,UAAM/P,OAAOoH,OAAOL,IAAG,IAAKmI,eAAenI,IAAG;AAC9C,WAAOzH,QAAQU,IAAI,IAAI;EACzB;AAEA,WAASgQ,WAAQ;AACf,WAAOT;EACT;AAEA,WAAStJ,YAAS;AAChB,WAAOqJ;EACT;AAEA,WAASW,WAAQ;AACf,WAAOZ;EACT;AAEA,WAASa,kBAAe;AACtB,WAAOnF,YAAYqE,YAAY;EACjC;AAEA,WAASe,kBAAe;AACtB,WAAOrF,YAAY9C,YAAY;EACjC;AAEA,WAAS+C,YAAYxL,GAAS;AAC5BgQ,qBAAiBhQ;AACjB,WAAOqD;EACT;AAEA,WAASkI,YAAYvL,GAAS;AAC5BiQ,qBAAiBjQ;AACjB,WAAOqD;EACT;AAEA,QAAMA,OAAuB;IAC3BqD;IACA+J;IACAC;IACAN;IACAI;IACAI;IACAD;IACApF;IACAC;;AAEF,SAAOnI;AACT;AC5FM,SAAUwN,aACdC,OACA/I,UACAF,QACAK,YACAG,eAAgC;AAEhC,QAAM0I,oBAAoB1I,cAAcjF,QAAQ,EAAE;AAClD,QAAM4N,sBAAsB3I,cAAcjF,QAAQ,EAAE;AACpD,QAAM6N,gBAAgBtK,MAAM,KAAK,IAAI;AACrC,MAAIuK,WAAW;AAEf,WAASC,kBAAe;AACtB,QAAID,SAAU,QAAO;AACrB,QAAI,CAACJ,MAAM/J,WAAWc,OAAOL,IAAG,CAAE,EAAG,QAAO;AAC5C,QAAI,CAACsJ,MAAM/J,WAAWgB,SAASP,IAAG,CAAE,EAAG,QAAO;AAC9C,WAAO;EACT;AAEA,WAASR,UAAUsE,aAAoB;AACrC,QAAI,CAAC6F,gBAAe,EAAI;AACxB,UAAMC,OAAON,MAAMjK,WAAWkB,SAASP,IAAG,CAAE,IAAI,QAAQ;AACxD,UAAM6J,aAAatR,QAAQ+Q,MAAMM,IAAI,IAAIrJ,SAASP,IAAG,CAAE;AACvD,UAAM8J,eAAezJ,OAAOL,IAAG,IAAKO,SAASP,IAAG;AAChD,UAAMgF,WAAWyE,cAAcjK,UAAUqK,aAAaL,mBAAmB;AAEzEnJ,WAAO0J,SAASD,eAAe9E,QAAQ;AAEvC,QAAI,CAAClB,eAAevL,QAAQuR,YAAY,IAAIP,mBAAmB;AAC7DlJ,aAAOJ,IAAIqJ,MAAM9J,UAAUa,OAAOL,IAAG,CAAE,CAAC;AACxCU,iBAAWsD,YAAY,EAAE,EAAEoF,gBAAe;IAC5C;EACF;AAEA,WAASY,aAAaC,QAAe;AACnCP,eAAW,CAACO;EACd;AAEA,QAAMpO,OAAyB;IAC7B8N;IACAnK;IACAwK;;AAEF,SAAOnO;AACT;AC9CM,SAAUqO,cACd3O,UACA4O,aACAC,cACAC,eACAC,gBAAsB;AAEtB,QAAMC,eAAepL,MAAM,CAACgL,cAAc5O,UAAU,CAAC;AACrD,QAAMiP,eAAeC,eAAc;AACnC,QAAMC,qBAAqBC,uBAAsB;AACjD,QAAMC,iBAAiBC,iBAAgB;AAEvC,WAASC,kBAAkBC,OAAeC,MAAY;AACpD,WAAOnS,SAASkS,OAAOC,IAAI,KAAK;EAClC;AAEA,WAASL,yBAAsB;AAC7B,UAAMM,YAAYT,aAAa,CAAC;AAChC,UAAMU,UAAUxR,UAAU8Q,YAAY;AACtC,UAAMpL,MAAMoL,aAAaW,YAAYF,SAAS;AAC9C,UAAMrR,MAAM4Q,aAAa9C,QAAQwD,OAAO,IAAI;AAC5C,WAAO/L,MAAMC,KAAKxF,GAAG;EACvB;AAEA,WAAS6Q,iBAAc;AACrB,WAAOL,aACJ5Q,IAAI,CAAC4R,aAAarR,UAAS;AAC1B,YAAM;QAAEqF;QAAKxF;MAAK,IAAG2Q;AACrB,YAAMS,OAAOT,aAAa/K,UAAU4L,WAAW;AAC/C,YAAMC,UAAU,CAACtR;AACjB,YAAMuR,SAASxR,iBAAiBsQ,cAAcrQ,KAAK;AACnD,UAAIsR,QAAS,QAAOzR;AACpB,UAAI0R,OAAQ,QAAOlM;AACnB,UAAI0L,kBAAkB1L,KAAK4L,IAAI,EAAG,QAAO5L;AACzC,UAAI0L,kBAAkBlR,KAAKoR,IAAI,EAAG,QAAOpR;AACzC,aAAOoR;IACT,CAAC,EACAxR,IAAK+R,iBAAgBC,WAAWD,YAAYE,QAAQ,CAAC,CAAC,CAAC;EAC5D;AAEA,WAASZ,mBAAgB;AACvB,QAAIV,eAAe5O,WAAW+O,eAAgB,QAAO,CAACC,aAAa3Q,GAAG;AACtE,QAAIyQ,kBAAkB,YAAa,QAAOG;AAC1C,UAAM;MAAEpL;MAAKxF;IAAK,IAAG8Q;AACrB,WAAOF,aAAakB,MAAMtM,KAAKxF,GAAG;EACpC;AAEA,QAAMiC,OAA0B;IAC9B+O;IACAF;;AAEF,SAAO7O;AACT;SCvDgB8P,YACdxB,aACAyB,aACAhM,MAAa;AAEb,QAAMhG,MAAMgS,YAAY,CAAC;AACzB,QAAMxM,MAAMQ,OAAOhG,MAAMuQ,cAAczQ,UAAUkS,WAAW;AAC5D,QAAMtC,QAAQnK,MAAMC,KAAKxF,GAAG;AAE5B,QAAMiC,OAAwB;IAC5ByN;;AAEF,SAAOzN;AACT;ACbM,SAAUgQ,aACd1B,aACAb,OACA/I,UACAuL,SAAuB;AAEvB,QAAMC,cAAc;AACpB,QAAM3M,MAAMkK,MAAMlK,MAAM2M;AACxB,QAAMnS,MAAM0P,MAAM1P,MAAMmS;AACxB,QAAM;IAAE1M;IAAYC;EAAY,IAAGH,MAAMC,KAAKxF,GAAG;AAEjD,WAASoS,WAAW9M,WAAiB;AACnC,QAAIA,cAAc,EAAG,QAAOI,WAAWiB,SAASP,IAAG,CAAE;AACrD,QAAId,cAAc,GAAI,QAAOG,WAAWkB,SAASP,IAAG,CAAE;AACtD,WAAO;EACT;AAEA,WAASJ,KAAKV,WAAiB;AAC7B,QAAI,CAAC8M,WAAW9M,SAAS,EAAG;AAE5B,UAAM+M,eAAe9B,eAAejL,YAAY;AAChD4M,YAAQhR,QAASoR,OAAMA,EAAElQ,IAAIiQ,YAAY,CAAC;EAC5C;AAEA,QAAMpQ,OAAyB;IAC7B+D;;AAEF,SAAO/D;AACT;AC7BM,SAAUsQ,eAAe7C,OAAgB;AAC7C,QAAM;IAAE1P;IAAKC;EAAQ,IAAGyP;AAExB,WAAStJ,IAAIxH,GAAS;AACpB,UAAMmM,kBAAkBnM,IAAIoB;AAC5B,WAAOC,SAAS8K,kBAAkB,CAAC9K,SAAS;EAC9C;AAEA,QAAMgC,OAA2B;IAC/BmE;;AAEF,SAAOnE;AACT;ACPM,SAAUuQ,YACdhO,MACAiO,WACAC,eACAC,YACAC,gBAAkC;AAElC,QAAM;IAAE9N;IAAWE;EAAS,IAAGR;AAC/B,QAAM;IAAEqO;EAAa,IAAGD;AACxB,QAAME,aAAaC,aAAY,EAAGnT,IAAI6S,UAAUzQ,OAAO;AACvD,QAAMgR,QAAQC,iBAAgB;AAC9B,QAAMzC,eAAe0C,eAAc;AAEnC,WAASH,eAAY;AACnB,WAAOF,YAAYF,UAAU,EAC1B/S,IAAKuT,WAAUrT,UAAUqT,KAAK,EAAEnO,OAAO,IAAImO,MAAM,CAAC,EAAErO,SAAS,CAAC,EAC9DlF,IAAIjB,OAAO;EAChB;AAEA,WAASsU,mBAAgB;AACvB,WAAON,WACJ/S,IAAKwT,UAASV,cAAc5N,SAAS,IAAIsO,KAAKtO,SAAS,CAAC,EACxDlF,IAAKwR,UAAS,CAACzS,QAAQyS,IAAI,CAAC;EACjC;AAEA,WAAS8B,iBAAc;AACrB,WAAOL,YAAYG,KAAK,EACrBpT,IAAKyT,OAAMA,EAAE,CAAC,CAAC,EACfzT,IAAI,CAACwR,MAAMjR,UAAUiR,OAAO0B,WAAW3S,KAAK,CAAC;EAClD;AAEA,QAAM8B,OAAwB;IAC5B+Q;IACAxC;;AAEF,SAAOvO;AACT;ACjCgB,SAAAqR,cACdC,cACA9C,eACAuB,aACAlB,oBACA8B,gBACAY,cAAsB;AAEtB,QAAM;IAAEX;EAAa,IAAGD;AACxB,QAAM;IAAEpN;IAAKxF;EAAK,IAAG8Q;AACrB,QAAM2C,gBAAgBC,oBAAmB;AAEzC,WAASA,sBAAmB;AAC1B,UAAMC,sBAAsBd,YAAYW,YAAY;AACpD,UAAMI,eAAe,CAACL,gBAAgB9C,kBAAkB;AAExD,QAAIuB,YAAY/R,WAAW,EAAG,QAAO,CAACuT,YAAY;AAClD,QAAII,aAAc,QAAOD;AAEzB,WAAOA,oBAAoB7B,MAAMtM,KAAKxF,GAAG,EAAEJ,IAAI,CAACiU,OAAO1T,OAAO2T,WAAU;AACtE,YAAMrC,UAAU,CAACtR;AACjB,YAAMuR,SAASxR,iBAAiB4T,QAAQ3T,KAAK;AAE7C,UAAIsR,SAAS;AACX,cAAMsC,QAAQjU,UAAUgU,OAAO,CAAC,CAAC,IAAI;AACrC,eAAO1T,gBAAgB2T,KAAK;MAC9B;AACA,UAAIrC,QAAQ;AACV,cAAMqC,QAAQhU,eAAeyT,YAAY,IAAI1T,UAAUgU,MAAM,EAAE,CAAC,IAAI;AACpE,eAAO1T,gBAAgB2T,OAAOjU,UAAUgU,MAAM,EAAE,CAAC,CAAC;MACpD;AACA,aAAOD;IACT,CAAC;EACH;AAEA,QAAM5R,OAA0B;IAC9BwR;;AAEF,SAAOxR;AACT;ACtCM,SAAU+R,aACdhO,MACAgM,aACAzB,aACAb,OACAuE,cAA0B;AAE1B,QAAM;IAAEtO;IAAYE;IAAcD;EAAS,IAAK8J;AAEhD,WAASwE,YAAYC,WAAmB;AACtC,WAAOA,UAAUhH,OAAM,EAAGiH,KAAK,CAACC,GAAGC,MAAM3V,QAAQ0V,CAAC,IAAI1V,QAAQ2V,CAAC,CAAC,EAAE,CAAC;EACrE;AAEA,WAASC,eAAe9N,QAAc;AACpC,UAAMoD,WAAW7D,OAAOH,aAAaY,MAAM,IAAIb,UAAUa,MAAM;AAC/D,UAAM+N,kBAAkBxC,YACrBpS,IAAI,CAACwR,MAAMjR,YAAW;MAAEd,MAAMoV,SAASrD,OAAOvH,UAAU,CAAC;MAAG1J,OAAAA;MAAQ,EACpEiU,KAAK,CAACM,IAAIC,OAAOhW,QAAQ+V,GAAGrV,IAAI,IAAIV,QAAQgW,GAAGtV,IAAI,CAAC;AAEvD,UAAM;MAAEc;IAAO,IAAGqU,gBAAgB,CAAC;AACnC,WAAO;MAAErU;MAAO0J;;EAClB;AAEA,WAAS4K,SAAShO,QAAgBnB,WAAiB;AACjD,UAAMsP,UAAU,CAACnO,QAAQA,SAAS8J,aAAa9J,SAAS8J,WAAW;AAEnE,QAAI,CAACvK,KAAM,QAAOS;AAClB,QAAI,CAACnB,UAAW,QAAO4O,YAAYU,OAAO;AAE1C,UAAMC,kBAAkBD,QAAQ3R,OAAQ6R,OAAM/V,SAAS+V,CAAC,MAAMxP,SAAS;AACvE,QAAIuP,gBAAgB5U,OAAQ,QAAOiU,YAAYW,eAAe;AAC9D,WAAO/U,UAAU8U,OAAO,IAAIrE;EAC9B;AAEA,WAASzG,QAAQ3J,OAAemF,WAAiB;AAC/C,UAAMyP,aAAa/C,YAAY7R,KAAK,IAAI8T,aAAa7N,IAAG;AACxD,UAAMyD,WAAW4K,SAASM,YAAYzP,SAAS;AAC/C,WAAO;MAAEnF;MAAO0J;;EAClB;AAEA,WAASD,WAAWC,UAAkBuH,MAAa;AACjD,UAAM3K,SAASwN,aAAa7N,IAAG,IAAKyD;AACpC,UAAM;MAAE1J;MAAO0J,UAAUmL;IAAoB,IAAGT,eAAe9N,MAAM;AACrE,UAAMwO,eAAe,CAACjP,QAAQL,WAAWc,MAAM;AAE/C,QAAI,CAAC2K,QAAQ6D,aAAc,QAAO;MAAE9U;MAAO0J;;AAE3C,UAAMkL,aAAa/C,YAAY7R,KAAK,IAAI6U;AACxC,UAAME,eAAerL,WAAW4K,SAASM,YAAY,CAAC;AAEtD,WAAO;MAAE5U;MAAO0J,UAAUqL;;EAC5B;AAEA,QAAMjT,OAAyB;IAC7B2H;IACAE;IACA2K;;AAEF,SAAOxS;AACT;AC9DgB,SAAAkT,SACdvO,WACAwO,cACAC,eACAvO,YACAC,cACAkN,cACAjN,cAA8B;AAE9B,WAASH,SAASJ,QAAkB;AAClC,UAAM6O,eAAe7O,OAAOoD;AAC5B,UAAM0L,YAAY9O,OAAOtG,UAAUiV,aAAahP,IAAG;AAEnD6N,iBAAa7R,IAAIkT,YAAY;AAE7B,QAAIA,cAAc;AAChB,UAAIxO,WAAWuI,SAAQ,GAAI;AACzBzI,kBAAU/E,MAAK;MACjB,OAAO;AACL+E,kBAAUvD,OAAM;AAChBuD,kBAAUtD,OAAO,CAAC;AAClBsD,kBAAUvD,OAAM;MAClB;IACF;AAEA,QAAIkS,WAAW;AACbF,oBAAchP,IAAI+O,aAAahP,IAAG,CAAE;AACpCgP,mBAAa/O,IAAII,OAAOtG,KAAK;AAC7B6G,mBAAasD,KAAK,QAAQ;IAC5B;EACF;AAEA,WAAST,SAASjL,GAAWwS,MAAa;AACxC,UAAM3K,SAASM,aAAa6C,WAAWhL,GAAGwS,IAAI;AAC9CvK,aAASJ,MAAM;EACjB;AAEA,WAAStG,MAAMvB,GAAW0G,WAAiB;AACzC,UAAMkQ,cAAcJ,aAAa9O,MAAK,EAAGD,IAAIzH,CAAC;AAC9C,UAAM6H,SAASM,aAAa+C,QAAQ0L,YAAYpP,IAAG,GAAId,SAAS;AAChEuB,aAASJ,MAAM;EACjB;AAEA,QAAMxE,OAAqB;IACzB4H;IACA1J;;AAEF,SAAO8B;AACT;SCzCgBwT,WACdC,MACA3I,QACA0G,eACA5M,UACAC,YACA6O,YACA3O,cACA4O,YAAkC;AAElC,QAAMC,uBAAuB;IAAEpT,SAAS;IAAMqT,SAAS;;AACvD,MAAIC,mBAAmB;AAEvB,WAASnS,KAAK6E,UAA2B;AACvC,QAAI,CAACmN,WAAY;AAEjB,aAASnI,gBAAgBtN,OAAa;AACpC,YAAM6V,WAAU,oBAAIC,KAAI,GAAGC,QAAO;AAClC,YAAMlK,WAAWgK,UAAUD;AAE3B,UAAI/J,WAAW,GAAI;AAEnBhF,mBAAasD,KAAK,iBAAiB;AACnCoL,WAAKS,aAAa;AAElB,YAAMtC,QAAQJ,cAAc2C,UAAWvC,CAAAA,WAAUA,OAAMzK,SAASjJ,KAAK,CAAC;AAEtE,UAAI,CAACjC,SAAS2V,KAAK,EAAG;AAEtB/M,iBAAWsD,YAAY,CAAC;AACxBvD,eAAS1G,MAAM0T,OAAO,CAAC;AAEvB7M,mBAAasD,KAAK,YAAY;IAChC;AAEAqL,eAAWvT,IAAIiU,UAAU,WAAWC,kBAAkB,KAAK;AAE3DvJ,WAAO7L,QAAQ,CAACqV,OAAO1I,eAAc;AACnC8H,iBAAWvT,IACTmU,OACA,SACCjV,SAAmB;AAClB,YAAIjD,UAAUuX,UAAU,KAAKA,WAAWnN,UAAUnH,GAAG,GAAG;AACtDmM,0BAAgBI,UAAU;QAC5B;SAEFgI,oBAAoB;IAExB,CAAC;EACH;AAEA,WAASS,iBAAiBE,OAAoB;AAC5C,QAAIA,MAAMC,SAAS,MAAOV,qBAAmB,oBAAIE,KAAI,GAAGC,QAAO;EACjE;AAEA,QAAMjU,OAAuB;IAC3B2B;;AAEF,SAAO3B;AACT;ACrEM,SAAUyU,SAASC,cAAoB;AAC3C,MAAIC,QAAQD;AAEZ,WAASvQ,MAAG;AACV,WAAOwQ;EACT;AAEA,WAASvQ,IAAIzH,GAAwB;AACnCgY,YAAQC,eAAejY,CAAC;EAC1B;AAEA,WAASwD,IAAIxD,GAAwB;AACnCgY,aAASC,eAAejY,CAAC;EAC3B;AAEA,WAASuR,SAASvR,GAAwB;AACxCgY,aAASC,eAAejY,CAAC;EAC3B;AAEA,WAASiY,eAAejY,GAAwB;AAC9C,WAAOV,SAASU,CAAC,IAAIA,IAAIA,EAAEwH,IAAG;EAChC;AAEA,QAAMnE,OAAqB;IACzBmE;IACAC;IACAjE;IACA+N;;AAEF,SAAOlO;AACT;AC9BgB,SAAA6U,UACdtS,MACAsI,WAAsB;AAEtB,QAAMiK,YAAYvS,KAAKI,WAAW,MAAMoS,IAAIC;AAC5C,QAAMC,iBAAiBpK,UAAUqK;AACjC,MAAIC,iBAAgC;AACpC,MAAItH,WAAW;AAEf,WAASkH,EAAEpY,GAAS;AAClB,WAAO,eAAeA,CAAC;EACzB;AAEA,WAASqY,EAAErY,GAAS;AAClB,WAAO,mBAAmBA,CAAC;EAC7B;AAEA,WAASyY,GAAG5Q,QAAc;AACxB,QAAIqJ,SAAU;AAEd,UAAMwH,YAAYhY,mBAAmBkF,KAAKc,UAAUmB,MAAM,CAAC;AAC3D,QAAI6Q,cAAcF,eAAgB;AAElCF,mBAAeK,YAAYR,UAAUO,SAAS;AAC9CF,qBAAiBE;EACnB;AAEA,WAASlH,aAAaC,QAAe;AACnCP,eAAW,CAACO;EACd;AAEA,WAASrN,QAAK;AACZ,QAAI8M,SAAU;AACdoH,mBAAeK,YAAY;AAC3B,QAAI,CAACzK,UAAU0K,aAAa,OAAO,EAAG1K,WAAU2K,gBAAgB,OAAO;EACzE;AAEA,QAAMxV,OAAsB;IAC1Be;IACAqU;IACAjH;;AAEF,SAAOnO;AACT;SC3BgByV,YACdlT,MACA7C,UACA4O,aACAjD,YACAqK,oBACA3E,OACAhB,aACArL,UACAoG,QAAqB;AAErB,QAAM6K,iBAAiB;AACvB,QAAMC,WAAWpY,UAAUkY,kBAAkB;AAC7C,QAAMG,YAAYrY,UAAUkY,kBAAkB,EAAEI,QAAO;AACvD,QAAMC,aAAaC,YAAW,EAAG9K,OAAO+K,UAAS,CAAE;AAEnD,WAASC,iBAAiBC,SAAmB7X,MAAY;AACvD,WAAO6X,QAAQrX,OAAO,CAACsT,GAAW5T,MAAK;AACrC,aAAO4T,IAAIsD,mBAAmBlX,CAAC;OAC9BF,IAAI;EACT;AAEA,WAAS8X,YAAYD,SAAmBE,KAAW;AACjD,WAAOF,QAAQrX,OAAO,CAACsT,GAAa5T,MAAK;AACvC,YAAM8X,eAAeJ,iBAAiB9D,GAAGiE,GAAG;AAC5C,aAAOC,eAAe,IAAIlE,EAAElH,OAAO,CAAC1M,CAAC,CAAC,IAAI4T;OACzC,CAAA,CAAE;EACP;AAEA,WAASmE,gBAAgBjM,QAAc;AACrC,WAAOyG,MAAMpT,IAAI,CAACwR,MAAMjR,WAAW;MACjC0B,OAAOuP,OAAO9D,WAAWnN,KAAK,IAAIyX,iBAAiBrL;MACnDxK,KAAKqP,OAAOzP,WAAWiW,iBAAiBrL;IACzC,EAAC;EACJ;AAEA,WAASkM,eACPL,SACA7L,QACAmM,WAAkB;AAElB,UAAMC,cAAcH,gBAAgBjM,MAAM;AAE1C,WAAO6L,QAAQxY,IAAKO,WAAS;AAC3B,YAAMyY,UAAUF,YAAY,IAAI,CAACnI;AACjC,YAAMsI,UAAUH,YAAYnI,cAAc;AAC1C,YAAMuI,YAAYJ,YAAY,QAAQ;AACtC,YAAMK,YAAYJ,YAAYxY,KAAK,EAAE2Y,SAAS;AAE9C,aAAO;QACL3Y;QACA4Y;QACAC,eAAetC,SAAS,EAAE;QAC1BK,WAAWD,UAAUtS,MAAMuI,OAAO5M,KAAK,CAAC;QACxCsG,QAAQA,MAAOE,SAASP,IAAG,IAAK2S,YAAYH,UAAUC;;IAE1D,CAAC;EACH;AAEA,WAASZ,cAAW;AAClB,UAAMK,MAAMtG,YAAY,CAAC;AACzB,UAAMoG,UAAUC,YAAYP,WAAWQ,GAAG;AAC1C,WAAOG,eAAeL,SAAS7H,aAAa,KAAK;EACnD;AAEA,WAAS2H,YAAS;AAChB,UAAMI,MAAM3W,WAAWqQ,YAAY,CAAC,IAAI;AACxC,UAAMoG,UAAUC,YAAYR,UAAUS,GAAG;AACzC,WAAOG,eAAeL,SAAS,CAAC7H,aAAa,IAAI;EACnD;AAEA,WAAS0I,UAAO;AACd,WAAOjB,WAAWkB,MAAM,CAAC;MAAE/Y;IAAO,MAAI;AACpC,YAAMgZ,eAAetB,SAAS5U,OAAQxC,OAAMA,MAAMN,KAAK;AACvD,aAAOgY,iBAAiBgB,cAAcxX,QAAQ,KAAK;IACrD,CAAC;EACH;AAEA,WAASqE,OAAI;AACXgS,eAAW9W,QAAS6X,eAAa;AAC/B,YAAM;QAAEtS;QAAQsQ;QAAWiC;MAAa,IAAKD;AAC7C,YAAMK,gBAAgB3S,OAAM;AAC5B,UAAI2S,kBAAkBJ,cAAc5S,IAAG,EAAI;AAC3C2Q,gBAAUM,GAAG+B,aAAa;AAC1BJ,oBAAc3S,IAAI+S,aAAa;IACjC,CAAC;EACH;AAEA,WAASpW,QAAK;AACZgV,eAAW9W,QAAS6X,eAAcA,UAAUhC,UAAU/T,MAAK,CAAE;EAC/D;AAEA,QAAMf,OAAwB;IAC5BgX;IACAjW;IACAgD;IACAgS;;AAEF,SAAO/V;AACT;SC5GgBoX,cACdvM,WACA9F,cACAsS,aAAoC;AAEpC,MAAIC;AACJ,MAAIhM,YAAY;AAEhB,WAAS3J,KAAK6E,UAA2B;AACvC,QAAI,CAAC6Q,YAAa;AAElB,aAAS7L,gBAAgB+L,WAA2B;AAClD,iBAAWC,YAAYD,WAAW;AAChC,YAAIC,SAASnX,SAAS,aAAa;AACjCmG,mBAASyF,OAAM;AACflH,uBAAasD,KAAK,eAAe;AACjC;QACF;MACF;IACF;AAEAiP,uBAAmB,IAAIG,iBAAkBF,eAAa;AACpD,UAAIjM,UAAW;AACf,UAAIlP,UAAUib,WAAW,KAAKA,YAAY7Q,UAAU+Q,SAAS,GAAG;AAC9D/L,wBAAgB+L,SAAS;MAC3B;IACF,CAAC;AAEDD,qBAAiBnL,QAAQtB,WAAW;MAAE6M,WAAW;IAAM,CAAA;EACzD;AAEA,WAAS5V,UAAO;AACd,QAAIwV,iBAAkBA,kBAAiBlL,WAAU;AACjDd,gBAAY;EACd;AAEA,QAAMtL,OAA0B;IAC9B2B;IACAG;;AAEF,SAAO9B;AACT;AC1CM,SAAU2X,aACd9M,WACAC,QACA/F,cACA6S,WAAkC;AAElC,QAAMC,uBAAiD,CAAA;AACvD,MAAIC,cAA+B;AACnC,MAAIC,iBAAkC;AACtC,MAAIC;AACJ,MAAI1M,YAAY;AAEhB,WAAS3J,OAAI;AACXqW,2BAAuB,IAAIC,qBACxBxM,aAAW;AACV,UAAIH,UAAW;AAEfG,cAAQxM,QAASyM,WAAS;AACxB,cAAMxN,QAAQ4M,OAAOe,QAAqBH,MAAMlH,MAAM;AACtDqT,6BAAqB3Z,KAAK,IAAIwN;MAChC,CAAC;AAEDoM,oBAAc;AACdC,uBAAiB;AACjBhT,mBAAasD,KAAK,cAAc;IAClC,GACA;MACEoL,MAAM5I,UAAUqN;MAChBN;IACD,CAAA;AAGH9M,WAAO7L,QAASqV,WAAU0D,qBAAqB7L,QAAQmI,KAAK,CAAC;EAC/D;AAEA,WAASxS,UAAO;AACd,QAAIkW,qBAAsBA,sBAAqB5L,WAAU;AACzDd,gBAAY;EACd;AAEA,WAAS6M,iBAAiBC,QAAe;AACvC,WAAO1a,WAAWma,oBAAoB,EAAE/Y,OACtC,CAACuZ,MAAgBzM,eAAc;AAC7B,YAAM1N,QAAQoa,SAAS1M,UAAU;AACjC,YAAM;QAAE2M;MAAgB,IAAGV,qBAAqB3Z,KAAK;AACrD,YAAMsa,cAAcJ,UAAUG;AAC9B,YAAME,iBAAiB,CAACL,UAAU,CAACG;AAEnC,UAAIC,eAAeC,eAAgBJ,MAAKvX,KAAK5C,KAAK;AAClD,aAAOma;OAET,CAAA,CAAE;EAEN;AAEA,WAASlU,IAAIiU,SAAkB,MAAI;AACjC,QAAIA,UAAUN,YAAa,QAAOA;AAClC,QAAI,CAACM,UAAUL,eAAgB,QAAOA;AAEtC,UAAMxG,eAAe4G,iBAAiBC,MAAM;AAE5C,QAAIA,OAAQN,eAAcvG;AAC1B,QAAI,CAAC6G,OAAQL,kBAAiBxG;AAE9B,WAAOA;EACT;AAEA,QAAMvR,OAAyB;IAC7B2B;IACAG;IACAqC;;AAGF,SAAOnE;AACT;AC9EgB,SAAA0Y,WACdnW,MACAkO,eACAC,YACA5F,QACA6N,aACArZ,aAAuB;AAEvB,QAAM;IAAE2D;IAAaJ;IAAWE;EAAO,IAAKR;AAC5C,QAAMqW,cAAclI,WAAW,CAAC,KAAKiI;AACrC,QAAME,WAAWC,gBAAe;AAChC,QAAMC,SAASC,cAAa;AAC5B,QAAM3N,aAAaqF,WAAW/S,IAAIsF,WAAW;AAC7C,QAAMyS,qBAAqBuD,gBAAe;AAE1C,WAASH,kBAAe;AACtB,QAAI,CAACF,YAAa,QAAO;AACzB,UAAMM,YAAYxI,WAAW,CAAC;AAC9B,WAAOhU,QAAQ+T,cAAc5N,SAAS,IAAIqW,UAAUrW,SAAS,CAAC;EAChE;AAEA,WAASmW,gBAAa;AACpB,QAAI,CAACJ,YAAa,QAAO;AACzB,UAAM1D,QAAQ5V,YAAY6Z,iBAAiBtb,UAAUiN,MAAM,CAAC;AAC5D,WAAO6E,WAAWuF,MAAMkE,iBAAiB,UAAUrW,OAAO,EAAE,CAAC;EAC/D;AAEA,WAASkW,kBAAe;AACtB,WAAOvI,WACJ/S,IAAI,CAACwT,MAAMjT,OAAOgT,UAAS;AAC1B,YAAM1B,UAAU,CAACtR;AACjB,YAAMuR,SAASxR,iBAAiBiT,OAAOhT,KAAK;AAC5C,UAAIsR,QAAS,QAAOnE,WAAWnN,KAAK,IAAI2a;AACxC,UAAIpJ,OAAQ,QAAOpE,WAAWnN,KAAK,IAAI6a;AACvC,aAAO7H,MAAMhT,QAAQ,CAAC,EAAE2E,SAAS,IAAIsO,KAAKtO,SAAS;IACrD,CAAC,EACAlF,IAAIjB,OAAO;EAChB;AAEA,QAAMsD,OAAuB;IAC3BqL;IACAqK;IACAmD;IACAE;;AAEF,SAAO/Y;AACT;SCzCgBqZ,eACd9W,MACA7C,UACAiR,gBACA5M,MACA0M,eACAC,YACAmI,UACAE,QACAtK,gBAAsB;AAEtB,QAAM;IAAE5L;IAAWE;IAASM;EAAS,IAAKd;AAC1C,QAAM+W,gBAAgBrd,SAAS0U,cAAc;AAE7C,WAAS4I,SAAe9b,OAAe+b,WAAiB;AACtD,WAAOhc,UAAUC,KAAK,EACnBuD,OAAQxC,OAAMA,IAAIgb,cAAc,CAAC,EACjC7b,IAAKa,OAAMf,MAAMoS,MAAMrR,GAAGA,IAAIgb,SAAS,CAAC;EAC7C;AAEA,WAASC,OAAahc,OAAa;AACjC,QAAI,CAACA,MAAMO,OAAQ,QAAO,CAAA;AAE1B,WAAOR,UAAUC,KAAK,EACnBqB,OAAO,CAAC+S,QAAkB6H,OAAOxb,UAAS;AACzC,YAAMyb,QAAQ9b,UAAUgU,MAAM,KAAK;AACnC,YAAMrC,UAAUmK,UAAU;AAC1B,YAAMlK,SAASiK,UAAU5b,eAAeL,KAAK;AAE7C,YAAMmc,QAAQnJ,cAAc5N,SAAS,IAAI6N,WAAWiJ,KAAK,EAAE9W,SAAS;AACpE,YAAMgX,QAAQpJ,cAAc5N,SAAS,IAAI6N,WAAWgJ,KAAK,EAAE3W,OAAO;AAClE,YAAM+W,OAAO,CAAC/V,QAAQyL,UAAUnM,UAAUwV,QAAQ,IAAI;AACtD,YAAMkB,OAAO,CAAChW,QAAQ0L,SAASpM,UAAU0V,MAAM,IAAI;AACnD,YAAMiB,YAAYtd,QAAQmd,QAAQE,QAAQH,QAAQE,KAAK;AAEvD,UAAI5b,SAAS8b,YAAYta,WAAW+O,eAAgBoD,QAAO/Q,KAAK4Y,KAAK;AACrE,UAAIjK,OAAQoC,QAAO/Q,KAAKrD,MAAMO,MAAM;AACpC,aAAO6T;IACT,GAAG,CAAA,CAAE,EACJlU,IAAI,CAACsc,aAAa/b,OAAO2T,WAAU;AAClC,YAAMqI,eAAetd,KAAKmB,IAAI8T,OAAO3T,QAAQ,CAAC,KAAK,CAAC;AACpD,aAAOT,MAAMoS,MAAMqK,cAAcD,WAAW;IAC9C,CAAC;EACL;AAEA,WAASrJ,YAAkBnT,OAAa;AACtC,WAAO6b,gBAAgBC,SAAS9b,OAAOkT,cAAc,IAAI8I,OAAOhc,KAAK;EACvE;AAEA,QAAMuC,OAA2B;IAC/B4Q;;AAEF,SAAO5Q;AACT;ACOgB,SAAAma,OACd1G,MACA5I,WACAC,QACA3J,eACA7B,aACAiB,SACAwE,cAA8B;AAG9B,QAAM;IACJtF;IACA8C,MAAM6X;IACN/W;IACAgX;IACAtW;IACAqJ;IACAnI;IACAC;IACAoV;IACA3J,gBAAgBC;IAChBzL;IACAqJ;IACAzD;IACAsM;IACAhS;IACAsO;EACD,IAAGpT;AAGJ,QAAMkO,iBAAiB;AACvB,QAAMzD,YAAYf,UAAS;AAC3B,QAAMwG,gBAAgBzF,UAAUjL,QAAQ8K,SAAS;AACjD,QAAM6F,aAAa5F,OAAOnN,IAAIqN,UAAUjL,OAAO;AAC/C,QAAMwC,OAAOD,KAAK8X,YAAY/W,SAAS;AACvC,QAAM3D,WAAW6C,KAAKU,YAAYwN,aAAa;AAC/C,QAAMzL,gBAAgB2F,cAAcjL,QAAQ;AAC5C,QAAM8Q,YAAYhR,UAAUC,OAAOC,QAAQ;AAC3C,QAAM4R,eAAe,CAACvN,QAAQ,CAAC,CAACyK;AAChC,QAAMmK,cAAc5U,QAAQ,CAAC,CAACyK;AAC9B,QAAM;IAAEnD;IAAYqK;IAAoBmD;IAAUE;EAAQ,IAAGL,WAC3DnW,MACAkO,eACAC,YACA5F,QACA6N,aACArZ,WAAW;AAEb,QAAMqR,iBAAiB0I,eACrB9W,MACA7C,UACAkR,aACA7M,MACA0M,eACAC,YACAmI,UACAE,QACAtK,cAAc;AAEhB,QAAM;IAAEsC;IAAOxC;EAAc,IAAGgC,YAC9BhO,MACAiO,WACAC,eACAC,YACAC,cAAc;AAEhB,QAAMrC,cAAc,CAACzQ,UAAUkT,KAAK,IAAIlT,UAAU6X,kBAAkB;AACpE,QAAM;IAAE3G;IAAgBF;EAAoB,IAAGR,cAC7C3O,UACA4O,aACAC,cACAC,eACAC,cAAc;AAEhB,QAAMsB,cAAcuB,eAAevC,iBAAiBR;AACpD,QAAM;IAAEd;MAAUqC,YAAYxB,aAAayB,aAAahM,IAAI;AAG5D,QAAM7F,QAAQ4F,QAAQhG,eAAeiS,WAAW,GAAGsK,YAAYtW,IAAI;AACnE,QAAMqP,gBAAgBlV,MAAMmG,MAAK;AACjC,QAAMkN,eAAe/T,UAAUsN,MAAM;AAGrC,QAAM1J,SAA+BA,CAAC;IACpCmZ;IACA1V,YAAAA;IACA6J;IACAnO,SAAS;MAAEwD,MAAAA;IAAM;EAAA,MACd;AACH,QAAI,CAACA,MAAM2K,cAAa/K,UAAU4W,YAAYtS,YAAW,CAAE;AAC3DpD,IAAAA,YAAWkI,KAAI;;AAGjB,QAAM1L,SAA+BA,CACnC;IACEwD,YAAAA;IACAiQ;IACApQ,UAAAA;IACA4H,gBAAAA;IACAC,kBAAAA;IACAiO;IACAC;IACAF;IACA5V,WAAAA;IACAI,cAAAA;IACA2J;IACAnO,SAAS;MAAEwD,MAAAA;IAAM;KAEnB5B,UACE;AACF,UAAMuY,eAAe7V,YAAWsI,QAAO;AACvC,UAAMwN,eAAe,CAACjM,aAAaZ,gBAAe;AAClD,UAAM8M,aAAa7W,QAAO2W,eAAeA,gBAAgBC;AACzD,UAAME,oBAAoBD,cAAc,CAACL,YAAYtS,YAAW;AAEhE,QAAI4S,kBAAmBlW,CAAAA,WAAU5C,KAAI;AAErC,UAAM+Y,uBACJpW,UAASP,IAAG,IAAKhC,QAAQoK,kBAAiBpI,IAAG,KAAM,IAAIhC;AAEzDmK,IAAAA,gBAAelI,IAAI0W,oBAAoB;AAEvC,QAAI/W,OAAM;AACRyW,mBAAazW,KAAKc,YAAWxB,UAAS,CAAE;AACxCoX,kBAAY1W,KAAI;IAClB;AAEA+Q,cAAUM,GAAG9I,gBAAenI,IAAG,CAAE;AAEjC,QAAI0W,kBAAmB9V,CAAAA,cAAasD,KAAK,QAAQ;AACjD,QAAI,CAACuS,WAAY7V,CAAAA,cAAasD,KAAK,QAAQ;;AAG7C,QAAM1D,YAAYzD,WAChBC,eACA7B,aACA,MAAM8B,OAAO2Z,MAAM,GAClB5Y,WAAkBd,OAAO0Z,QAAQ5Y,KAAK,CAAC;AAI1C,QAAMgH,WAAW;AACjB,QAAM6R,gBAAgBjL,YAAY7R,MAAMiG,IAAG,CAAE;AAC7C,QAAMO,WAAW+P,SAASuG,aAAa;AACvC,QAAMzO,mBAAmBkI,SAASuG,aAAa;AAC/C,QAAM1O,iBAAiBmI,SAASuG,aAAa;AAC7C,QAAMxW,SAASiQ,SAASuG,aAAa;AACrC,QAAMnW,aAAawH,WACjB3H,UACA4H,gBACAC,kBACA/H,QACA4I,UACAjE,QAAQ;AAEV,QAAMrE,eAAeiN,aACnBhO,MACAgM,aACAzB,aACAb,OACAjJ,MAAM;AAER,QAAMI,WAAWsO,SACfvO,WACAzG,OACAkV,eACAvO,YACAC,cACAN,QACAO,YAAY;AAEd,QAAMkW,iBAAiB3K,eAAe7C,KAAK;AAC3C,QAAMiG,aAAazT,WAAU;AAC7B,QAAMib,eAAevD,aACnB9M,WACAC,QACA/F,cACAuV,eAAe;AAEjB,QAAM;IAAE9I;EAAa,IAAKH,cACxBC,cACA9C,eACAuB,aACAlB,oBACA8B,gBACAY,YAAY;AAEd,QAAM4J,aAAa3H,WACjBC,MACA3I,QACA0G,eACA5M,UACAC,YACA6O,YACA3O,cACA4O,UAAU;AAIZ,QAAMoH,SAAqB;IACzB5Z;IACA7B;IACAyF;IACA0L;IACAC;IACA/L;IACApC;IACAgY,aAAajW,YACX/B,MACAkR,MACAtS,eACA7B,aACAkF,QACA6E,YAAY9G,MAAMjD,WAAW,GAC7BoF,UACAC,WACAC,UACAC,YACAC,cACA5G,OACA6G,cACAC,eACAC,UACAC,eACAC,WACAgE,UACA9D,SAAS;IAEXqO;IACA1O;IACA9G;IACAkV;IACA3F;IACA/I;IACA4H;IACAC;IACAhM;IACA6a,eAAexQ,cACbC,WACA9F,cACAzF,aACAwL,QACAvI,MACAwI,aACAC,SAAS;IAEXnG;IACA6J,cAAclB,aACZC,OACAnB,gBACA9H,QACAK,YACAG,aAAa;IAEfwV,cAAcxK,aAAa1B,aAAab,OAAOnB,gBAAgB,CAC7D5H,UACA4H,gBACAC,kBACA/H,MAAM,CACP;IACDyW;IACAI,gBAAgBtL,YAAYpS,IAAIsd,eAAe9W,GAAG;IAClD4L;IACAjL;IACAF;IACA6V,aAAahF,YACXlT,MACA7C,UACA4O,aACAjD,YACAqK,oBACA3E,OACAhB,aACAzD,gBACAxB,MAAM;IAERqQ;IACAG,eAAelE,cAAcvM,WAAW9F,cAAcsS,WAAW;IACjE6D;IACA3J;IACAC;IACAb;IACAnM;IACAsQ,WAAWD,UAAUtS,MAAMsI,SAAS;;AAGtC,SAAOkQ;AACT;SC5UgBQ,eAAY;AAC1B,MAAIrb,YAA2B,CAAA;AAC/B,MAAIsb;AAEJ,WAAS7Z,KAAK6E,UAA2B;AACvCgV,UAAMhV;EACR;AAEA,WAASiV,aAAapc,KAAmB;AACvC,WAAOa,UAAUb,GAAG,KAAK,CAAA;EAC3B;AAEA,WAASgJ,KAAKhJ,KAAmB;AAC/Boc,iBAAapc,GAAG,EAAEJ,QAASyc,OAAMA,EAAEF,KAAKnc,GAAG,CAAC;AAC5C,WAAOW;EACT;AAEA,WAAS2b,GAAGtc,KAAqBuc,IAAgB;AAC/C1b,cAAUb,GAAG,IAAIoc,aAAapc,GAAG,EAAE6L,OAAO,CAAC0Q,EAAE,CAAC;AAC9C,WAAO5b;EACT;AAEA,WAAS6b,IAAIxc,KAAqBuc,IAAgB;AAChD1b,cAAUb,GAAG,IAAIoc,aAAapc,GAAG,EAAE2B,OAAQ0a,OAAMA,MAAME,EAAE;AACzD,WAAO5b;EACT;AAEA,WAASe,QAAK;AACZb,gBAAY,CAAA;EACd;AAEA,QAAMF,OAAyB;IAC7B2B;IACA0G;IACAwT;IACAF;IACA5a;;AAEF,SAAOf;AACT;AC5BO,IAAM8b,iBAA8B;EACzCrc,OAAO;EACP8C,MAAM;EACNsI,WAAW;EACXC,QAAQ;EACR0D,eAAe;EACfnL,WAAW;EACXsN,gBAAgB;EAChB2J,iBAAiB;EACjByB,aAAa,CAAA;EACb9W,UAAU;EACVC,eAAe;EACfnB,MAAM;EACNoB,WAAW;EACXiI,UAAU;EACViN,YAAY;EACZjM,QAAQ;EACR/I,WAAW;EACX0F,aAAa;EACbsM,aAAa;EACb1D,YAAY;;AChDR,SAAUqI,eAAe1c,aAAuB;AACpD,WAAS2c,aACPC,UACAC,UAAgB;AAEhB,WAAcxd,iBAAiBud,UAAUC,YAAY,CAAA,CAAE;EACzD;AAEA,WAASC,eAAyC7b,SAAa;AAC7D,UAAM6b,kBAAiB7b,QAAQwb,eAAe,CAAA;AAC9C,UAAMM,sBAAsB3e,WAAW0e,eAAc,EAClDpb,OAAQsb,WAAUhd,YAAYid,WAAWD,KAAK,EAAEE,OAAO,EACvD7e,IAAK2e,WAAUF,gBAAeE,KAAK,CAAC,EACpCxd,OAAO,CAACsT,GAAGqK,gBAAgBR,aAAa7J,GAAGqK,WAAW,GAAG,CAAA,CAAE;AAE9D,WAAOR,aAAa1b,SAAS8b,mBAAmB;EAClD;AAEA,WAASK,oBAAoBC,aAA0B;AACrD,WAAOA,YACJhf,IAAK4C,aAAY7C,WAAW6C,QAAQwb,eAAe,CAAA,CAAE,CAAC,EACtDjd,OAAO,CAAC8d,KAAKC,iBAAiBD,IAAI1R,OAAO2R,YAAY,GAAG,CAAA,CAAE,EAC1Dlf,IAAI2B,YAAYid,UAAU;EAC/B;AAEA,QAAMvc,OAA2B;IAC/Bic;IACAG;IACAM;;AAEF,SAAO1c;AACT;ACjCM,SAAU8c,eACdC,gBAAkC;AAElC,MAAIC,gBAAmC,CAAA;AAEvC,WAASrb,KACP6E,UACAyW,SAA0B;AAE1BD,oBAAgBC,QAAQjc,OACtB,CAAC;MAAET;UAAcwc,eAAeX,eAAe7b,OAAO,EAAE6N,WAAW,KAAK;AAE1E4O,kBAAc/d,QAASie,YAAWA,OAAOvb,KAAK6E,UAAUuW,cAAc,CAAC;AAEvE,WAAOE,QAAQne,OACb,CAACnB,KAAKuf,WAAW5gB,OAAO6gB,OAAOxf,KAAK;MAAE,CAACuf,OAAOE,IAAI,GAAGF;IAAQ,CAAA,GAC7D,CAAA,CAAE;EAEN;AAEA,WAASpb,UAAO;AACdkb,oBAAgBA,cAAchc,OAAQkc,YAAWA,OAAOpb,QAAO,CAAE;EACnE;AAEA,QAAM9B,OAA2B;IAC/B2B;IACAG;;AAEF,SAAO9B;AACT;ACRA,SAASqd,cACP5J,MACA6J,aACAC,aAA+B;AAE/B,QAAMpc,gBAAgBsS,KAAKtS;AAC3B,QAAM7B,cAA0B6B,cAAcqc;AAC9C,QAAMT,iBAAiBf,eAAe1c,WAAW;AACjD,QAAMme,iBAAiBX,eAAeC,cAAc;AACpD,QAAMW,gBAAgBzd,WAAU;AAChC,QAAM8E,eAAewW,aAAY;AACjC,QAAM;IAAEU;IAAcG;IAAgBM;EAAmB,IAAKK;AAC9D,QAAM;IAAEpB;IAAIE;IAAKxT;EAAI,IAAKtD;AAC1B,QAAMkH,SAAS0R;AAEf,MAAIrS,YAAY;AAChB,MAAIyP;AACJ,MAAI6C,cAAc3B,aAAaH,gBAAgBuB,cAAcQ,aAAa;AAC1E,MAAItd,UAAU0b,aAAa2B,WAAW;AACtC,MAAIE,aAAgC,CAAA;AACpC,MAAIC;AAEJ,MAAIlT;AACJ,MAAIC;AAEJ,WAASkT,gBAAa;AACpB,UAAM;MAAEnT,WAAWoT;MAAenT,QAAQoT;IAAU,IAAK3d;AAEzD,UAAM4d,kBAAkBhiB,SAAS8hB,aAAa,IAC1CxK,KAAK2K,cAAcH,aAAa,IAChCA;AACJpT,gBAA0BsT,mBAAmB1K,KAAK4K,SAAS,CAAC;AAE5D,UAAMC,eAAeniB,SAAS+hB,UAAU,IACpCrT,UAAU0T,iBAAiBL,UAAU,IACrCA;AACJpT,aAAwB,CAAA,EAAG+E,MAAMpT,KAAK6hB,gBAAgBzT,UAAUwT,QAAQ;EAC1E;AAEA,WAASG,aAAaje,UAAoB;AACxC,UAAMwa,UAASZ,OACb1G,MACA5I,WACAC,QACA3J,eACA7B,aACAiB,UACAwE,YAAY;AAGd,QAAIxE,SAAQwD,QAAQ,CAACgX,QAAON,YAAYzD,QAAO,GAAI;AACjD,YAAMyH,qBAAqBniB,OAAO6gB,OAAO,CAAA,GAAI5c,UAAS;QAAEwD,MAAM;MAAK,CAAE;AACrE,aAAOya,aAAaC,kBAAkB;IACxC;AACA,WAAO1D;EACT;AAEA,WAAS2D,SACPC,aACAC,aAA+B;AAE/B,QAAItT,UAAW;AAEfsS,kBAAc3B,aAAa2B,aAAae,WAAW;AACnDpe,cAAU6b,eAAewB,WAAW;AACpCE,iBAAac,eAAed;AAE5BE,kBAAa;AAEbjD,aAASyD,aAAaje,OAAO;AAE7Bmc,wBAAoB,CAClBkB,aACA,GAAGE,WAAWngB,IAAI,CAAC;MAAE4C,SAAAA;UAAcA,QAAO,CAAC,CAC5C,EAAEtB,QAAS4f,WAAUnB,cAAcvd,IAAI0e,OAAO,UAAUlB,UAAU,CAAC;AAEpE,QAAI,CAACpd,QAAQ6N,OAAQ;AAErB2M,WAAOjG,UAAUM,GAAG2F,OAAOrW,SAASP,IAAG,CAAE;AACzC4W,WAAOpW,UAAUhD,KAAI;AACrBoZ,WAAOG,aAAavZ,KAAI;AACxBoZ,WAAOI,WAAWxZ,KAAK3B,IAAI;AAC3B+a,WAAOhW,aAAapD,KAAK3B,IAAI;AAC7B+a,WAAOK,cAAczZ,KAAK3B,IAAI;AAC9B+a,WAAOO,cAAc3Z,KAAK3B,IAAI;AAE9B,QAAI+a,OAAOxa,QAAQwD,KAAMgX,QAAON,YAAY1W,KAAI;AAChD,QAAI8G,UAAUiU,gBAAgBhU,OAAO9M,OAAQ+c,QAAOR,YAAY5Y,KAAK3B,IAAI;AAEzE+d,iBAAaN,eAAe9b,KAAK3B,MAAM8d,UAAU;EACnD;AAEA,WAASH,WACPgB,aACAC,aAA+B;AAE/B,UAAMvE,aAAa0E,mBAAkB;AACrCC,eAAU;AACVN,aAASzC,aAAa;MAAE5B;IAAU,GAAIsE,WAAW,GAAGC,WAAW;AAC/D7Z,iBAAasD,KAAK,QAAQ;EAC5B;AAEA,WAAS2W,aAAU;AACjBjE,WAAOR,YAAYzY,QAAO;AAC1BiZ,WAAOrH,WAAW3S,MAAK;AACvBga,WAAOjG,UAAU/T,MAAK;AACtBga,WAAON,YAAY1Z,MAAK;AACxBga,WAAOK,cAActZ,QAAO;AAC5BiZ,WAAOO,cAAcxZ,QAAO;AAC5BiZ,WAAOG,aAAapZ,QAAO;AAC3BiZ,WAAOpW,UAAU7C,QAAO;AACxB2b,mBAAe3b,QAAO;AACtB4b,kBAAc3c,MAAK;EACrB;AAEA,WAASe,UAAO;AACd,QAAIwJ,UAAW;AACfA,gBAAY;AACZoS,kBAAc3c,MAAK;AACnBie,eAAU;AACVja,iBAAasD,KAAK,SAAS;AAC3BtD,iBAAahE,MAAK;EACpB;AAEA,WAAS6D,SAAS1G,OAAe+gB,MAAgB5b,WAAkB;AACjE,QAAI,CAAC9C,QAAQ6N,UAAU9C,UAAW;AAClCyP,WAAOlW,WACJ0I,gBAAe,EACfpF,YAAY8W,SAAS,OAAO,IAAI1e,QAAQ6M,QAAQ;AACnD2N,WAAOnW,SAAS1G,MAAMA,OAAOmF,aAAa,CAAC;EAC7C;AAEA,WAAS6b,WAAWD,MAAc;AAChC,UAAMxX,OAAOsT,OAAO7c,MAAMiC,IAAI,CAAC,EAAEgE,IAAG;AACpCS,aAAS6C,MAAMwX,MAAM,EAAE;EACzB;AAEA,WAASE,WAAWF,MAAc;AAChC,UAAMG,OAAOrE,OAAO7c,MAAMiC,IAAI,EAAE,EAAEgE,IAAG;AACrCS,aAASwa,MAAMH,MAAM,CAAC;EACxB;AAEA,WAASI,gBAAa;AACpB,UAAM5X,OAAOsT,OAAO7c,MAAMiC,IAAI,CAAC,EAAEgE,IAAG;AACpC,WAAOsD,SAASsX,mBAAkB;EACpC;AAEA,WAASO,gBAAa;AACpB,UAAMF,OAAOrE,OAAO7c,MAAMiC,IAAI,EAAE,EAAEgE,IAAG;AACrC,WAAOib,SAASL,mBAAkB;EACpC;AAEA,WAAS1D,iBAAc;AACrB,WAAON,OAAOM;EAChB;AAEA,WAASJ,iBAAc;AACrB,WAAOF,OAAOE,eAAe9W,IAAI4W,OAAOzO,eAAenI,IAAG,CAAE;EAC9D;AAEA,WAAS4a,qBAAkB;AACzB,WAAOhE,OAAO7c,MAAMiG,IAAG;EACzB;AAEA,WAASob,qBAAkB;AACzB,WAAOxE,OAAO3H,cAAcjP,IAAG;EACjC;AAEA,WAAS+W,eAAY;AACnB,WAAOH,OAAOG,aAAa/W,IAAG;EAChC;AAEA,WAASqb,kBAAe;AACtB,WAAOzE,OAAOG,aAAa/W,IAAI,KAAK;EACtC;AAEA,WAAS8Y,UAAO;AACd,WAAOc;EACT;AAEA,WAAS0B,iBAAc;AACrB,WAAO1E;EACT;AAEA,WAASxW,WAAQ;AACf,WAAOkP;EACT;AAEA,WAASiM,gBAAa;AACpB,WAAO7U;EACT;AAEA,WAAS8U,aAAU;AACjB,WAAO7U;EACT;AAEA,QAAM9K,OAA0B;IAC9Bqf;IACAC;IACAI;IACAD;IACA3d;IACA+Z;IACAF;IACAtT;IACA4U;IACAsC;IACAtT;IACA1H;IACA2a;IACAC;IACAlE;IACAI;IACAzW;IACAma;IACAY;IACAzE;IACAsE;;AAGFd,WAASpB,aAAaC,WAAW;AACjCqC,aAAW,MAAM7a,aAAasD,KAAK,MAAM,GAAG,CAAC;AAC7C,SAAOrI;AACT;AAMAqd,cAAcQ,gBAAgBjX;", "names": ["isNumber", "subject", "isString", "isBoolean", "isObject", "Object", "prototype", "toString", "call", "mathAbs", "n", "Math", "abs", "mathSign", "sign", "deltaAbs", "valueB", "valueA", "factorAbs", "diff", "roundToTwoDecimals", "num", "round", "arrayKeys", "array", "objectKeys", "map", "Number", "arrayLast", "arrayLastIndex", "max", "length", "arrayIsLastIndex", "index", "arrayFromNumber", "startAt", "Array", "from", "_", "i", "object", "keys", "objectsMergeDeep", "objectA", "objectB", "reduce", "mergedObjects", "currentObject", "for<PERSON>ach", "key", "areObjects", "isMouseEvent", "evt", "ownerWindow", "MouseEvent", "Alignment", "align", "viewSize", "predefined", "start", "center", "end", "measure", "self", "EventStore", "listeners", "add", "node", "type", "handler", "options", "passive", "removeListener", "addEventListener", "removeEventListener", "legacyMediaQueryList", "addListener", "push", "clear", "filter", "remove", "Animations", "ownerDocument", "update", "render", "documentVisibleHandler", "fixedTimeStep", "lastTimeStamp", "accumulatedTime", "animationId", "init", "hidden", "reset", "destroy", "stop", "animate", "timeStamp", "timeElapsed", "alpha", "requestAnimationFrame", "cancelAnimationFrame", "Axis", "axis", "contentDirection", "isRightToLeft", "isVertical", "scroll", "cross", "startEdge", "getStartEdge", "endEdge", "getEndEdge", "measureSize", "nodeRect", "height", "width", "direction", "Limit", "min", "reachedMin", "reachedMax", "reachedAny", "constrain", "removeOffset", "ceil", "Counter", "loop", "loopEnd", "counter", "withinLimit", "get", "set", "clone", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rootNode", "target", "dragTracker", "location", "animation", "scrollTo", "scrollBody", "scrollTarget", "<PERSON><PERSON><PERSON><PERSON>", "percentOfView", "dragFree", "drag<PERSON><PERSON><PERSON><PERSON>", "skipSnaps", "baseFriction", "watchDrag", "crossAxis", "focusNodes", "nonPassiveEvent", "initEvents", "dragEvents", "goToNextThreshold", "snapForceBoost", "mouse", "touch", "freeForceBoost", "baseSpeed", "isMoving", "startScroll", "startCross", "pointerIsDown", "preventScroll", "preventClick", "isMouse", "emblaApi", "downIfAllowed", "down", "preventDefault", "undefined", "up", "click", "addDragEvents", "move", "isFocusNode", "nodeName", "includes", "forceBoost", "boost", "<PERSON><PERSON><PERSON><PERSON>", "force", "targetChanged", "next", "baseForce", "byDistance", "distance", "byIndex", "isMouseEvt", "buttons", "button", "pointerDown", "useFriction", "useDuration", "readPoint", "emit", "isTouchEvt", "touches", "lastScroll", "lastCross", "diffScroll", "diffCross", "cancelable", "pointer<PERSON><PERSON>", "currentLocation", "rawForce", "pointerUp", "forceFactor", "speed", "friction", "stopPropagation", "DragTracker", "logInterval", "startEvent", "lastEvent", "readTime", "evtAxis", "property", "coord", "expired", "diffDrag", "diffTime", "isFlick", "NodeRects", "offsetTop", "offsetLeft", "offsetWidth", "offsetHeight", "offset", "top", "right", "bottom", "left", "PercentOfView", "ResizeHandler", "container", "slides", "watchResize", "nodeRects", "observeNodes", "concat", "resizeObserver", "containerSize", "slideSizes", "destroyed", "readSize", "defaultCallback", "entries", "entry", "<PERSON><PERSON><PERSON><PERSON>", "slideIndex", "indexOf", "lastSize", "newSize", "diffSize", "reInit", "ResizeObserver", "observe", "disconnect", "ScrollBody", "offsetLocation", "previousLocation", "baseDuration", "scrollVelocity", "scrollDirection", "scrollDuration", "scrollFriction", "rawLocation", "rawLocationPrevious", "seek", "displacement", "isInstant", "scrollDistance", "settled", "duration", "velocity", "useBaseDuration", "useBaseFriction", "ScrollBounds", "limit", "pullBackThreshold", "edgeOffsetTolerance", "frictionLimit", "disabled", "shouldConstrain", "edge", "diffToEdge", "diffTo<PERSON>arget", "subtract", "toggleActive", "active", "ScrollContain", "contentSize", "snapsAligned", "containScroll", "pixelTolerance", "scrollBounds", "snapsBounded", "measureBounded", "scrollContainLimit", "findScrollContainLimit", "snapsContained", "measureContained", "usePixelTolerance", "bound", "snap", "startSnap", "endSnap", "lastIndexOf", "snapAligned", "<PERSON><PERSON><PERSON><PERSON>", "isLast", "scrollBound", "parseFloat", "toFixed", "slice", "ScrollLimit", "scrollSnaps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vectors", "jointSafety", "shouldLoop", "loopDistance", "v", "ScrollProgress", "ScrollSnaps", "alignment", "containerRect", "slideRects", "slidesToScroll", "groupSlides", "alignments", "measureSizes", "snaps", "measureUnaligned", "measureAligned", "rects", "rect", "g", "SlideRegistry", "containSnaps", "slideIndexes", "slideRegistry", "createSlideRegistry", "groupedSlideIndexes", "doNotContain", "group", "groups", "range", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetVector", "minDistance", "distances", "sort", "a", "b", "findTargetSnap", "ascDiffsToSnaps", "shortcut", "d1", "d2", "targets", "matchingTargets", "t", "diffToSnap", "targetSnapDistance", "reachedBound", "snapDistance", "ScrollTo", "indexCurrent", "indexPrevious", "distanceDiff", "indexDiff", "targetIndex", "SlideFocus", "root", "eventStore", "watchFocus", "focusListenerOptions", "capture", "lastTabPressTime", "nowTime", "Date", "getTime", "scrollLeft", "findIndex", "document", "registerTabPress", "slide", "event", "code", "Vector1D", "initialValue", "value", "normalizeInput", "Translate", "translate", "x", "y", "containerStyle", "style", "previousTarget", "to", "newTarget", "transform", "getAttribute", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slideSizesWithGaps", "roundingSafety", "ascItems", "descItems", "reverse", "loopPoints", "startPoints", "endPoints", "removeSlideSizes", "indexes", "slidesInGap", "gap", "remainingGap", "findSlideBounds", "findLoopPoints", "isEndEdge", "slideBounds", "initial", "altered", "boundEdge", "loopPoint", "slideLocation", "canLoop", "every", "otherIndexes", "shiftLocation", "SlidesHandler", "watchSlides", "mutationObserver", "mutations", "mutation", "MutationObserver", "childList", "SlidesInView", "threshold", "intersectionEntryMap", "inView<PERSON>ache", "notInViewCache", "intersectionObserver", "IntersectionObserver", "parentElement", "createInViewList", "inView", "list", "parseInt", "isIntersecting", "inViewMatch", "notInViewMatch", "SlideSizes", "readEdgeGap", "withEdgeGap", "startGap", "measureStartGap", "endGap", "measureEndGap", "measureWithGaps", "slideRect", "getComputedStyle", "getPropertyValue", "SlidesToScroll", "groupByNumber", "byNumber", "groupSize", "bySize", "rectB", "rectA", "edgeA", "edgeB", "gapA", "gapB", "chunkSize", "currentSize", "previousSize", "Engine", "scrollAxis", "startIndex", "inViewThreshold", "<PERSON><PERSON><PERSON><PERSON>", "scrollLooper", "slideLooper", "<PERSON><PERSON><PERSON><PERSON>", "withinBounds", "hasSettled", "hasSettledAndIdle", "interpolatedLocation", "engine", "startLocation", "scrollProgress", "slidesInView", "slideFocus", "resize<PERSON><PERSON>ler", "scrollSnapList", "<PERSON><PERSON><PERSON>ler", "EventHandler", "api", "getListeners", "e", "on", "cb", "off", "defaultOptions", "breakpoints", "OptionsHandler", "mergeOptions", "optionsA", "optionsB", "optionsAtMedia", "matchedMediaOptions", "media", "matchMedia", "matches", "mediaOption", "optionsMediaQueries", "optionsList", "acc", "mediaQueries", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "optionsHandler", "activePlugins", "plugins", "plugin", "assign", "name", "EmblaCarousel", "userOptions", "userPlugins", "defaultView", "pluginsHandler", "mediaHandlers", "reActivate", "optionsBase", "globalOptions", "pluginList", "pluginApis", "storeElements", "userContainer", "userSlides", "customContainer", "querySelector", "children", "customSlides", "querySelectorAll", "createEngine", "optionsWithoutLoop", "activate", "withOptions", "with<PERSON><PERSON><PERSON>", "query", "offsetParent", "selectedScrollSnap", "deActivate", "jump", "scrollNext", "scrollPrev", "prev", "canScrollNext", "canScrollPrev", "previousScrollSnap", "slidesNotInView", "internalEngine", "containerNode", "slideNodes", "setTimeout"]}