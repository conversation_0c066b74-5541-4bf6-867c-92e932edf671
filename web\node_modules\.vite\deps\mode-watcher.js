import {
  MediaQuery
} from "./chunk-MFYP3NKL.js";
import "./chunk-Z37IDGP3.js";
import {
  createSubscriber
} from "./chunk-UHLSHST5.js";
import "./chunk-7RQDXF5S.js";
import "./chunk-C5KNTEDU.js";
import {
  add_locations,
  check_target,
  hmr,
  html,
  if_block,
  legacy_api,
  log_if_contains_state,
  onMount,
  prop,
  set_attribute
} from "./chunk-RTJGYWHJ.js";
import {
  append,
  comment,
  head,
  template
} from "./chunk-NTWKMJSY.js";
import {
  FILENAME,
  HMR,
  effect_root,
  equals,
  first_child,
  get,
  on,
  pop,
  proxy,
  push,
  set,
  sibling,
  state,
  strict_equals,
  template_effect,
  untrack,
  user_derived,
  user_effect,
  user_pre_effect
} from "./chunk-2TCGLMOU.js";
import {
  true_default
} from "./chunk-DM772GTT.js";
import "./chunk-RVAV4ZRS.js";
import {
  __commonJS,
  __privateAdd,
  __privateGet,
  __privateMethod,
  __privateSet,
  __toESM
} from "./chunk-NNIHVWYK.js";

// node_modules/inline-style-parser/index.js
var require_inline_style_parser = __commonJS({
  "node_modules/inline-style-parser/index.js"(exports, module) {
    var COMMENT_REGEX = /\/\*[^*]*\*+([^/*][^*]*\*+)*\//g;
    var NEWLINE_REGEX = /\n/g;
    var WHITESPACE_REGEX = /^\s*/;
    var PROPERTY_REGEX = /^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/;
    var COLON_REGEX = /^:\s*/;
    var VALUE_REGEX = /^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/;
    var SEMICOLON_REGEX = /^[;\s]*/;
    var TRIM_REGEX = /^\s+|\s+$/g;
    var NEWLINE = "\n";
    var FORWARD_SLASH = "/";
    var ASTERISK = "*";
    var EMPTY_STRING = "";
    var TYPE_COMMENT = "comment";
    var TYPE_DECLARATION = "declaration";
    module.exports = function(style, options) {
      if (typeof style !== "string") {
        throw new TypeError("First argument must be a string");
      }
      if (!style) return [];
      options = options || {};
      var lineno = 1;
      var column = 1;
      function updatePosition(str) {
        var lines = str.match(NEWLINE_REGEX);
        if (lines) lineno += lines.length;
        var i = str.lastIndexOf(NEWLINE);
        column = ~i ? str.length - i : column + str.length;
      }
      function position() {
        var start = { line: lineno, column };
        return function(node) {
          node.position = new Position(start);
          whitespace();
          return node;
        };
      }
      function Position(start) {
        this.start = start;
        this.end = { line: lineno, column };
        this.source = options.source;
      }
      Position.prototype.content = style;
      var errorsList = [];
      function error(msg) {
        var err = new Error(
          options.source + ":" + lineno + ":" + column + ": " + msg
        );
        err.reason = msg;
        err.filename = options.source;
        err.line = lineno;
        err.column = column;
        err.source = style;
        if (options.silent) {
          errorsList.push(err);
        } else {
          throw err;
        }
      }
      function match(re) {
        var m = re.exec(style);
        if (!m) return;
        var str = m[0];
        updatePosition(str);
        style = style.slice(str.length);
        return m;
      }
      function whitespace() {
        match(WHITESPACE_REGEX);
      }
      function comments(rules) {
        var c;
        rules = rules || [];
        while (c = comment2()) {
          if (c !== false) {
            rules.push(c);
          }
        }
        return rules;
      }
      function comment2() {
        var pos = position();
        if (FORWARD_SLASH != style.charAt(0) || ASTERISK != style.charAt(1)) return;
        var i = 2;
        while (EMPTY_STRING != style.charAt(i) && (ASTERISK != style.charAt(i) || FORWARD_SLASH != style.charAt(i + 1))) {
          ++i;
        }
        i += 2;
        if (EMPTY_STRING === style.charAt(i - 1)) {
          return error("End of comment missing");
        }
        var str = style.slice(2, i - 2);
        column += 2;
        updatePosition(str);
        style = style.slice(i);
        column += 2;
        return pos({
          type: TYPE_COMMENT,
          comment: str
        });
      }
      function declaration() {
        var pos = position();
        var prop2 = match(PROPERTY_REGEX);
        if (!prop2) return;
        comment2();
        if (!match(COLON_REGEX)) return error("property missing ':'");
        var val = match(VALUE_REGEX);
        var ret = pos({
          type: TYPE_DECLARATION,
          property: trim(prop2[0].replace(COMMENT_REGEX, EMPTY_STRING)),
          value: val ? trim(val[0].replace(COMMENT_REGEX, EMPTY_STRING)) : EMPTY_STRING
        });
        match(SEMICOLON_REGEX);
        return ret;
      }
      function declarations() {
        var decls = [];
        comments(decls);
        var decl;
        while (decl = declaration()) {
          if (decl !== false) {
            decls.push(decl);
            comments(decls);
          }
        }
        return decls;
      }
      whitespace();
      return declarations();
    };
    function trim(str) {
      return str ? str.replace(TRIM_REGEX, EMPTY_STRING) : EMPTY_STRING;
    }
  }
});

// node_modules/style-to-object/cjs/index.js
var require_cjs = __commonJS({
  "node_modules/style-to-object/cjs/index.js"(exports) {
    "use strict";
    var __importDefault = exports && exports.__importDefault || function(mod) {
      return mod && mod.__esModule ? mod : { "default": mod };
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.default = StyleToObject2;
    var inline_style_parser_1 = __importDefault(require_inline_style_parser());
    function StyleToObject2(style, iterator) {
      var styleObject = null;
      if (!style || typeof style !== "string") {
        return styleObject;
      }
      var declarations = (0, inline_style_parser_1.default)(style);
      var hasIterator = typeof iterator === "function";
      declarations.forEach(function(declaration) {
        if (declaration.type !== "declaration") {
          return;
        }
        var property = declaration.property, value = declaration.value;
        if (hasIterator) {
          iterator(property, value, declaration);
        } else if (value) {
          styleObject = styleObject || {};
          styleObject[property] = value;
        }
      });
      return styleObject;
    }
  }
});

// node_modules/mode-watcher/node_modules/runed/dist/internal/configurable-globals.js
var defaultWindow = true_default && typeof window !== "undefined" ? window : void 0;
var defaultDocument = true_default && typeof window !== "undefined" ? window.document : void 0;
var defaultNavigator = true_default && typeof window !== "undefined" ? window.navigator : void 0;
var defaultLocation = true_default && typeof window !== "undefined" ? window.location : void 0;

// node_modules/mode-watcher/node_modules/runed/dist/internal/utils/dom.js
function getActiveElement(document2) {
  let activeElement3 = document2.activeElement;
  while (activeElement3 == null ? void 0 : activeElement3.shadowRoot) {
    const node = activeElement3.shadowRoot.activeElement;
    if (node === activeElement3)
      break;
    else
      activeElement3 = node;
  }
  return activeElement3;
}

// node_modules/mode-watcher/node_modules/runed/dist/utilities/active-element/active-element.svelte.js
var _document, _subscribe;
var ActiveElement = class {
  constructor(options = {}) {
    __privateAdd(this, _document);
    __privateAdd(this, _subscribe);
    const {
      window: window2 = defaultWindow,
      document: document2 = window2 == null ? void 0 : window2.document
    } = options;
    if (strict_equals(window2, void 0)) return;
    __privateSet(this, _document, document2);
    __privateSet(this, _subscribe, createSubscriber((update) => {
      const cleanupFocusIn = on(window2, "focusin", update);
      const cleanupFocusOut = on(window2, "focusout", update);
      return () => {
        cleanupFocusIn();
        cleanupFocusOut();
      };
    }));
  }
  get current() {
    var _a;
    (_a = __privateGet(this, _subscribe)) == null ? void 0 : _a.call(this);
    if (!__privateGet(this, _document)) return null;
    return getActiveElement(__privateGet(this, _document));
  }
};
_document = new WeakMap();
_subscribe = new WeakMap();
var activeElement = new ActiveElement();

// node_modules/mode-watcher/node_modules/runed/dist/utilities/watch/watch.svelte.js
function runEffect(flush, effect) {
  switch (flush) {
    case "post":
      user_effect(effect);
      break;
    case "pre":
      user_pre_effect(effect);
      break;
  }
}
function runWatcher(sources, flush, effect, options = {}) {
  const { lazy = false } = options;
  let active = !lazy;
  let previousValues = Array.isArray(sources) ? [] : void 0;
  runEffect(flush, () => {
    const values = Array.isArray(sources) ? sources.map((source) => source()) : sources();
    if (!active) {
      active = true;
      previousValues = values;
      return;
    }
    const cleanup = untrack(() => effect(values, previousValues));
    previousValues = values;
    return cleanup;
  });
}
function runWatcherOnce(sources, flush, effect) {
  const cleanupRoot = effect_root(() => {
    let stop = false;
    runWatcher(
      sources,
      flush,
      (values, previousValues) => {
        if (stop) {
          cleanupRoot();
          return;
        }
        const cleanup = effect(values, previousValues);
        stop = true;
        return cleanup;
      },
      // Running the effect immediately just once makes no sense at all.
      // That's just `onMount` with extra steps.
      { lazy: true }
    );
  });
  user_effect(() => {
    return cleanupRoot;
  });
}
function watch(sources, effect, options) {
  runWatcher(sources, "post", effect, options);
}
function watchPre(sources, effect, options) {
  runWatcher(sources, "pre", effect, options);
}
watch.pre = watchPre;
function watchOnce(source, effect) {
  runWatcherOnce(source, "post", effect);
}
function watchOncePre(source, effect) {
  runWatcherOnce(source, "pre", effect);
}
watchOnce.pre = watchOncePre;

// node_modules/mode-watcher/node_modules/runed/dist/utilities/persisted-state/persisted-state.svelte.js
function getStorage(storageType, window2) {
  switch (storageType) {
    case "local":
      return window2.localStorage;
    case "session":
      return window2.sessionStorage;
  }
}
var _current, _key, _serializer, _storage, _subscribe2, _version, _handleStorageEvent, _PersistedState_instances, deserialize_fn, serialize_fn;
var PersistedState = class {
  constructor(key, initialValue, options = {}) {
    __privateAdd(this, _PersistedState_instances);
    __privateAdd(this, _current);
    __privateAdd(this, _key);
    __privateAdd(this, _serializer);
    __privateAdd(this, _storage);
    __privateAdd(this, _subscribe2);
    __privateAdd(this, _version, state(0));
    __privateAdd(this, _handleStorageEvent, (event) => {
      if (strict_equals(event.key, __privateGet(this, _key), false) || strict_equals(event.newValue, null)) return;
      __privateSet(this, _current, __privateMethod(this, _PersistedState_instances, deserialize_fn).call(this, event.newValue));
      set(__privateGet(this, _version), get(__privateGet(this, _version)) + 1);
    });
    const {
      storage: storageType = "local",
      serializer = {
        serialize: JSON.stringify,
        deserialize: JSON.parse
      },
      syncTabs = true,
      window: window2 = defaultWindow
    } = options;
    __privateSet(this, _current, initialValue);
    __privateSet(this, _key, key);
    __privateSet(this, _serializer, serializer);
    if (strict_equals(window2, void 0)) return;
    const storage = getStorage(storageType, window2);
    __privateSet(this, _storage, storage);
    const existingValue = storage.getItem(key);
    if (strict_equals(existingValue, null, false)) {
      __privateSet(this, _current, __privateMethod(this, _PersistedState_instances, deserialize_fn).call(this, existingValue));
    } else {
      __privateMethod(this, _PersistedState_instances, serialize_fn).call(this, initialValue);
    }
    if (syncTabs && strict_equals(storageType, "local")) {
      __privateSet(this, _subscribe2, createSubscriber(() => {
        return on(window2, "storage", __privateGet(this, _handleStorageEvent));
      }));
    }
  }
  get current() {
    var _a, _b;
    (_a = __privateGet(this, _subscribe2)) == null ? void 0 : _a.call(this);
    get(__privateGet(this, _version));
    const root = __privateMethod(this, _PersistedState_instances, deserialize_fn).call(this, (_b = __privateGet(this, _storage)) == null ? void 0 : _b.getItem(__privateGet(this, _key))) ?? __privateGet(this, _current);
    const proxies = /* @__PURE__ */ new WeakMap();
    const proxy2 = (value) => {
      if (strict_equals(value, null) || strict_equals(value == null ? void 0 : value.constructor.name, "Date") || strict_equals(typeof value, "object", false)) {
        return value;
      }
      let p = proxies.get(value);
      if (!p) {
        p = new Proxy(value, {
          get: (target, property) => {
            get(__privateGet(this, _version));
            return proxy2(Reflect.get(target, property));
          },
          set: (target, property, value2) => {
            set(__privateGet(this, _version), get(__privateGet(this, _version)) + 1);
            Reflect.set(target, property, value2);
            __privateMethod(this, _PersistedState_instances, serialize_fn).call(this, root);
            return true;
          }
        });
        proxies.set(value, p);
      }
      return p;
    };
    return proxy2(root);
  }
  set current(newValue) {
    __privateMethod(this, _PersistedState_instances, serialize_fn).call(this, newValue);
    set(__privateGet(this, _version), get(__privateGet(this, _version)) + 1);
  }
};
_current = new WeakMap();
_key = new WeakMap();
_serializer = new WeakMap();
_storage = new WeakMap();
_subscribe2 = new WeakMap();
_version = new WeakMap();
_handleStorageEvent = new WeakMap();
_PersistedState_instances = new WeakSet();
deserialize_fn = function(value) {
  try {
    return __privateGet(this, _serializer).deserialize(value);
  } catch (error) {
    console.error(...log_if_contains_state("error", `Error when parsing "${value}" from persisted store "${__privateGet(this, _key)}"`, error));
    return;
  }
};
serialize_fn = function(value) {
  var _a;
  try {
    if (equals(value, void 0, false)) {
      (_a = __privateGet(this, _storage)) == null ? void 0 : _a.setItem(__privateGet(this, _key), __privateGet(this, _serializer).serialize(value));
    }
  } catch (error) {
    console.error(...log_if_contains_state("error", `Error when writing value from persisted store "${__privateGet(this, _key)}" to ${__privateGet(this, _storage)}`, error));
  }
};

// node_modules/mode-watcher/node_modules/runed/dist/utilities/resource/resource.svelte.js
function debounce(fn, delay) {
  let timeoutId;
  let lastResolve = null;
  return (...args) => {
    return new Promise((resolve) => {
      if (lastResolve) {
        lastResolve(void 0);
      }
      lastResolve = resolve;
      clearTimeout(timeoutId);
      timeoutId = setTimeout(
        async () => {
          const result = await fn(...args);
          if (lastResolve) {
            lastResolve(result);
            lastResolve = null;
          }
        },
        delay
      );
    });
  };
}
function throttle(fn, delay) {
  let lastRun = 0;
  let lastPromise = null;
  return (...args) => {
    const now = Date.now();
    if (lastRun && now - lastRun < delay) {
      return lastPromise ?? Promise.resolve(void 0);
    }
    lastRun = now;
    lastPromise = fn(...args);
    return lastPromise;
  };
}
function runResource(source, fetcher, options = {}, effectFn) {
  const {
    lazy = false,
    once = false,
    initialValue,
    debounce: debounceTime,
    throttle: throttleTime
  } = options;
  let current = state(proxy(initialValue));
  let loading = state(false);
  let error = state(void 0);
  let cleanupFns = state(proxy([]));
  const runCleanup = () => {
    get(cleanupFns).forEach((fn) => fn());
    set(cleanupFns, [], true);
  };
  const onCleanup = (fn) => {
    set(cleanupFns, [...get(cleanupFns), fn], true);
  };
  const baseFetcher = async (value, previousValue, refetching = false) => {
    try {
      set(loading, true);
      set(error, void 0);
      runCleanup();
      const controller = new AbortController();
      onCleanup(() => controller.abort());
      const result = await fetcher(value, previousValue, {
        data: get(current),
        refetching,
        onCleanup,
        signal: controller.signal
      });
      set(current, result, true);
      return result;
    } catch (e) {
      if (!(e instanceof DOMException && strict_equals(e.name, "AbortError"))) {
        set(error, e, true);
      }
      return void 0;
    } finally {
      set(loading, false);
    }
  };
  const runFetcher = debounceTime ? debounce(baseFetcher, debounceTime) : throttleTime ? throttle(baseFetcher, throttleTime) : baseFetcher;
  const sources = Array.isArray(source) ? source : [source];
  let prevValues;
  effectFn(
    (values, previousValues) => {
      if (once && prevValues) {
        return;
      }
      if (prevValues && strict_equals(JSON.stringify(values), JSON.stringify(prevValues))) {
        return;
      }
      prevValues = values;
      runFetcher(Array.isArray(source) ? values : values[0], Array.isArray(source) ? previousValues : previousValues == null ? void 0 : previousValues[0]);
    },
    { lazy }
  );
  return {
    get current() {
      return get(current);
    },
    get loading() {
      return get(loading);
    },
    get error() {
      return get(error);
    },
    mutate: (value) => {
      set(current, value, true);
    },
    refetch: (info) => {
      const values = sources.map((s) => s());
      return runFetcher(Array.isArray(source) ? values : values[0], Array.isArray(source) ? values : values[0], info ?? true);
    }
  };
}
function resource(source, fetcher, options) {
  return runResource(source, fetcher, options, (fn, options2) => {
    const sources = Array.isArray(source) ? source : [source];
    const getters = () => sources.map((s) => s());
    watch(
      getters,
      (values, previousValues) => {
        fn(values, previousValues ?? []);
      },
      options2
    );
  });
}
function resourcePre(source, fetcher, options) {
  return runResource(source, fetcher, options, (fn, options2) => {
    const sources = Array.isArray(source) ? source : [source];
    const getter = () => sources.map((s) => s());
    watch.pre(
      getter,
      (values, previousValues) => {
        fn(values, previousValues ?? []);
      },
      options2
    );
  });
}
resource.pre = resourcePre;

// node_modules/mode-watcher/dist/utils.js
function sanitizeClassNames(classNames) {
  return classNames.filter((className) => className.length > 0);
}
var noopStorage = {
  getItem: (_key2) => null,
  setItem: (_key2, _value3) => {
  }
};
var isBrowser = typeof document !== "undefined";

// node_modules/svelte-toolbelt/dist/utils/is.js
function isFunction2(value) {
  return typeof value === "function";
}
function isObject(value) {
  return value !== null && typeof value === "object";
}

// node_modules/svelte-toolbelt/dist/box/box.svelte.js
var BoxSymbol = Symbol("box");
var isWritableSymbol = Symbol("is-writable");
function isBox(value) {
  return isObject(value) && BoxSymbol in value;
}
function isWritableBox(value) {
  return box.isBox(value) && isWritableSymbol in value;
}
function box(initialValue) {
  let current = state(proxy(initialValue));
  return {
    [BoxSymbol]: true,
    [isWritableSymbol]: true,
    get current() {
      return get(current);
    },
    set current(v) {
      set(current, v, true);
    }
  };
}
function boxWith(getter, setter) {
  const derived = user_derived(getter);
  if (setter) {
    return {
      [BoxSymbol]: true,
      [isWritableSymbol]: true,
      get current() {
        return get(derived);
      },
      set current(v) {
        setter(v);
      }
    };
  }
  return {
    [BoxSymbol]: true,
    get current() {
      return getter();
    }
  };
}
function boxFrom(value) {
  if (box.isBox(value)) return value;
  if (isFunction2(value)) return box.with(value);
  return box(value);
}
function boxFlatten(boxes) {
  return Object.entries(boxes).reduce(
    (acc, [key, b]) => {
      if (!box.isBox(b)) {
        return Object.assign(acc, { [key]: b });
      }
      if (box.isWritableBox(b)) {
        Object.defineProperty(acc, key, {
          get() {
            return b.current;
          },
          set(v) {
            b.current = v;
          }
        });
      } else {
        Object.defineProperty(acc, key, {
          get() {
            return b.current;
          }
        });
      }
      return acc;
    },
    {}
  );
}
function toReadonlyBox(b) {
  if (!box.isWritableBox(b)) return b;
  return {
    [BoxSymbol]: true,
    get current() {
      return b.current;
    }
  };
}
box.from = boxFrom;
box.with = boxWith;
box.flatten = boxFlatten;
box.readonly = toReadonlyBox;
box.isBox = isBox;
box.isWritableBox = isWritableBox;

// node_modules/style-to-object/esm/index.mjs
var import_cjs = __toESM(require_cjs(), 1);
var esm_default = import_cjs.default.default || import_cjs.default;

// node_modules/svelte-toolbelt/dist/utils/style-to-css.js
function createParser(matcher, replacer) {
  const regex = RegExp(matcher, "g");
  return (str) => {
    if (typeof str !== "string") {
      throw new TypeError(`expected an argument of type string, but got ${typeof str}`);
    }
    if (!str.match(regex))
      return str;
    return str.replace(regex, replacer);
  };
}
var camelToKebab = createParser(/[A-Z]/, (match) => `-${match.toLowerCase()}`);
function styleToCSS(styleObj) {
  if (!styleObj || typeof styleObj !== "object" || Array.isArray(styleObj)) {
    throw new TypeError(`expected an argument of type object, but got ${typeof styleObj}`);
  }
  return Object.keys(styleObj).map((property) => `${camelToKebab(property)}: ${styleObj[property]};`).join("\n");
}

// node_modules/svelte-toolbelt/dist/utils/style.js
function styleToString(style = {}) {
  return styleToCSS(style).replace("\n", " ");
}
var srOnlyStyles = {
  position: "absolute",
  width: "1px",
  height: "1px",
  padding: "0",
  margin: "-1px",
  overflow: "hidden",
  clip: "rect(0, 0, 0, 0)",
  whiteSpace: "nowrap",
  borderWidth: "0",
  transform: "translateX(-100%)"
};
var srOnlyStylesString = styleToString(srOnlyStyles);

// node_modules/runed/dist/internal/configurable-globals.js
var defaultWindow2 = true_default && typeof window !== "undefined" ? window : void 0;
var defaultDocument2 = true_default && typeof window !== "undefined" ? window.document : void 0;
var defaultNavigator2 = true_default && typeof window !== "undefined" ? window.navigator : void 0;
var defaultLocation2 = true_default && typeof window !== "undefined" ? window.location : void 0;

// node_modules/runed/dist/internal/utils/dom.js
function getActiveElement2(document2) {
  let activeElement3 = document2.activeElement;
  while (activeElement3 == null ? void 0 : activeElement3.shadowRoot) {
    const node = activeElement3.shadowRoot.activeElement;
    if (node === activeElement3)
      break;
    else
      activeElement3 = node;
  }
  return activeElement3;
}

// node_modules/runed/dist/utilities/active-element/active-element.svelte.js
var _document2, _subscribe3;
var ActiveElement2 = class {
  constructor(options = {}) {
    __privateAdd(this, _document2);
    __privateAdd(this, _subscribe3);
    const {
      window: window2 = defaultWindow2,
      document: document2 = window2 == null ? void 0 : window2.document
    } = options;
    if (strict_equals(window2, void 0)) return;
    __privateSet(this, _document2, document2);
    __privateSet(this, _subscribe3, createSubscriber((update) => {
      const cleanupFocusIn = on(window2, "focusin", update);
      const cleanupFocusOut = on(window2, "focusout", update);
      return () => {
        cleanupFocusIn();
        cleanupFocusOut();
      };
    }));
  }
  get current() {
    var _a;
    (_a = __privateGet(this, _subscribe3)) == null ? void 0 : _a.call(this);
    if (!__privateGet(this, _document2)) return null;
    return getActiveElement2(__privateGet(this, _document2));
  }
};
_document2 = new WeakMap();
_subscribe3 = new WeakMap();
var activeElement2 = new ActiveElement2();

// node_modules/runed/dist/utilities/watch/watch.svelte.js
function runEffect2(flush, effect) {
  switch (flush) {
    case "post":
      user_effect(effect);
      break;
    case "pre":
      user_pre_effect(effect);
      break;
  }
}
function runWatcher2(sources, flush, effect, options = {}) {
  const { lazy = false } = options;
  let active = !lazy;
  let previousValues = Array.isArray(sources) ? [] : void 0;
  runEffect2(flush, () => {
    const values = Array.isArray(sources) ? sources.map((source) => source()) : sources();
    if (!active) {
      active = true;
      previousValues = values;
      return;
    }
    const cleanup = untrack(() => effect(values, previousValues));
    previousValues = values;
    return cleanup;
  });
}
function runWatcherOnce2(sources, flush, effect) {
  const cleanupRoot = effect_root(() => {
    let stop = false;
    runWatcher2(
      sources,
      flush,
      (values, previousValues) => {
        if (stop) {
          cleanupRoot();
          return;
        }
        const cleanup = effect(values, previousValues);
        stop = true;
        return cleanup;
      },
      // Running the effect immediately just once makes no sense at all.
      // That's just `onMount` with extra steps.
      { lazy: true }
    );
  });
  user_effect(() => {
    return cleanupRoot;
  });
}
function watch2(sources, effect, options) {
  runWatcher2(sources, "post", effect, options);
}
function watchPre2(sources, effect, options) {
  runWatcher2(sources, "pre", effect, options);
}
watch2.pre = watchPre2;
function watchOnce2(source, effect) {
  runWatcherOnce2(source, "post", effect);
}
function watchOncePre2(source, effect) {
  runWatcherOnce2(source, "pre", effect);
}
watchOnce2.pre = watchOncePre2;

// node_modules/mode-watcher/dist/storage-keys.svelte.js
var modeStorageKey = box("mode-watcher-mode");
var themeStorageKey = box("mode-watcher-theme");

// node_modules/mode-watcher/dist/modes.js
var modes = ["dark", "light", "system"];
function isValidMode(value) {
  if (typeof value !== "string")
    return false;
  return modes.includes(value);
}

// node_modules/mode-watcher/dist/mode-states.svelte.js
var _defaultValue, _storage2, _initialValue, _value, _persisted, _UserPrefersMode_instances, makePersisted_fn;
var UserPrefersMode = class {
  constructor() {
    __privateAdd(this, _UserPrefersMode_instances);
    __privateAdd(this, _defaultValue, "system");
    __privateAdd(this, _storage2, isBrowser ? localStorage : noopStorage);
    __privateAdd(this, _initialValue, __privateGet(this, _storage2).getItem(modeStorageKey.current));
    __privateAdd(this, _value, isValidMode(__privateGet(this, _initialValue)) ? __privateGet(this, _initialValue) : __privateGet(this, _defaultValue));
    __privateAdd(this, _persisted, state(proxy(__privateMethod(this, _UserPrefersMode_instances, makePersisted_fn).call(this))));
    effect_root(() => {
      return watch.pre(() => modeStorageKey.current, (_, prevStorageKey) => {
        const currModeValue = get(__privateGet(this, _persisted)).current;
        set(__privateGet(this, _persisted), __privateMethod(this, _UserPrefersMode_instances, makePersisted_fn).call(this, currModeValue), true);
        if (prevStorageKey) {
          localStorage.removeItem(prevStorageKey);
        }
      });
    });
  }
  get current() {
    return get(__privateGet(this, _persisted)).current;
  }
  set current(newValue) {
    get(__privateGet(this, _persisted)).current = newValue;
  }
};
_defaultValue = new WeakMap();
_storage2 = new WeakMap();
_initialValue = new WeakMap();
_value = new WeakMap();
_persisted = new WeakMap();
_UserPrefersMode_instances = new WeakSet();
makePersisted_fn = function(value = __privateGet(this, _value)) {
  return new PersistedState(modeStorageKey.current, value, {
    serializer: {
      serialize: (v) => v,
      deserialize: (v) => {
        if (isValidMode(v)) return v;
        return __privateGet(this, _defaultValue);
      }
    }
  });
};
var _defaultValue2, _track, _current2, _mediaQueryState;
var SystemPrefersMode = class {
  constructor() {
    __privateAdd(this, _defaultValue2);
    __privateAdd(this, _track, true);
    __privateAdd(this, _current2, state(proxy(__privateGet(this, _defaultValue2))));
    __privateAdd(this, _mediaQueryState, strict_equals(typeof window, "undefined", false) && strict_equals(typeof window.matchMedia, "function") ? new MediaQuery("prefers-color-scheme: light") : { current: false });
    effect_root(() => {
      user_pre_effect(() => {
        if (!__privateGet(this, _track)) return;
        this.query();
      });
    });
    this.query = this.query.bind(this);
    this.tracking = this.tracking.bind(this);
  }
  query() {
    if (!isBrowser) return;
    set(__privateGet(this, _current2), __privateGet(this, _mediaQueryState).current ? "light" : "dark", true);
  }
  tracking(active) {
    __privateSet(this, _track, active);
  }
  get current() {
    return get(__privateGet(this, _current2));
  }
};
_defaultValue2 = new WeakMap();
_track = new WeakMap();
_current2 = new WeakMap();
_mediaQueryState = new WeakMap();
var userPrefersMode = new UserPrefersMode();
var systemPrefersMode = new SystemPrefersMode();

// node_modules/mode-watcher/dist/theme-state.svelte.js
var _storage3, _initialValue2, _value2, _persisted2, _CustomTheme_instances, makePersisted_fn2;
var CustomTheme = class {
  constructor() {
    __privateAdd(this, _CustomTheme_instances);
    __privateAdd(this, _storage3, isBrowser ? localStorage : noopStorage);
    __privateAdd(this, _initialValue2, __privateGet(this, _storage3).getItem(themeStorageKey.current));
    __privateAdd(this, _value2, strict_equals(__privateGet(this, _initialValue2), null) || strict_equals(__privateGet(this, _initialValue2), void 0) ? "" : __privateGet(this, _initialValue2));
    __privateAdd(this, _persisted2, state(proxy(__privateMethod(this, _CustomTheme_instances, makePersisted_fn2).call(this))));
    effect_root(() => {
      return watch.pre(() => themeStorageKey.current, (_, prevStorageKey) => {
        const currModeValue = get(__privateGet(this, _persisted2)).current;
        set(__privateGet(this, _persisted2), __privateMethod(this, _CustomTheme_instances, makePersisted_fn2).call(this, currModeValue), true);
        if (prevStorageKey) {
          localStorage.removeItem(prevStorageKey);
        }
      });
    });
  }
  /**
   * The current theme.
   * @returns The current theme.
   */
  get current() {
    return get(__privateGet(this, _persisted2)).current;
  }
  /**
   * Set the current theme.
   * @param newValue The new theme to set.
   */
  set current(newValue) {
    get(__privateGet(this, _persisted2)).current = newValue;
  }
};
_storage3 = new WeakMap();
_initialValue2 = new WeakMap();
_value2 = new WeakMap();
_persisted2 = new WeakMap();
_CustomTheme_instances = new WeakSet();
makePersisted_fn2 = function(value = __privateGet(this, _value2)) {
  return new PersistedState(themeStorageKey.current, value, {
    serializer: {
      serialize: (v) => {
        if (strict_equals(typeof v, "string", false)) return "";
        return v;
      },
      deserialize: (v) => v
    }
  });
};
var customTheme = new CustomTheme();

// node_modules/mode-watcher/dist/without-transition.js
var timeoutAction;
var timeoutEnable;
var hasLoaded = false;
function withoutTransition(action) {
  if (typeof document === "undefined")
    return;
  if (!hasLoaded) {
    hasLoaded = true;
    action();
    return;
  }
  clearTimeout(timeoutAction);
  clearTimeout(timeoutEnable);
  const style = document.createElement("style");
  const css = document.createTextNode(`* {
     -webkit-transition: none !important;
     -moz-transition: none !important;
     -o-transition: none !important;
     -ms-transition: none !important;
     transition: none !important;
  }`);
  style.appendChild(css);
  const disable = () => document.head.appendChild(style);
  const enable = () => document.head.removeChild(style);
  if (typeof window.getComputedStyle !== "undefined") {
    disable();
    action();
    window.getComputedStyle(style).opacity;
    enable();
    return;
  }
  if (typeof window.requestAnimationFrame !== "undefined") {
    disable();
    action();
    window.requestAnimationFrame(enable);
    return;
  }
  disable();
  timeoutAction = window.setTimeout(() => {
    action();
    timeoutEnable = window.setTimeout(enable, 120);
  }, 120);
}

// node_modules/mode-watcher/dist/states.svelte.js
var themeColors = box(void 0);
var disableTransitions = box(true);
var darkClassNames = box([]);
var lightClassNames = box([]);
function createDerivedMode() {
  const current = user_derived(() => {
    if (!isBrowser) return void 0;
    const derivedMode2 = strict_equals(userPrefersMode.current, "system") ? systemPrefersMode.current : userPrefersMode.current;
    const sanitizedDarkClassNames = sanitizeClassNames(darkClassNames.current);
    const sanitizedLightClassNames = sanitizeClassNames(lightClassNames.current);
    function update() {
      const htmlEl = document.documentElement;
      const themeColorEl = document.querySelector('meta[name="theme-color"]');
      if (strict_equals(derivedMode2, "light")) {
        if (sanitizedDarkClassNames.length) htmlEl.classList.remove(...sanitizedDarkClassNames);
        if (sanitizedLightClassNames.length) htmlEl.classList.add(...sanitizedLightClassNames);
        htmlEl.style.colorScheme = "light";
        if (themeColorEl && themeColors.current) {
          themeColorEl.setAttribute("content", themeColors.current.light);
        }
      } else {
        if (sanitizedLightClassNames.length) htmlEl.classList.remove(...sanitizedLightClassNames);
        if (sanitizedDarkClassNames.length) htmlEl.classList.add(...sanitizedDarkClassNames);
        htmlEl.style.colorScheme = "dark";
        if (themeColorEl && themeColors.current) {
          themeColorEl.setAttribute("content", themeColors.current.dark);
        }
      }
    }
    if (disableTransitions.current) {
      withoutTransition(update);
    } else {
      update();
    }
    return derivedMode2;
  });
  return {
    get current() {
      return get(current);
    }
  };
}
function createDerivedTheme() {
  const current = user_derived(() => {
    customTheme.current;
    if (!isBrowser) return void 0;
    function update() {
      const htmlEl = document.documentElement;
      htmlEl.setAttribute("data-theme", customTheme.current);
    }
    if (disableTransitions.current) {
      withoutTransition(update);
    } else {
      update();
    }
    return customTheme.current;
  });
  return {
    get current() {
      return get(current);
    }
  };
}
var derivedMode = createDerivedMode();
var derivedTheme = createDerivedTheme();

// node_modules/mode-watcher/dist/mode.js
function toggleMode() {
  userPrefersMode.current = derivedMode.current === "dark" ? "light" : "dark";
}
function setMode(mode) {
  userPrefersMode.current = mode;
}
function resetMode() {
  userPrefersMode.current = "system";
}
function setTheme(newTheme) {
  customTheme.current = newTheme;
}
function defineConfig(config) {
  return config;
}
function setInitialMode({ defaultMode = "system", themeColors: themeColors2, darkClassNames: darkClassNames2 = ["dark"], lightClassNames: lightClassNames2 = [], defaultTheme = "", modeStorageKey: modeStorageKey2 = "mode-watcher-mode", themeStorageKey: themeStorageKey2 = "mode-watcher-theme" }) {
  const rootEl = document.documentElement;
  const mode = localStorage.getItem(modeStorageKey2) ?? defaultMode;
  const theme = localStorage.getItem(themeStorageKey2) ?? defaultTheme;
  const light = mode === "light" || mode === "system" && window.matchMedia("(prefers-color-scheme: light)").matches;
  if (light) {
    if (darkClassNames2.length)
      rootEl.classList.remove(...darkClassNames2.filter(Boolean));
    if (lightClassNames2.length)
      rootEl.classList.add(...lightClassNames2.filter(Boolean));
  } else {
    if (lightClassNames2.length)
      rootEl.classList.remove(...lightClassNames2.filter(Boolean));
    if (darkClassNames2.length)
      rootEl.classList.add(...darkClassNames2.filter(Boolean));
  }
  rootEl.style.colorScheme = light ? "light" : "dark";
  if (themeColors2) {
    const themeMetaEl = document.querySelector('meta[name="theme-color"]');
    if (themeMetaEl) {
      themeMetaEl.setAttribute("content", mode === "light" ? themeColors2.light : themeColors2.dark);
    }
  }
  if (theme) {
    rootEl.setAttribute("data-theme", theme);
    localStorage.setItem(themeStorageKey2, theme);
  }
  localStorage.setItem(modeStorageKey2, mode);
}
function generateSetInitialModeExpression(config = {}) {
  return `(${setInitialMode.toString()})(${JSON.stringify(config)});`;
}
var createInitialModeExpression = generateSetInitialModeExpression;

// node_modules/mode-watcher/dist/components/mode-watcher-lite.svelte
Mode_watcher_lite[FILENAME] = "node_modules/mode-watcher/dist/components/mode-watcher-lite.svelte";
var root_1 = add_locations(template(`<meta name="theme-color">`), Mode_watcher_lite[FILENAME], [[11, 1]]);
function Mode_watcher_lite($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Mode_watcher_lite);
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var meta = root_1();
      template_effect(() => set_attribute(meta, "content", $$props.themeColors.dark));
      append($$anchor2, meta);
    };
    if_block(node, ($$render) => {
      if ($$props.themeColors) $$render(consequent);
    });
  }
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Mode_watcher_lite = hmr(Mode_watcher_lite, () => Mode_watcher_lite[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Mode_watcher_lite[HMR].source;
    set(Mode_watcher_lite[HMR].source, module.default[HMR].original);
  });
}
var mode_watcher_lite_default = Mode_watcher_lite;

// node_modules/mode-watcher/dist/components/mode-watcher-full.svelte
Mode_watcher_full[FILENAME] = "node_modules/mode-watcher/dist/components/mode-watcher-full.svelte";
var root_2 = add_locations(template(`<meta name="theme-color">`), Mode_watcher_full[FILENAME], [[21, 2]]);
var root_12 = add_locations(template(`<!> <!>`, 1), Mode_watcher_full[FILENAME], []);
function Mode_watcher_full($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Mode_watcher_full);
  let trueNonce = prop($$props, "trueNonce", 3, "");
  head(($$anchor2) => {
    var fragment = root_12();
    var node = first_child(fragment);
    {
      var consequent = ($$anchor3) => {
        var meta = root_2();
        template_effect(() => set_attribute(meta, "content", $$props.themeColors.dark));
        append($$anchor3, meta);
      };
      if_block(node, ($$render) => {
        if ($$props.themeColors) $$render(consequent);
      });
    }
    var node_1 = sibling(node, 2);
    html(node_1, () => `<script${trueNonce() ? ` nonce=${trueNonce()}` : ""}>(` + setInitialMode.toString() + `)(` + JSON.stringify($$props.initConfig) + `);<\/script>`, false, false, true);
    append($$anchor2, fragment);
  });
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Mode_watcher_full = hmr(Mode_watcher_full, () => Mode_watcher_full[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Mode_watcher_full[HMR].source;
    set(Mode_watcher_full[HMR].source, module.default[HMR].original);
  });
}
var mode_watcher_full_default = Mode_watcher_full;

// node_modules/mode-watcher/dist/components/mode-watcher.svelte
Mode_watcher[FILENAME] = "node_modules/mode-watcher/dist/components/mode-watcher.svelte";
function Mode_watcher($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Mode_watcher);
  let track = prop($$props, "track", 3, true), defaultMode = prop($$props, "defaultMode", 3, "system"), disableTransitionsProp = prop($$props, "disableTransitions", 3, true), darkClassNamesProp = prop($$props, "darkClassNames", 19, () => ["dark"]), lightClassNamesProp = prop($$props, "lightClassNames", 19, () => []), defaultTheme = prop($$props, "defaultTheme", 3, ""), nonce = prop($$props, "nonce", 3, ""), themeStorageKeyProp = prop($$props, "themeStorageKey", 3, "mode-watcher-theme"), modeStorageKeyProp = prop($$props, "modeStorageKey", 3, "mode-watcher-mode"), disableHeadScriptInjection = prop($$props, "disableHeadScriptInjection", 3, false);
  modeStorageKey.current = modeStorageKeyProp();
  themeStorageKey.current = themeStorageKeyProp();
  darkClassNames.current = darkClassNamesProp();
  lightClassNames.current = lightClassNamesProp();
  disableTransitions.current = disableTransitionsProp();
  themeColors.current = $$props.themeColors;
  user_pre_effect(() => {
    disableTransitions.current = disableTransitionsProp();
  });
  user_pre_effect(() => {
    themeColors.current = $$props.themeColors;
  });
  user_pre_effect(() => {
    darkClassNames.current = darkClassNamesProp();
  });
  user_pre_effect(() => {
    lightClassNames.current = lightClassNamesProp();
  });
  user_pre_effect(() => {
    modeStorageKey.current = modeStorageKeyProp();
  });
  user_pre_effect(() => {
    themeStorageKey.current = themeStorageKeyProp();
  });
  user_pre_effect(() => {
    derivedMode.current;
    modeStorageKey.current;
    themeStorageKey.current;
    derivedTheme.current;
  });
  onMount(() => {
    systemPrefersMode.tracking(track());
    systemPrefersMode.query();
    const localStorageMode = localStorage.getItem(modeStorageKey.current);
    setMode(isValidMode(localStorageMode) ? localStorageMode : defaultMode());
    const localStorageTheme = localStorage.getItem(themeStorageKey.current);
    setTheme(localStorageTheme || defaultTheme());
  });
  const initConfig = defineConfig({
    defaultMode: defaultMode(),
    themeColors: $$props.themeColors,
    darkClassNames: darkClassNamesProp(),
    lightClassNames: lightClassNamesProp(),
    defaultTheme: defaultTheme(),
    modeStorageKey: modeStorageKeyProp(),
    themeStorageKey: themeStorageKeyProp()
  });
  const trueNonce = user_derived(() => strict_equals(typeof window, "undefined") ? nonce() : "");
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      mode_watcher_lite_default(node_1, {
        get themeColors() {
          return themeColors.current;
        }
      });
      append($$anchor2, fragment_1);
    };
    var alternate = ($$anchor2) => {
      var fragment_2 = comment();
      var node_2 = first_child(fragment_2);
      mode_watcher_full_default(node_2, {
        get trueNonce() {
          return get(trueNonce);
        },
        initConfig,
        get themeColors() {
          return themeColors.current;
        }
      });
      append($$anchor2, fragment_2);
    };
    if_block(node, ($$render) => {
      if (disableHeadScriptInjection()) $$render(consequent);
      else $$render(alternate, false);
    });
  }
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Mode_watcher = hmr(Mode_watcher, () => Mode_watcher[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Mode_watcher[HMR].source;
    set(Mode_watcher[HMR].source, module.default[HMR].original);
  });
}
var mode_watcher_default = Mode_watcher;
export {
  mode_watcher_default as ModeWatcher,
  createInitialModeExpression,
  generateSetInitialModeExpression,
  derivedMode as mode,
  modeStorageKey,
  resetMode,
  setMode,
  setTheme,
  systemPrefersMode,
  derivedTheme as theme,
  themeStorageKey,
  toggleMode,
  userPrefersMode
};
//# sourceMappingURL=mode-watcher.js.map
