{"name": "runed", "version": "0.25.0", "type": "module", "svelte": "./dist/index.js", "types": "./dist/index.d.ts", "contributors": [{"name": "<PERSON>", "url": "https://thomasglopes.com"}, {"name": "<PERSON>", "url": "https://x.com/huntabyte"}], "repository": {"type": "git", "url": "https://github.com/svecosystem/runed", "directory": "packages/runed"}, "funding": ["https://github.com/sponsors/huntabyte", "https://github.com/sponsors/tglide"], "exports": {".": {"types": "./dist/index.d.ts", "svelte": "./dist/index.js"}}, "files": ["dist", "!dist/**/*.test.*", "!dist/**/*.spec.*"], "peerDependencies": {"svelte": "^5.7.0"}, "devDependencies": {"@sveltejs/kit": "^2.5.3", "@sveltejs/package": "^2.3.0", "@sveltejs/vite-plugin-svelte": "4.0.0-next.7", "@testing-library/dom": "^10.2.0", "@testing-library/jest-dom": "^6.4.6", "@testing-library/svelte": "^5.2.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.10.6", "@vitest/coverage-v8": "^1.5.1", "@vitest/ui": "^1.6.0", "jsdom": "^24.0.0", "msw": "^2.7.0", "publint": "^0.1.9", "resize-observer-polyfill": "^1.5.1", "svelte": "^5.11.0", "svelte-check": "^4.1.1", "tslib": "^2.4.1", "typescript": "^5.0.0", "vite": "^5.0.3", "vitest": "^1.5.1"}, "dependencies": {"esm-env": "^1.0.0"}, "scripts": {"dev": "pnpm sync && pnpm watch", "build": "pnpm package", "package": "svelte-kit sync && svelte-package && publint", "test": "vitest --run", "test:watch": "vitest --watch", "test:ui": "vitest --watch --ui", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "watch": "svelte-kit sync && svelte-package --watch"}}