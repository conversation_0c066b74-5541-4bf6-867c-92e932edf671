import {
  asClassComponent,
  createB<PERSON>bler,
  createClassComponent,
  handlers,
  nonpassive,
  once,
  passive,
  preventDefault,
  run,
  self,
  stopImmediatePropagation,
  stopPropagation,
  trusted
} from "./chunk-NTWKMJSY.js";
import "./chunk-2TCGLMOU.js";
import "./chunk-DM772GTT.js";
import "./chunk-RVAV4ZRS.js";
import "./chunk-NNIHVWYK.js";
export {
  asClassComponent,
  createBubbler,
  createClassComponent,
  handlers,
  nonpassive,
  once,
  passive,
  preventDefault,
  run,
  self,
  stopImmediatePropagation,
  stopPropagation,
  trusted
};
//# sourceMappingURL=svelte_legacy.js.map
